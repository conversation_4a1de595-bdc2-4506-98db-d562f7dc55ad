// src/mixins/performanceMixin.js
export default {
    data() {
        return {
            loadStartTime: 0,
            loadDuration: 0,
        }
    },
    onLoad() {
        // 🔥 只在开发环境执行性能监控
        if (process.env.NODE_ENV === 'development') {
            this.loadStartTime = this.getNow()
            // console.log(`[性能监控] ${this.$options.name || '未命名页面'} onLoad - 开始时间: ${this.loadStartTime}`)
        }
    },
    onReady() {
        // 🔥 只在开发环境执行性能监控
        if (process.env.NODE_ENV === 'development') {
            const endTime = this.getNow()
            this.loadDuration = Math.max(endTime - this.loadStartTime, 1)
            // console.log(`[性能监控] ${this.$options.name || '未命名页面'} onReady - 结束时间: ${endTime}, 耗时: ${this.loadDuration}ms`)
            this.$forceUpdate()
            this.logPerformance()
        }
    },
    methods: {
        getNow() {
            // 统一使用 Date.now() 确保时间计算的一致性
            return Date.now()
        },
        logPerformance() {
            // 🔥 只在开发环境打印性能日志
            if (process.env.NODE_ENV === 'development') {
                // console.log(`[页面性能] ${this.$options.name || '未命名页面'} 加载耗时: ${this.loadDuration.toFixed(2)}ms`)
            }
        }
    }
}