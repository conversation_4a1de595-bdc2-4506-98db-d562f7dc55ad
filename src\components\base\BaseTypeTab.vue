<template>
  <view class="tab-container">
    <!-- 固定的"全部"按钮 -->
    <view
      class="fixed-tab"
      :class="{ active: curTabIndex === 0 }"
      @click="selectTab(0)"
    >
      <text class="tab-text">全部</text>
      <view v-if="curTabIndex === 0" class="tab-bar"></view>
    </view>

    <!-- 可滚动的其他选项 -->
    <scroll-view
      ref="scrollView"
      class="scroll-tabs"
      scroll-x
      :show-scrollbar="false"
      :scroll-with-animation="true"
      :scroll-left="scrollLeft"
      enable-flex
    >
      <view class="scroll-content">
        <view
          v-for="(item, index) in scrollableTabList"
          :key="item.id"
          class="scroll-tab"
          :class="{ active: curTabIndex === index + 1 }"
          @click="selectTab(index + 1)"
        >
          <text class="tab-text">{{ item.name }}</text>
          <view v-if="curTabIndex === index + 1" class="tab-bar"></view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import BaseTabs from "@/components/base/BaseTabs.vue"
export default {
  name: "BaseTypeTabs",
  components: {
    BaseTabs,
  },
  props: {},
  data() {
    return {
      // current: 0,、
      tabList: [
        // 模拟数据，确保 tab 栏能正常显示
        // {
        //   id: 0,
        //   name: "全部",
        // },
        // {
        //   id: 1,
        //   name: "娃娃机",
        // },
        // {
        //   id: 2,
        //   name: "游戏机",
        // },
        // {
        //   id: 3,
        //   name: "充电设备",
        // },
        // {
        //   id: 4,
        //   name: "售货机",
        // },
        // {
        //   id: 5,
        //   name: "饮水机",
        // },
        // {
        //   id: 6,
        //   name: "吃火鸡",
        // },
        // {
        //   id: 7,
        //   name: "冰箱机",
        // },
        // {
        //   id: 8,
        //   name: "洗衣机",
        // },
      ],
      curTabIndex: 0,
      scrollLeft: 0,
    }
  },
  computed: {
    // 可滚动的选项列表（除了"全部"）
    scrollableTabList() {
      return this.tabList.filter((item) => item.id !== 0)
    },
  },
  methods: {
    // 选择 tab
    selectTab(index) {
      const oldIndex = this.curTabIndex
      this.curTabIndex = index

      // 如果选择的是可滚动区域的选项，且索引发生了变化，才进行滚动
      if (index > 0 && index !== oldIndex) {
        this.scrollToTab(index - 1) // index - 1 因为可滚动列表不包含"全部"
      }

      this.tabChange(index)
    },

    // 自动滚动到指定的 tab
    scrollToTab(scrollableIndex) {
      const tabWidth = 120 // 估算每个tab的宽度
      const totalTabs = this.scrollableTabList.length

      let targetScrollLeft = 0

      if (scrollableIndex <= 1) {
        // 选择前面的项，滚动到最左边
        targetScrollLeft = 0
      } else if (scrollableIndex >= totalTabs - 2) {
        // 选择最后几个项，滚动到能看到最后几个
        const maxScrollLeft = Math.max(0, (totalTabs - 3) * tabWidth)
        targetScrollLeft = maxScrollLeft
      } else {
        // 选择中间的项，居中显示
        targetScrollLeft = Math.max(0, (scrollableIndex - 1) * tabWidth)
      }

      this.scrollLeft = targetScrollLeft
    },

    async getProductList() {
      try {
        const res = await this.$u.api.getProduct()

        // 确保数据结构一致性
        const formattedData = res.map((item, index) => ({
          id: item.id || index + 1,
          name: item.name || item.title || item.label || `选项${index + 1}`,
        }))

        this.tabList = [
          {
            id: 0,
            name: "全部",
          },
          ...formattedData,
        ]
      } catch (err) {
        // API 请求失败时保持模拟数据
        console.error("获取产品列表失败:", err)
      }
    },
    tabChange(e) {
      this.curTabIndex = e
      // console.log('切换tab', e,this.tabList[e])

      this.$emit("change", this.tabList[e])
    },
  },
  //组件加载完成
  mounted() {
    this.getProductList()
  },
}
</script>

<style lang="scss" scoped>
.tab-container {
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 1rpx solid #e5e5e5;
  height: 80rpx;
}

.fixed-tab {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  padding: 0 30rpx;
  background: #fff;
  flex-shrink: 0;
  box-shadow: 4rpx 0 20rpx rgba(99, 99, 99, 0.12);

  &.active {
    .tab-text {
      color: #0eade2;
    }
  }
}

.scroll-tabs {
  flex: 1;
  height: 80rpx;
  white-space: nowrap;
  overflow-x: auto;
}

.scroll-content {
  display: flex;
  align-items: center;
  height: 80rpx;
  width: max-content;
}

.scroll-tab {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  padding: 0 30rpx;
  flex-shrink: 0;

  &.active {
    .tab-text {
      color: #0eade2;
    }
  }
}

.tab-text {
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;
}

.tab-bar {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #0eade2;
  border-radius: 2rpx;
}

.baseTab {
  width: 100%;
  flex: 1;
}
</style>
