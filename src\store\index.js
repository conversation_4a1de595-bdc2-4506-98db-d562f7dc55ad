import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)
let vTabbars = [
    {
        iconPath: "/static/img/icon/home.png",
        selectedIconPath: "/static/img/icon/home_act.png",
        text: '首页',
        customIcon: false,
        midButton: false,
        pagePath: '/pages/index/index',
    },
    {
        iconPath: "/static/img/icon/scan.png",
        selectedIconPath: "/static/img/icon/scan.png",
        text: '扫一扫',
        midButton: true,
        customIcon: false,
        pagePath: ''
    },
    {
        iconPath: "/static/img/icon/my.png",
        selectedIconPath: "/static/img/icon/my_act.png",
        text: '我的',
        customIcon: false,
        midButton: false,
        pagePath: '/pages/index/Personal',
    }
]

//系统列表
let sysList = [

    {
        value: "0",
        label: "汉骑士",
        extra: "https://ppj.handaiwulian.com",
    },
    // {
    //     value: "0",
    //     label: "汉骑士",
    //     extra: "https://ppj2.handaiwulian.cn",
    // },
    // {
    //     value: "0",
    //     label: "汉袋物联",
    //     extra: "https://hd.handaiwulian.com",
    // },
    // {
    //     value: "1",
    //     label: "原有袋平台",
    //     extra: "https://youdai.wrlsw.com",
    // },
    // {
    //     index: 1,
    //     value: "1",
    //     label: "口罩机云平台",
    //     extra: "https://kz.51xhkj.com",
    // },
    // {
    //     index: 2,
    //     value: "2",
    //     label: "汉袋物联",
    //     extra: "https://hd.handaiwulian.com",
    // },
]
let lifeData = {};

//用户列表
try {
    // 尝试获取本地是否存在lifeData变量，第一次启动APP时是不存在的
    lifeData = uni.getStorageSync('lifeData');
} catch (e) {

}

// 需要永久存储，且下次APP启动需要取出的，在state中的变量名
let saveStateKeys = ['vName','vSelectSysObj', 'vUserInfo', 'sysList','vGamingTime',' vPointUser','vInputPermissions','vButtonPermissions','vButtonPermis','vButtonPermisTwo','vButtonPermisThree','vButtonPermisFour','vButtonPermisAides','vInputDisable','vSiteConfig','vServerList',"vRoleList",'vLoginList'];

// 保存变量到本地存储中
const saveLifeData = function (key, value) {
    // 判断变量名是否在需要存储的数组中
    if (saveStateKeys.indexOf(key) != -1) {
        // 获取本地存储的lifeData对象，将变量添加到对象中
        let tmp = uni.getStorageSync('lifeData');
        // 第一次打开APP，不存在lifeData变量，故放一个{}空对象
        tmp = tmp ? tmp : {};
        tmp[key] = value;
        // 执行这一步后，所有需要存储的变量，都挂载在本地的lifeData对象中
        uni.setStorageSync('lifeData', tmp);
    }
}



const store = new Vuex.Store({
    // 下面这些值仅为示例，使用过程中请删除
    state: {
        // 如果上面从本地获取的lifeData对象下有对应的属性，就赋值给state中对应的变量
        // 加上vuex_前缀，是防止变量名冲突，也让人一目了然
        // vuex_user: lifeData.vuex_user ? lifeData.vuex_user : {name: '明月'},
        // vuex_token: lifeData.vuex_token ? lifeData.vuex_token : '',
        // 如果vuex_version无需保存到本地永久存储，无需lifeData.vuex_version方式
        // vuex_version: '1.0.1',
       
        vIphoneXBottomHeight: 0,//iphonex 底部安全区域高度
        vSelectSysObj: lifeData.vSelectSysObj?.label ? lifeData.vSelectSysObj : sysList[0],//当前选择的系统 保存到本地。没有选择默认syslist（系统列表第一个）
        vUserInfo: lifeData.vUserInfo || {},//用户信息
        // vUserInfo: vUserList[0] || {},//用户信息
        sysList:sysList||[],//系统列表
        vToken: lifeData.vUserInfo?.token,//用户Token
        // vToken: vUserList[0]?.token,//用户Token
        vTabbars:vTabbars||[],
        vTel: lifeData.vTel,//客服热线
        vPointName: lifeData.vPointName||'场地',//场地方|酒店
        vCargoLanes:'套餐',//套餐
        vPointUser:lifeData.vPointUser||{},//上次缓存商户信息
        vDefaultIcon: require('@/static/img/errorLoad.png'),
        vStatusBarHeight: 0,//状态栏高度rpx
        vNavBarHeight: 0,//导航栏高度
        // vSiteConfig: {site_info: {
        //     // 正确初始化 site_info 或根据需要设置初始值
        //   }},//网站配置信息
        vSiteConfig:lifeData.vSiteConfig,
        vAppId: '',//Appid
        vNoticeNum: 0,//通知未读数量
        vTaskType: [],//任务类型
        vPayOpenid: '',//支付openid
        vIsCharge: false,//是否是充电管理后台？ true是
        vMiniCode: '',//小程序码
        vGamingTime:'', // 一升多少分钟
        vUserList: uni.getStorageSync("vUserList") ? JSON.parse(uni.getStorageSync("vUserList")):[],//用户列表
        vInputPermissions:lifeData.vInputPermissions,//输入权限>5&&!12
        vButtonPermissions:lifeData.vButtonPermissions,//按钮显示权限<5||==12
        vButtonPermis:lifeData.vButtonPermis,//按钮显示//!=6&&!=8
        vButtonPermisTwo:lifeData.vButtonPermisTwo,//按钮显示<8&&!=6||==12
        vButtonPermisThree:lifeData.vButtonPermisThree,//按钮显示<8&&!=6||==12
        vButtonPermisFour:lifeData.vButtonPermisFour,//按钮显示<5||==12
        vButtonPermisAides:lifeData.vButtonPermisAides,//助手展示 !=12
        vInputDisable:lifeData.vInputDisable,//按钮显示 id>5&&id!=12&&id!=7
        vTime:2,//禁止启动时常
        vCurrPage:{
            isDoRefresh:false,
            goodsInfo:[],
            isGetGoodsInfo:false,
            item:{},
            ad_select_item:{},
            ad_select_device:[]
        },
        vServerList:{
            device:lifeData?.vServerList?.device||[],
            analysisList:lifeData?.vServerList?.analysisList||[],
            advertsMange:lifeData?.vServerList?.advertsMange||[],
            cloudcollege:lifeData?.vServerList?.cloudcollege||[],
            deviceAdverts:lifeData?.vServerList?.deviceAdverts||[],
            equipmentsSelect:lifeData?.vServerList?.equipmentsSelect||[],
            goodsList:lifeData?.vServerList?.goodsList||[],
            ChargeOrder:lifeData?.vServerList?.ChargeOrder||[],
            orderList:lifeData?.vServerList?.orderList||[],
            placeList:lifeData?.vServerList?.placeList||[],
            selectplace:lifeData?.vServerList?.selectplace||[],
            replenishList:lifeData?.vServerList?.replenishList||[],
            selectUser:lifeData?.vServerList?.selectUser||[],
            userList:lifeData?.vServerList?.userList||[],
            dashboards:lifeData?.vServerList?.dashboards||[],
            dashboardsTwo:lifeData?.vServerList?.dashboardsTwo||[],
            renwalselect:lifeData?.vServerList?.renwalselect||[],
        },
        vRoleList:lifeData?.vRoleList||[],
        vName:lifeData?.vName||{
            use_login:'',
            vsName:'',
            devsice_sn:'',
        },
        vLoginList:lifeData?.vLoginList||{userName:'',password:''}
    },
    mutations: {
        $uStore(state, payload) {
            // 判断是否多层级调用，state中为对象存在的情况，诸如user.info.score = 1
            let nameArr = payload.name.split('.');
            let saveKey = '';
            let len = nameArr.length;
            if (nameArr.length >= 2) {
                let obj = state[nameArr[0]];
                for (let i = 1; i < len - 1; i++) {
                    obj = obj[nameArr[i]];
                }
                obj[nameArr[len - 1]] = payload.value;
                saveKey = nameArr[0];
            } else {
                // 单层级变量，在state就是一个普通变量的情况
                state[payload.name] = payload.value;
                saveKey = payload.name;
            }
            // 保存变量到本地，见顶部函数定义
            saveLifeData(saveKey, state[saveKey])
        },

        // 添加用户
        addItem(state, item) {
            // console.log(state.vUserList, item)
            state.vUserList.unshift(item)
            if (state.vUserList.length > 0) {
                for (let i = 1; i < state.vUserList.length; i++) {
                    if (state.vUserList[i].userName == item.userName) {
                        state.vUserList.splice(i, 1)
                    }

                }

            }

            if (state.vUserList.length > 5) {
                state.vUserList.pop()
            }
        },
        // 更新用户
        updateItem(state, item) {
            state.vUserList.unshift(item)
            for (let i = 1; i < state.vUserList.length; i++) {
                if (state.vUserList[i].userName == item.userName) {
                    state.vUserList.splice(i, 1)
                }
            }



        },
        // 删除用户
        deleteItem(state, item) {
            for (let i = 1; i < state.vUserList.length; i++) {
                if (state.vUserList[i].userName == item.userName) {
                    state.vUserList.splice(i, 1)
                }
            }
        },


    },
})
store.subscribe((mutation, state) => {
    if (['setList', 'addItem', 'updateItem', 'deleteItem'].includes(mutation.type)) {
        // 不设置定时删除
        uni.setStorageSync('vUserList', JSON.stringify(state.vUserList));

    }
})


export default store