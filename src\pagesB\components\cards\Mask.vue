<template>
    <view class="zhe" v-if="show" @click="clear">
        <view class="zhe-chunk">
            <image @click.stop="goImg" show-menu-by-longpress  class="image" :src="src" />
            <view>长按保存图片</view>
        </view>
     
    </view>
   
</template>

<script>
export default {
    name:'Mask',
    props: {
        src: {
            type: String,
            default: ''
        },
        isShow: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            show: false,
        }
    },
    methods: {
        clear(){
            this.$emit('clear')
        },
        goImg() {
            // this.$emit('goImg')
        }
    },

    watch: {
        isShow(val) {
            console.log('isShow值变化',val)
            this.show = val
          
        }
    },

}
</script>

<style scoped lang="scss">

.zhe{
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999999;
    background-color: #c5c5c586;
    display: flex;
    justify-content: center;
    align-items: center;
}
.zhe-chunk {
    width: 500rpx;
    height: 550rpx;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    // border-radius: 20rpx;
    .image{
        width: 500rpx;
        height: 500rpx;
    }
}
</style>