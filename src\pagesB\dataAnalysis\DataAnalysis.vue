<template>
  <view>
    <BaseNavbar :title="title" />
    <PerformanceMonitor :duration="loadDuration" />

    <view>
      <BaseSearch
        :placeholder="placeholderName"
        @search="search"
        typeImg="screen"
        @onClickIcon="onClickIcon"
        listType="analysisList"
        :hotelName="hotelName"
      />
    </view>
    <!-- <BaseList listType="analysisList" @searchChange="searchChange" /> -->
    <view class="subsection" @click="handleSubsection">
      <u-subsection
        mode="subsection"
        :active-color="mainColor"
        :list="subsectionList"
        :current="current"
        @change="changeSubsection"
      />
      <u-calendar
        v-model="showCalendar"
        mode="range"
        :safe-area-inset-bottom="true"
        btn-type="error"
        :range-color="mainColor"
        :active-bg-color="mainColor"
        :change-year="false"
        @change="changeCalendar"
      ></u-calendar>
    </view>
    <view class="profit">
      <view class="profit-title">
        周期：{{ dateTime.startDate }} 至 {{ dateTime.endDate }}
      </view>
      <UCharts :lineData="lineData" :chartsName="chartsName" v-show="show" />
    </view>
    <view class="info-card">
      <view
        class="info-card-title"
        :style="{ marginBottom: loadingType === 3 ? '20rpx' : 0 }"
      >
        <text class="text">{{ vName.use_login || "" }}</text>
        {{
          vName.vsName
            ? vName.use_login
              ? "->" + vName.vsName
              : vName.vsName
            : ""
        }}
        {{ vName.devsice_sn ? "->" + vName.devsice_sn : "" }}
        {{ titleName }}：{{ total }}
      </view>
      <ComList :loadingType="loadingType" v-if="loadingType !== 3">
        <block v-if="cardType === 'device'">
          <ChargeOrderCard
            v-for="item in listData"
            :key="item.id"
            :info="item"
            :linkmanDianwei="linkmanDianwei"
          />
        </block>
        <block v-else>
          <DataAnalysisCard
            v-for="item in listData"
            :key="item.id"
            :startDate="startDate"
            :endDate="endDate"
            :index="current"
            :info="item"
            :cardType="cardType"
            :linkmanDianwei="linkmanDianwei"
          />
        </block>
      </ComList>
      <view class="info-card-box" v-else>
        <BaseEmpty top="0" />
      </view>
    </view>
    <BaseBackTop
      @onPageScroll="onPageScroll"
      :scrollTop="scrollTop"
    ></BaseBackTop>
    <BasePopup :show.sync="isShowPopup" mode="top" :customStyle="customStyle">
      <PupSerch
        @selectHotel="selectHotel"
        :linkmanDianwei="linkmanDianwei"
        @confirm="pupConfirm"
        @resetData="resetData"
        :type="cardType"
      >
      </PupSerch>
    </BasePopup>
  </view>
</template>
<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue"
import BaseSearch from "@/components/base/BaseSearch.vue"
import UCharts from "./components/UCharts.vue"
import BaseEmpty from "../../components/base/BaseEmpty.vue"
import ComList from "@/components/list/ComList.vue"
import myPull from "../../mixins/myPull"
import ChargeOrderCard from "../components/cards/ChargeOrderCard.vue"
import DataAnalysisCard from "../components/cards/DataAnalysisCard.vue"
import { subtractDaysAndFormat } from "@/wxutil/times"
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from "@/wxutil/list"
import BaseBackTop from "@/components/base/BaseBackTop.vue"
import BasePopup from "@/components/base/BasePopup.vue"
import PupSerch from "./components/PupSerch.vue"
import PerformanceMonitor from "@/components/common/PerformanceMonitor .vue"
import performanceMixin from "@/mixins/performanceMixin"
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    UCharts,
    BaseEmpty,
    ComList,
    ChargeOrderCard,
    DataAnalysisCard,
    // BaseList,
    BaseBackTop,
    BasePopup,
    PupSerch,
    PerformanceMonitor,
  },
  mixins: [myPull(), performanceMixin],

  data() {
    return {
      scrollTop: 0,
      subsectionList: [
        //     {
        //     name: '昨天'
        // },
        {
          name: "今天",
          status: 1,
        },
        {
          status: 2,
          name: "昨天",
        },

        {
          name: "前天",
          status: 3,
        },
        {
          name: "本月",
          status: 4,
        },

        {
          name: "上月",
          status: 5,
        },
        {
          name: "自定义",
          isCustom: true,
        },
      ],
      current: 0,
      showCalendar: false,
      lineData: [],
      mainColor: "#fa3534",
      dateTime: {
        //显示时间
        startDate: "",
        endDate: "",
      },
      startDate: "", //查询开始时间
      endDate: "", //查询结束时间
      queryDate: 7, //查询天数
      cardType: "user", //user->用户数据，hotel->场地方数据，device->设备数据
      hotel_id: undefined,
      device_sn: "",
      uid: "",
      total: 0, //总数量
      searchVal: "",
      hotelName: "",
      title: "汉骑士销量统计",
      isShowPopup: false,
      linkmanDianwei: "",
      dianwei: "",
      show: true,
      serchHotelName: "",
      typeImg: "screen",
      customStyle: {
        top: 110 + this.vStatusBarHeight + this.vNavBarHeight + "rpx",
      },
    }
  },
  computed: {
    placeholderName() {
      if (this.cardType == "user") {
        // return `请输入${this.isExamine ? "通过" : "拒绝"}原因`;
        return `请输入${this.vPointName}名称`
      } else if (this.cardType == "hotel") {
        return "请输入设备编号"
      } else if (this.cardType == "device") {
        return "请输入订单编号"
      } else if (this.cardType == "manage") {
        return "请输入用户名"
      }
      return "请输入提示"
    },
    titleName() {
      if (this.cardType == "user") {
        // return `请输入${this.isExamine ? "通过" : "拒绝"}原因`;
        return `${this.vPointName}数量`
      } else if (this.cardType == "hotel") {
        return "设备数量"
      } else if (this.cardType == "device") {
        return "订单数量"
      } else if (this.cardType == "manage") {
        return "用户数量"
      }
    },
    chartsName() {
      if (this.cardType == "user") {
        // return `请输入${this.isExamine ? "通过" : "拒绝"}原因`;
        return "我的销售额"
      } else if (this.cardType == "hotel") {
        return `${this.vPointName}销售额`
      } else if (this.cardType == "device") {
        return "设备销售额"
      }
    },
  },
  methods: {
    resetData() {
      this.dianwei = ""
      this.linkmanDianwei = ""
      this.serchHotelName = ""
      let data = {
        use_login: "",
        vsName: "",
        devsice_sn: "",
      }
      if (this.linkmanDianwei) {
        this.$u.vuex("vName", { ...data, use_login: this.linkmanDianwei })
      }
    },
    pupConfirm(obj) {
      this.searchVal = obj.hotelName
      this.serchHotelName = obj.hotelName
      this.uid = this.dianwei
      this.onClickIcon()
      // this.show=true
      this.refresh()
      if (this.linkmanDianwei) {
        this.$u.vuex("vName", { ...this.vName, use_login: this.linkmanDianwei })
      } else {
        this.$u.vuex("vName", { ...this.vName, use_login: "" })
      }
    },
    selectHotel() {
      uni.navigateTo({
        url: `/pagesB/place/SelectPlace?from=dataAnalsis`,
      })
    },
    onClickIcon() {
      this.isShowPopup = !this.isShowPopup
      // this.show=false
    },
    onPageScroll(e) {
      if (e.scrollTop) {
        this.scrollTop = e.scrollTop
      }
    },
    handleSubsection() {
      if (this.current === 5 && !this.showCalendar) this.showCalendar = true
    },
    handleData(date, startDate, endDate) {
      // this.queryDate = date
      this.startDate = startDate
      this.endDate = endDate
      this.refresh()
    },
    changeSubsection(i) {
      this.current = i
      let selectItem = this.subsectionList[i]
      if (selectItem.isCustom) {
        this.showCalendar = true
      } else {
        let date = new Date()
        if (selectItem.status < 4) {
          this.startDate = this.endDate = subtractDaysAndFormat(
            selectItem.status - 1
          )
        } else if (selectItem.status == 4) {
          this.endDate =
            date.getFullYear() +
            "-" +
            (date.getMonth() + 1) +
            "-" +
            date.getDate()
          this.startDate =
            date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + 1
        } else if (selectItem.status == 5) {
          let currentDate = new Date()
          let lastMonthDate = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth(),
            0
          )

          this.startDate =
            lastMonthDate.getFullYear() +
            "-" +
            (lastMonthDate.getMonth() + 1) +
            "-1"
          this.endDate =
            lastMonthDate.getFullYear() +
            "-" +
            (lastMonthDate.getMonth() + 1) +
            "-" +
            lastMonthDate.getDate()
        }
        this.handleData("", this.startDate, this.endDate)
      }
    },
    async getList(page, done) {
      try {
        let data = {
          page,
          limit: 10,
          type: this.cardType,
          date: this.queryDate,
          start_time: this.startDate,
          end_time: this.endDate,
          keyword: this.searchVal,
        }

        if (this.cardType === "user") {
          data["uid"] = this.uid
          if (this.hotel_id) {
            data["hotel_id"] = this.hotel_id
          }
        } else if (this.cardType === "hotel") {
          data["hotel_id"] = this.hotel_id
        } else if (this.cardType === "device") {
          data["device_sn"] = this.device_sn
        } else if (this.cardType === "manage") {
        }

        let res = await this.$u.api.getUserReport(data)
        if (page == 1) {
          // 第一页的时候，才传递总数
          this.lineData = res.table
          this.dateTime = {
            startDate: this.lineData[0]?.time_point,
            endDate: this.lineData[this.lineData?.length - 1]?.time_point,
          }
          this.total = res.total ?? 0
        }
        done(res.data)
      } catch (error) {
        console.log("错误信息", error)
      }
    },

    changeCalendar(e) {
      let { startDate, endDate } = e
      this.handleData("", startDate, endDate)
    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(val) {
      this.searchVal = val
      let list = AddValueInObject(this.vServerList.analysisList, val)
      this.$u.vuex(`vServerList.analysisList`, list)
      this.refresh()
    },
  },
  onLoad(opt) {
    this.cardType = opt?.cardType ?? "user"
    this.hotel_id = opt?.hotel_id ?? undefined
    this.device_sn = opt?.device_sn ?? undefined
    this.uid = opt?.uid ?? undefined
    this.linkmanDianwei = opt?.linkmanDianwei ?? ""
    this.serchHotelName = opt?.serchHotelName ?? ""
    this.current = opt?.index ?? 0
    if (opt.from == "placeList") {
      this.cardType = "user"
      this.searchVal = opt?.hotelName
      this.hotelName = opt?.hotelName
      this.hotel_id = opt?.hotel_id
    }
    this.customStyle = {
      top: 110 + this.vStatusBarHeight + this.vNavBarHeight + "rpx",
    }
    if (this.current == 5) {
      this.endDate = opt?.endDate
      this.startDate = opt?.startDate
      this.handleData("", this.startDate, this.endDate)
    } else {
      this.changeSubsection(this.current)
    }
  },
  onShow() {
    // 获取当前小程序的页面栈
    let pages = getCurrentPages()
    // 数组中索引最大的页面--当前页面
    let currentPage = pages[pages.length - 1]
    // 打印出当前页面中的 options
    //正常打印出 options 值
    let opt = currentPage.options
    let data = {
      use_login: "",
      vsName: "",
      devsice_sn: "",
    }

    /*  #ifndef H5 */
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.item) {
      // 有值
      // 修改listData中值
      this.checkHotel = currPage.data.item
      // this.linkman = this.checkHotel.linkman;
      this.linkmanDianwei = this.checkHotel.user_login
      this.dianwei = this.checkHotel.id
    }
    /*#endif */
    /*  #ifdef H5 */
    this.checkHotel = this.vCurrPage.item
    // this.linkman = this.checkHotel.linkman;
    this.linkmanDianwei = this.checkHotel.user_login
    this.dianwei = this.checkHotel.id
    /*#endif */
    if (!opt.cardType || opt.cardType == "manage") {
      this.$u.vuex("vName", {
        ...data,
      })
    } else if (opt.cardType == "user") {
      this.$u.vuex("vName", { ...data, use_login: opt.userName })
    } else if (opt.cardType == "hotel") {
      this.$u.vuex("vName", {
        ...this.vName,
        vsName: opt.userName,
        devsice_sn: "",
      })
    } else if (opt.cardType == "device") {
      this.$u.vuex("vName", { ...this.vName, devsice_sn: opt.userName })
    } else {
      if (this.linkmanDianwei) {
        this.$u.vuex("vName", { ...data, use_login: this.linkmanDianwei })
      } else {
        this.$u.vuex("vName", { ...data })
      }
    }
    // if(opt.cardType!='user'&&opt.cardType){
    //   this.typeImg=''
    // }
  },
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
.subsection {
  background-color: #fff;
  padding: 20rpx;
}

.profit {
  &-title {
    padding: 20rpx;
    font-size: $font-size-middle;
    color: $textDarkGray;
  }
}

.text {
  text-decoration: underline;
}

.info-card {
  &-title {
    padding: 20rpx;
    padding-bottom: 0;
    font-size: $font-size-middle;
    color: $textBlack;
    font-weight: 700;
  }

  &-box {
    padding: 20rpx;
    background-color: #fff;
  }
}
</style>
