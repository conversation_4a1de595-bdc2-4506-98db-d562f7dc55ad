<template>
  <view>
    <BaseNavbar :title="title" />
    <PerformanceMonitor :duration="loadDuration" />

    <view class="info">
      <view class="panel" v-if="!userList">
        <image class="panel_bg" src="@/pagesB/static/img/financeBg.png" />
        <view class="panel_content">
          <view class="panel_content_top flexRowBetween">
            <view>
              当前余额
              <BaseCountTo
                class="balance"
                :value="balance"
                :size="40"
                :decimals="2"
              />
              元
            </view>
            <view>已提现：{{ withdrawalBalance.toFixed(2) }}元</view>
            <!-- <BaseIcon class="rule" name="info-circle" color="#fff" /> -->
          </view>
          <view class="panel_content_bottom flexRowAllCenter">
            <view class="withdrawal" @click="goWithdrawal">立即提现</view>
            <view class="record flexRowAllCenter" @click="goWithdrawalRecord">
              提现记录
            </view>
          </view>
        </view>
      </view>
      <view class="income" v-if="!userList">
        <view class="title"
          >流水明细 <text class="list_m"> 总记录：{{ totalNum }}条 </text>
          <text class="list_m">
            {{ myselectIndex == 0 ? "(我的)" : "(我的下级)" }} 总交易额：{{
              totaAmount
            }}元
          </text>
        </view>
        <!-- <view class="screen flexRowBetween" v-if="false">
          <view class="flexRowAllCenter">
            <view>全部(包含下级)</view>
            <view></view>
          </view>
          <view class="total">总收益：{{ totalIncome.toFixed(2) }}元</view>
        </view> -->
      </view>

      <view class="subsection" @click="handleSubsection">
        <u-subsection
          v-show="vButtonPermisAides && vButtonPermissions && !userList"
          mode="subsection"
          :active-color="myMainColor"
          :list="myselectList"
          :current="myselectIndex"
          @change="changeMyselect"
        ></u-subsection>
        <u-subsection
          mode="subsection"
          :active-color="mainColor"
          :list="subsectionList"
          :current="current"
          @change="changeSubsection"
        />
        <u-calendar
          v-model="showCalendar"
          mode="range"
          :safe-area-inset-bottom="true"
          btn-type="error"
          :range-color="mainColor"
          :active-bg-color="mainColor"
          :change-year="false"
          @change="changeCalendar"
        ></u-calendar>
      </view>
      <view class="profit">
        <view class="profit-title">周期：{{ startDate }} 至 {{ endDate }}</view>
        <UCharts
          v-if="userList"
          :lineData="lineDataEchart"
          :name="name"
          :chartsName="chartsName"
          title="总收益"
        />
      </view>
    </view>
    <!-- <view class="list_top" v-if="listData.length > 0">
      <view class="list_title">
        收益明细
      </view>


    </view> -->
    <ComList :loading-type="2">
      <FinanceListCard
        v-for="item in listData"
        :key="item.log_id"
        :info="item"
      />
    </ComList>
    <BaseBackTop
      @onPageScroll="onPageScroll"
      :scrollTop="scrollTop"
    ></BaseBackTop>
  </view>
</template>

<script>
import BaseCountTo from "@/components/base/BaseCountTo.vue"
import BaseIcon from "@/components/base/BaseIcon.vue"
import BaseNavbar from "@/components/base/BaseNavbar.vue"
import ComList from "@/components/list/ComList.vue"
import FinanceListCard from "../components/cards/FinanceListCard.vue"
import myPull from "@/mixins/myPull.js"
import { subtractDaysAndFormat } from "@/wxutil/times"
import uCalendar from "@/components/uni-calendar/u-calendar.vue"
import UCharts from "@/pagesB/dataAnalysis/components/UCharts"
import BaseBackTop from "@/components/base/BaseBackTop.vue"
import PerformanceMonitor from "@/components/common/PerformanceMonitor .vue"
import performanceMixin from "@/mixins/performanceMixin"
export default {
  components: {
    BaseNavbar,
    BaseCountTo,
    BaseIcon,
    ComList,
    FinanceListCard,
    uCalendar,
    UCharts,
    BaseBackTop,
    PerformanceMonitor,
  },
  data() {
    return {
      scrollTop: 0,
      title: "财务管理",
      balance: 0,
      totalIncome: 0, //总收入
      totaAmount: 0, //总收益
      totalNum: 0, //记录总数
      withdrawalBalance: 0, //提现金额
      subsectionList: [
        //     {
        //     name: '昨天'
        // },
        {
          name: "今天",
          status: 1,
        },
        {
          name: "昨天",
          status: 2,
        },

        {
          name: "前天",
          status: 3,
        },
        {
          name: "本月",
          status: 4,
        },

        {
          name: "上月",
          status: 5,
        },
        {
          name: "自定义",
          isCustom: true,
        },
      ],
      myselectList: [
        //     {
        //     name: '昨天'
        // },
        {
          name: "我的",
          status: 1,
        },
        {
          name: "我的下级",
          status: 0,
        },
      ],
      myselectIndex: 0,
      current: 0,
      startDate: "",
      endDate: "",
      showCalendar: false,
      mainColor: "#fa3534",
      myMainColor: "#0EADE2",
      isMe: 1,
      userList: false, //商户页面来的
      user_login: "",
      lineData: [],
      name: "总收益：",
      chartsName: "我的收益",
      lineDataEchart: [],
    }
  },
  methods: {
    onPageScroll(e) {
      this.scrollTop = e.scrollTop
    },
    handleSubsection() {
      if (this.current === 5 && !this.showCalendar) this.showCalendar = true
    },
    handleData(date, startDate, endDate) {
      // this.queryDate = date
      this.startDate = startDate
      this.endDate = endDate
      this.refresh()
    },
    changeSubsection(i) {
      this.current = i
      let selectItem = this.subsectionList[i]
      if (selectItem.isCustom) {
        this.showCalendar = true
      } else {
        let date = new Date()
        if (selectItem.status < 4) {
          this.startDate = this.endDate = subtractDaysAndFormat(
            selectItem.status - 1
          )
        } else if (selectItem.status == 4) {
          this.endDate =
            date.getFullYear() +
            "-" +
            (date.getMonth() + 1) +
            "-" +
            date.getDate()
          this.startDate =
            date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + 1
        } else if (selectItem.status == 5) {
          let currentDate = new Date()
          let lastMonthDate = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth(),
            0
          )

          this.startDate =
            lastMonthDate.getFullYear() +
            "-" +
            (lastMonthDate.getMonth() + 1) +
            "-1"
          this.endDate =
            lastMonthDate.getFullYear() +
            "-" +
            (lastMonthDate.getMonth() + 1) +
            "-" +
            lastMonthDate.getDate()
        }
        this.handleData("", this.startDate, this.endDate)
      }
    },
    changeMyselect(i) {
      this.myselectIndex = i
      let selectItem = this.myselectList[i]
      if (selectItem.status == 0) {
        this.isMe = 0
      } else {
        this.isMe = 1
      }
      this.refresh()
    },
    changeCalendar(e) {
      let { startDate, endDate } = e
      this.handleData("", startDate, endDate)
    },
    goWithdrawal() {
      uni.navigateTo({
        url: `/pagesB/finance/Withdrawal?from=finance&balance=${this.balance}`,
      })
    },
    goWithdrawalRecord() {
      uni.navigateTo({ url: "/pagesB/finance/WithdrawalRecord" })
    },
    getList(page, done) {
      let data = {
        page,
        limit: 10,
        start_time: this.startDate,
        end_time: this.endDate,
        isMe: this.isMe,
        user_login: this.user_login,
      }
      this.$u.api.getIncomeList(data).then((res) => {
        if (page == 1) {
          this.totalIncome = parseFloat(res.total_income.income) || 0
          this.totalNum = res.data.total
          this.totaAmount = parseFloat(res.data.total_amount) || 0
          this.withdrawalBalance = parseFloat(res.withdrawal_balance) || 0
          this.balance = parseFloat(res?.data?.userInfo?.cash || 0)
          this.lineData = res.table
          //更新本地保存余额start
          let vUserInfoNew = this.vUserInfo
          vUserInfoNew.user.cash = this.balance
          this.$u.vuex("vUserInfo", vUserInfoNew)
          //更新本地保存余额end
        }

        done(res.data.list)
      })
      if (this.userList) {
        let data = {
          start_time: this.startDate,
          end_time: this.endDate,
          uid: this.uid,
        }
        this.$u.api.getUserCashReport(data).then((res) => {
          if (page == 1) {
            this.lineDataEchart = res
            //更新本地保存余额end
          }
        })
      }
    },
  },
  onLoad(opt) {
    if (opt.from == "userList") {
      this.userList = true
      this.isMe = 0
      this.title = "收益统计"
      this.user_login = opt?.user_login
      this.uid = opt?.user_id
    }
    this.endDate = this.startDate = subtractDaysAndFormat(0)
    this.handleData("", this.startDate, this.endDate)
  },
  onShow() {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false
      this.refresh()
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false
      this.refresh()
    }
    /*#endif */
  },
  mixins: [myPull(), performanceMixin],
}
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.panel_content_top {
  display: flex;
  justify-content: space-around;

  > view {
    display: flex;
    justify-content: space-around;
    align-items: center;
    // border: 1px solid #000;
  }
}

.profit {
  &-title {
    padding: 20rpx;
    font-size: $font-size-middle;
    color: $textDarkGray;
  }
}

.list_m {
  margin-left: 20rpx;
  font-size: 24rpx;
}

.list_top {
  margin: 30rpx 30rpx 0 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.list_title {
  color: $textBlack;
  font-size: $font-size-xlarge;
  font-weight: bold;
}

.info {
  padding: 30rpx;
  background-color: $uni-bg-color;

  .panel {
    position: relative;
    height: 270rpx;

    &_bg {
      width: 100%;
      height: 100%;
    }

    &_content {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      &_top {
        color: $textWhite;
        font-size: $font-size-small;
        border-bottom: 2rpx solid $textWhite;
        padding: 80rpx 25rpx 20rpx;
        position: relative;

        .balance {
          margin: 0 6rpx 0 20rpx;
        }

        .rule {
          position: absolute;
          right: 20rpx;
          top: 20rpx;
        }
      }

      &_bottom {
        color: $textWhite;
        padding: 26rpx;
        position: relative;

        .withdrawal {
          font-size: $font-size-xlarge;
        }

        .record {
          position: absolute;
          top: 30rpx;
          right: 0;
          height: 40rpx;
          width: 150rpx;
          background-color: #25c2ff;
          border-radius: 20rpx 0 0 20rpx;
          font-size: $font-size-xsmall;
        }
      }
    }
  }

  .subsection {
    margin-top: 10rpx;
  }

  .income {
    .title {
      color: $textBlack;
      font-size: $font-size-xlarge;
      font-weight: bold;
      margin: 50rpx 0 30rpx;
    }

    .screen {
      .total {
        color: $themeComColor;
        font-size: $font-size-small;
      }
    }
  }
}
</style>
