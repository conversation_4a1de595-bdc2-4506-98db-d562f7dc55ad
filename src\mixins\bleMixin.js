import ble from "@/wxutil/ble";
export const bleMixin = {
    methods: {
        //蓝牙相关
        btConnectCallBack: function (obj, types) { // 链接后回调
            // console.log("index.js回调函数" + obj, types);
            //this.onShowBuyModel()
        },

        btErrorCallBack: function (errorType, errorMessage) {  //处理错误码回调
            // console.log('btErrorCallBack', errorType);
            ble.bluetoothStatus(errorType);
        },

        btWriteCallback: function (writeE, currentOrderSN) { // 蓝牙写入回调
            // console.log('写入数据状态', writeE);
            //todo界面处理逻辑

            if (writeE == 'w') { // 写入
                //feedbackApi.hideTimerToast(); //清空loadding
                // clearInterval(commonBLEDatas.downSecondId); //清空倒计时
                // uni.showToast({
                //     title: '连接失败,请再次尝试(0)',
                //     icon: 'none'
                // })
                // ble.closeBLEConnection();
            } else if (writeE == 'success') {
                //this.updateOrder(currentOrderSN);
            }
        },

        //初始化蓝牙
        initBt: function (device_sn, deviceType, mkey) {
            // console.log('initBt,ble=', ble);
            ble.setConnectionActionType(this.btConnectCallBack); //连接后操作回调
            ble.setBtErrorCallback(this.btErrorCallBack); //执行错误时错误码回调
            ble.setWriteCallback(this.btWriteCallback); //写入数据回调
            ble.initBle(device_sn, deviceType, mkey);
        },

        //充电
        onRecharge: async function (time) {
            // console.log('准备写入充电指令，time=', time)
            ble.rechargeDevice(time, this.btRechargeCallback)
        },

        btRechargeCallback: function () {
            // showOkCancelDialog({
            //     showCancel: false,
            //     title: '提示',
            //     content: '正在为您充电中',
            // }).then(res => {

            // })
        },

        //开始连接蓝牙设备
        startBleConnect(device_sn, deviceType, mkey) {
            this.initBt(device_sn, deviceType, mkey)
        },

    },

}