<template>
  <view class="selectMainboard">
    <BaseNavbar :title="title" />

    <view class="mainboardItemContent">
      <ComList :loading-type="computedLoadingType">
        <view
          v-for="(item, index) in mainboardList"
          :key="index"
          class="mainboardItem"
          @click="selectMainboard(item, index)"
        >
          <radio
            :checked="selectIndex == index"
            color="#2979ff"
            style="transform: scale(0.7)"
          />

          <view class="mainboardItemDesc">
            <view class="mainboardItemDescContent">
              <view class="mainboardName">
                {{ item.device_sn }}
              </view>
            </view>
            <view class="statusContent">
              <view
                class="status"
                :class="item.status === 1 ? 'bound' : 'unbound'"
              >
                {{ item.status === 1 ? "已绑定" : "未绑定" }}
              </view>
            </view>
          </view>
        </view>
      </ComList>

      <!-- 空状态 -->
      <view v-if="mainboardList.length === 0" class="empty-state">
        <view class="empty-text">暂无主板数据</view>
      </view>
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue"
import ComList from "@/components/list/ComList.vue"

export default {
  name: "MainboardList",
  components: {
    BaseNavbar,
    ComList,
  },
  data() {
    return {
      title: "主板列表",
      mainboardList: [],
      selectIndex: -1,
      loadingType: 0,
    }
  },
  computed: {
    // 当数据较少时不显示加载更多提示
    computedLoadingType() {
      // 如果数据少于10条，设置为-1不显示加载更多
      if (this.mainboardList.length < 10) {
        return -1
      }
      return this.loadingType
    },
  },
  methods: {
    // 选择主板
    selectMainboard(item, index) {
      this.selectIndex = index
      console.log("选择的主板:", item)
      // 直接返回选中的主板数据
      this.returnWithData(item.device_sn)
    },

    // 返回上一页并传递数据
    returnWithData(item) {
      // 获取上一页实例并传递数据
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]

      if (prevPage) {
        // 设置上一页的新设备码
        prevPage.$vm.newDeviceCode = item
        this.isShowSuccess("主板选择成功", 1)
      } else {
        uni.navigateBack()
      }
    },

    // 获取主板列表数据（暂时使用默认数据）
    async getMainboardList() {
      try {
        const res = await this.$u.api.getMotherboardsList()
        this.mainboardList = res.data
        // console.log("使用默认主板列表数据", res)
      } catch (error) {
        // console.error("获取主板列表失败:", error)
      }
    },
  },

  onLoad() {
    // 页面加载时获取主板列表
    this.getMainboardList()
  },
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
$margin-buttom-item: 10rpx;

.mainboardItem {
  width: 100%;
  background: rgba(255, 255, 255, 1);
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid rgba(229, 229, 229, 1);
  border-radius: $cardRadius;
  box-sizing: border-box;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;

  // &:active {
  //   background: #f5f5f5;
  // }
}

.mainboardItemDesc {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mainboardItemDescContent {
  flex: 1;
}

.mainboardName {
  font-size: 32rpx;
  font-weight: 400;
  color: #333;
}

.statusContent {
  display: flex;
  align-items: center;
}

.status {
  font-size: 28rpx;
  color: #666;

  &.bound {
    color: #52c41a;
  }

  &.unbound {
    color: #ff7875;
  }
}

.mainboardItemContent {
  padding: 0 20rpx;
}

.empty-state {
  text-align: center;
  padding: 120rpx 30rpx;
  background: #fff;
  border-radius: $cardRadius;
  margin: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
