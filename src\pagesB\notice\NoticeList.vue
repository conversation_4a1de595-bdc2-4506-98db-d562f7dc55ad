<template>
  <view>
    <view class="sticker">
      <BaseNavbar title="消息中心" />
      <BaseTabs :current="curTabIndex" :list="tabList" :isShowBar="false" @change="tabChange" />
      <BaseDropdown :options-list="optionsList" @change="change" :num="msgTotal" />
    </view>

    <view class="news">

      <!-- <view class="news-btn" @click="noMsg">一键未读读</view> -->
      <view class="news-btn" @click="readMessage">一键已读</view>
    </view>
    <ComList :loading-type="loadingType">
      <NoticeListCard
        v-for="item in listData"
        :key="item.id"
        :info="item"
        @setStatus="setStatus(item)"
      />
    </ComList>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseTabs from "../../components/base/BaseTabs.vue";
import ComList from "@/components/list/ComList.vue";
import NoticeListCard from "../components/cards/NoticeListCard.vue";
import myPull from "@/mixins/myPull.js";
import BaseDropdown from '@/components/base/BaseDropdown.vue'
export default {
  components: { BaseTabs, BaseNavbar, ComList, NoticeListCard,BaseDropdown },
  data() {
    return {
      tabList: [
        {
          name: "全部",
          status: 0,
        },
        {
          name: "系统",
          status: 1,
        },
        {
          name: "活动",
          status: 2,
        },
        {
          name: "其他",
          status: 9,
        },
      ],
      curTabIndex: 0,
      unreadNum: 0, //未读数量
      noticeList:[],
      optionsList: [
        {
          title: '全部',
          options: [
            { label: '全部', value: 0, status: '0,1', tiem: '' },
            {
              label: '未读',
              value: 1,
              status: '0',
              time: ''
            },
            {
              label: '已读',
              value: 2,
              status: '1',
              time: ''
            },
          ],
          value: 0,
        },
      ],
      msgTotal: 0, //消息总数
      read_status:'0,1', //-1全部 0未读 1已读,
    };
  },
  methods: {
    change(item) {
      this.optionsList = item
      let index = item[0].value
      this.read_status = item[0].options[index].status
      this.refresh()
    },
    setStatus(item) {
      this.$set(item, "read_status", 1);
    },
    tabChange(e) {
      this.curTabIndex = e;
      this.refresh();
    },
    getList(page, done) {
      let data = {
        type: this.tabList[this.curTabIndex].status,
        page,
        read_status: this.read_status,
      };
      this.$u.api.userMessage(data).then((res) => {
        this.unreadNum = res.data.total;
        if(page==1){
          this.msgTotal = res.data.total
        }
        // this.$u.vuex('vNoticeNum', res.data.count)
        done(res.data.list);
      });
    },
    readMessage() {
      // console.log('点击了一键已读')
      let readListId = [];
      this.listData?.forEach((item) => {
        item.read_status !== 1 && readListId.push(item.id);
      });
      // console.log('未读信息',readListId,this.noticeList);
      if (readListId.length == 0) return;
      let data = {
        // ids: readListId.join(","),
        ids: '',
        type: this.tabList[this.curTabIndex].status,
        read_status: 1,
      };
      this.$u.api.readMessage(data).then((res) => {
        this.refresh();
      });
    },
    // noMsg() {
    //   // console.log('点击了一键已读')
    //   let readListId = [];
    //   this.listData?.forEach((item) => {
    //     item.read_status == 1 && readListId.push(item.id);
    //   });
    //   // console.log('未读信息',readListId,this.noticeList);
    //   if (readListId.length == 0) return;
    //   let data = {
    //     // ids: readListId.join(","),
    //     ids: '',
    //     type: this.tabList[this.curTabIndex].status,
    //     read_status: 0,
    //   };
    //   this.$u.api.readMessage(data).then((res) => {
    //     this.refresh();
    //   });
    // },
  },
  onLoad(opt) {
    if(opt?.type){
      this.curTabIndex=opt?.type;
    }
    this.refresh();
  },
  onShow() {},
  mixins: [myPull()],
};
</script>

<style lang="scss" scoped>
.news {
  // display: flex;
  // justify-content: space-between;
  padding: 20rpx 30rpx;
  position: fixed;
  bottom: 50rpx;
  right: 30rpx;
  // width: 100r;
  // background: #fff;
  z-index: 999;
  &-num {
    font-size: $font-size-small;
    color: $themeComColor;
  }
  &-btn {
    color: $textBlack;
    font-size: $font-size-base;
    padding: 5rpx 10rpx ;
    border-radius: 50rpx;
    border: 1px solid $themeComColor;
  }
}
</style>