<script>
import { isIPhoneXRegex, calcBottomBarHeight } from './common/tools/safeArea'
export default {
  onLaunch: function () {
    this.siteInfo()
    //计算iphoneX小黑条高度
    this.$u.vuex('vIphoneXBottomHeight', calcBottomBarHeight() || 0)
    /* #ifndef H5 */
    let { statusBarHeight, screenWidth } = uni.getSystemInfoSync()
    let vStatusBarHeight = (750 / screenWidth) * statusBarHeight
    let vNavBarHeight = (750 / screenWidth) * 48
    this.$u.vuex('vStatusBarHeight', Number(vStatusBarHeight)) //状态栏高度rpx
    this.$u.vuex('vNavBarHeight', Number(vNavBarHeight)) //导航栏高度rpx
    let appid = uni.getAccountInfoSync().miniProgram.appId
    this.$u.vuex('vAppId', appid)
    this.$u.vuex('vIsCharge', appid == 'wx50be0622d644a4ee') //这里判断是否是无线充小程序
    /* #endif */
   
  },
  globalData: {},
  methods: {
    async siteInfo() {
      let res = await this.$u.api.siteInfo()
      if (res) {
        this.$u.vuex('vSiteConfig', res)
        this.$u.vuex('vTel', res?.site_info?.after_service_phone)
        this.$u.vuex('vPointName', res?.site_info?.point_category || '场地')
        this.$u.vuex('vGamingTime', res?.site_info?.gaming_time)
      }
    },
  },
}
</script>

<style lang="scss">
/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
@import 'uview-ui/index.scss';
// page {
//   background-color: $pageBgColor;
// }
</style>
