<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="device-content">
      <!--设备编号-->
      <view class="device-content-item">
        <view class="device-content-item__device-desc">设备编号</view>
        <view class="device-content-item__device-desc-code">{{
          device_sn
        }}</view>
      </view>
      <!--系统编号编号-->
      <view class="device-content-item">
        <view class="device-content-item__device-desc">系统码</view>
        <view class="device-content-item__device-desc-code">{{
          umDetail.pseudocode||''
        }}</view>
      </view>
      <!-- 套餐数 -->
      <view class="device-content-item">
        <view class="device-content-item__grid">{{vCargoLanes}}数</view>
        <view class="device-content-item__grid-number">{{
          umDetail.number
        }}</view>
      </view>
      <!-- 点位名称 -->
      <view class="device-content-item">
        <view class="device-content-item__hotelname">{{ vPointName }}名称</view>
        <view class="device-content-item__hotelname-select">{{
          hotel_name ? hotel_name : ""
        }}</view>
      </view>
      <!-- 桌号 -->
      <!-- <view class="device-content-item">
        <view class="device-content-item__room">备注</view>
        <view class="device-content-item__room-number">
          <input type="number" placeholder="请输入备注信息" v-model="room_num" />
        </view>
      </view> -->
      <LocationType @locationStr="locationStr" :str="room_num" :isCar="umDetail.is_car_device" :isPPJ="umDetail.is_pp_device" :numbers="number"></LocationType>

      <!-- 拥有者 -->
      <view
        class="device-content-item"
        @click.stop="selectOwner"
        v-if="isShowOwner"
      >
        <view class="device-content-item__owner">拥有者</view>
        <view class="device-content-item__owner-select">{{
          owner ? owner : "请选择拥有者"
        }}</view>
      </view>

      <block v-if="isChargeDevice">
        <view class="device-content-item">
          <view class="device-content-item__room">是否充电</view>
          <view class="device-content-item__room-number">
            <radio @click="setCharge(true)" :checked="isCharge" color="#05b2f2"
              >是</radio
            >
            <radio
              :style="{ marginLeft: '50rpx' }"
              @click="setCharge(false)"
              :checked="!isCharge"
              color="#05b2f2"
              >否
            </radio>
          </view>
        </view>
        <view class="device-content-item" v-if="isCharge">
          <view class="device-content-item__room">充电规则</view>
          <view class="charge">
            <view class="charge-rule-money" style="width:240rpx">
              <BaseInput
                placeholder="请输入金额"
                v-model="rule_money"
                rightText="元/小时"
              />
            </view>
            <!-- <view class="charge-rule-time">
              <BaseInput
                placeholder="请输入时间"
                v-model="rule_time"
                :rightText="rule_unit === 2 ? '小时' : '分钟'"
              />
            </view> -->
            <!-- <view @click="showSheet = true" class="select-rule">
              选择时间规则
              <u-action-sheet
                :list="sheetList"
                v-model="showSheet"
                :safe-area-inset-bottom="true"
                :cancel-btn="false"
                @click="selectRuleUnit"
              ></u-action-sheet>
            </view> -->
          </view>
        </view>
        <view class="device-content-item">
          <view class="device-content-item__room">是否免费5分钟</view>
          <view class="device-content-item__room-number">
            <radio @click="setFreeOrder(true)" :checked="isFreeOrder" color="#05b2f2"
              >是</radio
            >
            <radio
              :style="{ marginLeft: '50rpx' }"
              @click="setFreeOrder(false)"
              :checked="!isFreeOrder"
              color="#05b2f2"
              >否
            </radio>
          </view>
        </view>
        <view class="device-content-item">
          <view class="device-content-item__room">是否取消30分钟</view>
          <view class="device-content-item__room-number">
            <radio @click="setHalfHour(true)" :checked="isHalfHour" color="#05b2f2"
              >是</radio
            >
            <radio
              :style="{ marginLeft: '50rpx' }"
              @click="setHalfHour(false)"
              :checked="!isHalfHour"
              color="#05b2f2"
              >否
            </radio>
          </view>
        </view>
      </block>
      <!-- <block v-if="vSelectSysObj.extra.includes('hd.handaiwulian.com')"> -->
        <view class="device-content-item">
          <view class="device-content-item__owner">设备位置</view>
          <view
            class="device-content-item__owner-select select-place"
            @click="onClickGetLocation"
          >
            {{ choosedAddress || "点击获取当前位置" }}
          </view>
        </view>
        <view class="device-content-item">
          <view class="device-content-item__owner">GPS位置信息</view>
          <view
            class="device-content-item__owner-select select-place"
            @click="goLocation()"
          >
            {{  "点击查看GPS位置" }}
          </view>
        </view>

        <!-- <view class="uploadWrap">
          <view class="label">上传图片</view>
          <view class="upload">
            <BaseUpload
              width="200"
              :maxCount="3"
              :auto="true"
              @onUpload="onUpload"
            />
          </view>
        </view> -->
      <!-- </block> -->

      <view
        class="footer"
        :style="{ paddingBottom: 20 + vIphoneXBottomHeight + 'rpx' }"
      >
        <view class="footer__buttom">
          <BaseButton @onClick="save">保 存</BaseButton>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseUpload from "@/components/base/BaseUpload.vue";
import BaseButton from "../../components/base/BaseButton.vue";
import { locationMixin } from "@/mixins/locationMixin";
import BaseInput from "../../components/base/BaseInput.vue";
import ble from "@/wxutil/ble";
import LocationType from '../components/selects/LoctionType.vue';
export default {
  components: { BaseNavbar, BaseUpload, BaseButton, BaseInput,LocationType },
  mixins: [locationMixin],
  data() {
    return {
      title: "设备详情",
      fromData: "", //哪里来的
      mid: "",
      umDetail: "",
      room_num: "",
      hotel_name: "",
      device_sn: "",
      ownerid: "",
      owner: "",
      isShowOwner: false, //显示拥有者
      uploadUrlList: [], //上传图片成功后的url图片
      isCharge: false,
      isChargeDevice: false,
      isFreeOrder:true, // 是否免费5分钟
      isHalfHour:true, // 是否有半个小时
      rule_money: "",
      rule_time: "",
      rule_unit: 2,
      sheetList: [
        // {
        //   text: "分钟",
        //   color: "#333",
        //   fontSize: 28,
        //   rule_unit: 1,
        // },
        {
          text: "小时",
          color: "#333",
          fontSize: 28,
          rule_unit: 2,
        },
      ],
      showSheet: false,
      number: 1,
    };
  },
  methods: {
    locationStr(str, number) {
      this.number = number;
      this.room_num = str;
      console.log('位置信息',str,this.room_num, number)

    },
    goLocation(){
      uni.navigateTo({
        url:
          `/pagesC/mapMode/GoMapLoction?from=device&item=${encodeURIComponent(JSON.stringify(this.umDetail))}`
      });
    },
    //选择充电规则  小时or分钟
    selectRuleUnit(i) {
      this.rule_unit = this.sheetList[i]?.rule_unit ?? 2;
    },
    setCharge(flag) {
      this.isCharge = flag;
    },
    setFreeOrder(flag) {
      this.isFreeOrder = flag;
    },
    setHalfHour(flag) {
      this.isHalfHour = flag;
    },
    async getUMDetail() {
      // 获取数据
      //判断是否是获取屏幕详细信息
      let data = {
        device_sn: this.device_sn,
        mid: this.mid,
      };
      let rtnData = await this.$u.api.getUMDetail(data);
      this.umDetail = rtnData;
      if (this.umDetail) {
        this.room_num = this.umDetail.room_num;
        this.hotel_name = this.umDetail.hotelName;
        this.device_sn = this.umDetail.device_sn;
        this.ownerid = this.umDetail.uid;
        this.owner = this.umDetail.user_login;

        //售袋机系统，此时需要额外保存地址信息和上传的图片信息
        this.choosedAddress = this.umDetail.addressDetail;
        this.choosedAddressInfo.name = this.umDetail.adr_title;
        this.choosedAddressInfo.address = this.umDetail.addressDetail;
        this.choosedAddressInfo.latitude = this.umDetail.lat;
        this.choosedAddressInfo.longitude = this.umDetail.lon;

        this.isChargeDevice = ble.isRechargeDevice(this.umDetail.deviceType);
        this.isCharge =
          this.umDetail.isCharge && Number(this.umDetail.isCharge) > 0;

        this.rule_money = this.umDetail.rule_money;
        this.rule_time = this.umDetail.rule_time;
        this.rule_unit = this.umDetail.rule_unit;
        if (this.umDetail.rule_unit === 1) {
          this.rule_unit = 2;
          this.rule_money = this.rule_money * 60;
        }
      } else {
        if (this.fromData == "home") this.isShowErr("设备编号不正确~", 1);
      }
    },
    selectOwner() {
      if (!this.isShowFooter) {
        return;
      }
      //if (this.from === "deviceList") {
      //	uni.navigateTo({
      //		url: "./../userManage/selectUser?from=home_device_detail"
      //	});
      //}
    },
    onUpload(e) {
      this.uploadUrlList = e;
    },
    async save() {
      // 保存数据
      let data = {
        id: this.umDetail.id,
        machineName: this.umDetail.machineName,
        number: this.umDetail.number,
        ownerId: this.ownerid,
        device_type: this.umDetail.deviceType,
        device_sn: this.device_sn,
        room_num: this.room_num,
      };
      // if (this.vSelectSysObj.extra.includes("hd.handaiwulian.com")) {
        //售袋机系统，此时需要额外保存地址信息和上传的图片信息
        data = {
          ...data,
          adr_title: this.choosedAddressInfo.name || "",
          addressDetail: this.choosedAddressInfo.address || "",
          lat: this.choosedAddressInfo.latitude || "",
          lon: this.choosedAddressInfo.longitude || "",
          img_info: this.uploadUrlList.join("+++++"),
        };
      // }

      if (this.isChargeDevice) {
        if (this.isCharge) {
          data["is_recharge"] = 2;
          data["rule_money"] = this.rule_money;
          data["rule_time"] = 1; // 时长：1
          data["rule_unit"] = 2; // 单位 小时
        } else {
          data["is_recharge"] = 1;
        }
        data["is_free_order"] = this.isFreeOrder ? 1 : 0;
        data["is_half_hour"] = this.isHalfHour ? 1 : 0;
      }

      this.$u.api.editUMDetail(data).then((res) => {
        this.isShowSuccess("保存成功", 1, () => {}, true);
      });
    },
    onClickGetLocation() {
      this.getSelectLocation();
    },
  },
  onLoad(opt) {
    if (opt?.from) {
      this.fromData = opt.from;
      if (opt.from == "home_device_bind_hotel") {
        this.mid = opt.mid;
      }
      if (opt.from == "device_list" || opt.from == "home") {
        this.device_sn = opt.device_sn;
      }
    }
    this.getUMDetail();
    // 是否显示拥有者
    if (this.vUserInfo.role_id > 1&& this.vButtonPermisFour) {
      this.isShowOwner = true;
    } else {
      this.isShowOwner = false;
    }
  },
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.device-content {
  background-color: white;
  width: 750rpx;
  font-size: $font-size-large;
  margin-top: 20rpx;
  color: $textBlack;

  &-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 100rpx;
    border-bottom: 1rpx solid #e5e5e5;

    view {
      &:first-child {
        // width: 140rpx;
        margin: 20rpx;
        flex-shrink: 0;
      }
    }

    &__set-charge {
      &__content {
        display: inline-flex;
        align-items: center;

        input {
          width: 160rpx;

          &.last-child {
            margin-left: 10rpx;
          }
        }

        &__text {
          padding-left: 5rpx;
          transform: scale(1, 4) rotateX(60deg) !important;
        }

        &-btn {
          //   width: 100rpx;
          text-align: center;
          background-color: $themeComColor;
          border-radius: 10rpx;
          color: white;
          display: inline-flex;
          align-items: center;

          &__icon {
            margin: 0;
          }

          &__text {
            padding-left: 12rpx;
          }
        }
      }
    }

    &__hotelname-select,
    &__device-desc-code,
    &__room-number {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: inline-block;
      width: 520rpx;
    }

    &__owner-select {
      text-overflow: ellipsis;
      overflow: hidden;
      display: inline-block;
      width: 520rpx;
    }

    &__ischarge-select {
      display: inline-flex;
      align-items: center;

      radio {
        transform: scale(0.7);
      }
    }

    .charge {
      display: flex;
      align-items: center;

      &-rule-money {
        flex: 2;
      }

      &-rule-time {
        flex: 1;
        margin-left: 20rpx;
      }

      .select-rule {
        color: $themeComColor;
        margin-left: 10rpx;
        margin-right: 20rpx;
        font-weight: 700;
      }
    }
  }
}

.uploadWrap {
  padding: 20rpx;

  .upload {
    margin-top: 20rpx;
  }
}

.footer {
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  background-color: $uni-bg-color;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
}
</style>
