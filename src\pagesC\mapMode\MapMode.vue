<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="tabs_box">
      <view class="tabs">
        <BaseTabs :current="curTabIndex" :list="tabsList" :isShowBar="false" @change="tabChange" />
      </view>
      <view class="device_num">共{{ total }}台设备</view>
    </view>
    <view class="map_layout">
      <map :longitude="curLongitude" :latitude="curLatitude" :scale="scale" :markers="markers" id="map" :show-location="true"
        @markertap="onMarkerTap" @tap="onMapTap" />
    </view>
    <view
      class="map_card"
      :style="{ bottom: vIphoneXBottomHeight + 30 + 'rpx' }"
      v-show="isShowCard"
    >
      <DeviceListCard
        v-if="isFromDevice"
        :info="curClickMarkerMachine"
        @onUnbind="onUnbind(curClickMarkerMachine)"
        @deviceGoods="deviceGoods(curClickMarkerMachine.device_sn)"
        @repl="repl(curClickMarkerMachine.device_sn)"
      />
      <ScreenListCard v-if="isFromScreen" :info="curClickMarkerMachine" />
      <ReplenishListCard v-if="isFromReplenish" :info="curClickMarkerMachine" />
    </view>

    <BaseModal :show.sync="isShowModal" :content="'您的设备将与' + vPointName + '解除绑定，是否继续解绑？'" @confirm="confirmUnBind" />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import { locationMixin } from "@/mixins/locationMixin";
import BaseTabs from "@/components/base/BaseTabs.vue";
import { globalKeys } from "@/global/globalKeys";
import ReplenishListCard from "../components/cards/ReplenishListCard.vue";
import ScreenListCard from "../components/cards/ScreenListCard.vue";
import DeviceListCard from "../components/cards/DeviceListCard.vue";
import BaseModal from "@/components/base/BaseModal.vue";
export default {
  components: {
    BaseNavbar,
    BaseTabs,
    ReplenishListCard,
    ScreenListCard,
    DeviceListCard,
    BaseModal,
  },
  mixins: [locationMixin, globalKeys],
  data() {
    return {
      title: "地图模式",
      tabsList: [
        {
          name: "全部",
          status: 0,
        },
        {
          name: "在线",
          status: 1,
        },
        {
          name: "离线",
          status: 3,
        },
      ],
      //map上下文
      mapCtx: {},
      //地图坐标信息
      markers: [],
      fromData: "", //来源渠道
      isFromDevice: false, //来自设备
      isFromScreen: false, //来自屏幕
      isFromReplenish: false, //来自补货
      isShowCard: false, //是否显示卡片信息
      curTabIndex: 0,
      status: 0,
      total: 0, //设备总数量
      nearbyMachineList: [], //附近的设备
      curClickMarkerMachine: {}, //点击的marker设备信息
      unBindItem: {},
      isShowModal: false,
      scale:18,
    };
  },
  methods: {
    truncateDecimal(num, decimalPlaces) {
      let numStr = num.toString();
      let decimalPos = numStr.indexOf('.');
      if (decimalPos === -1) {
        return num;
      }
      let integerPart = numStr.substring(0, decimalPos);
      let decimalPart = numStr.substring(decimalPos + 1, decimalPos + 1 + decimalPlaces);
      while (decimalPart.length < decimalPlaces) {
        decimalPart += '0';
      }
      return parseFloat(integerPart + '.' + decimalPart);
    },
    goLocation() {
      // wx.openLocation({
      //   latitude: this.truncateDecimal(43.766017913818374, 6),
      //   longitude: this.truncateDecimal(43.766017913818374, 6),
      //   name: '',
      //   address: '',
      //   scale: 16,
      // });
      const mapContext = wx.createMapContext('map');
      mapContext.openMapApp({
        // longitude:30.375596, // 经度
        latitude:30.37559, // 纬度
        longitude:114.32168, // 经度
        destination: '',
      });
    },
    getLocPermission() {
      this.initLocPermission(() => {
        this.getCurrentLocation(() => {
          this.getUserUMs();
        });
      });
    },
    tabChange(e) {
      this.curTabIndex = e;
      this.status = this.tabsList[e].status;
      this.getUserUMs();
    },
    getUserUMs() {
      this.isShowCard = false;
      let data = {
        device_sn: "",
        dianweiid: 0,
        owner: "",
        page: 1,
        status: this.status, // 是否在线
        lat: this.curLatitude,
        lon: this.curLongitude,
        radius: 2,
      };
      this.$u.api.getUserUMs(data).then((res) => {
        this.total = res.total;
        this.nearbyMachineList = res.data;
        this.initMarkers();
      });
    },
    //点击了marker
    onMarkerTap(e) {
      let machineInfoList = this.nearbyMachineList.filter(
        (item) => item.id == e.markerId
      );
      this.curClickMarkerMachine = machineInfoList?.[0];
      this.isShowCard = true;
    },
    //点击了地图
    onMapTap() {
      this.isShowCard = false; //卡片隐藏
    },
    //初始化标注点
    initMarkers() {
      //console.log('locationMixin initMarkers,目标经度=',this.targetLongitude,',纬度=',this.targetLatitude)
      let markerArr = this.nearbyMachineList.map((item) => ({
        id: item.id,
        latitude: item.lat,
        longitude: item.lon,
        iconPath:
          "",
        width: 40,
        height: 40,
      }));
      this.markers = markerArr;
    },
    onUnbind(item) {
      if (item.dianweiid && item.dianweiid > 0) {
        // 已经绑定了点位 弹出是否解绑
        this.unBindItem = item;
        this.isShowModal = true;
      } else {
        // 如果没有绑定点位，跳转到绑定点位界面
        uni.navigateTo({
          url:
            `/pagesB/place/BindPlace?from=device&device_sn=` +
            item.device_sn +
            "&mid=" +
            item.id,
        });
      }
    },
    deviceGoods(device_sn) {
      uni.navigateTo({
        url: `/pagesB/device/DeviceGoodsList?from=device&device_sn=${device_sn}`,
      });
    },
    repl(device_sn) {
      uni.navigateTo({
        url:
          "/pagesB/device/DeviceGoodsList?from=replenish&device_sn=" +
          device_sn,
      });
    },
    confirmUnBind() {
      //确认解绑
      let data = {
        device_sn: this.unBindItem.device_sn,
      };

      this.$u.api.unbindHotel(data).then((res) => {
        this.isShowSuccess("解绑成功", 0, () => this.getUserUMs());
      });
    },
  },
  onLoad(options) {
    if (options?.from) {
      this.fromData = options?.from;
      let from = options?.from;
      if (from == "device") {
        this.isFromDevice = true;
      } else if (from == "screen") {
        this.isFromScreen = true;
      } else if (from == "replenish") {
        this.isFromReplenish = true;
      }
    }
    this.mapCtx = uni.createMapContext("map"); // map为地图的id
  },
  onShow(e) {
    /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false;

      this.getUserUMs();
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false;

      this.getUserUMs();
    }
    /*#endif */
  },
  mounted() {
    setTimeout(() => {
      this.getLocPermission();
    }, 1000);
  },
};
</script>


<style lang="scss" scoped>
.map_layout {
  width: 100%;
  height: 100vh;

  map {
    width: 100%;
    height: 100%;
  }
}

.tabs_box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  .tabs {
    width: 100%;
  }

  .device_num {
    white-space: nowrap;
    margin-right: 10rpx;
    color: $themeComColor;
  }
}

.map_card {
  position: fixed;
  left: 30rpx;
  right: 30rpx;
  bottom: 30rpx;
  z-index: 99999;
}
</style>