<template>
    <BasePopup :show="isShowProgress" @close="close" :maskClose="false" mode="center" height="400" :closeable="cancel"
        width="690" radius="20">

        <view class="cent_progres">
            <view class="center_pro_title">

                {{ titles }}进度
            </view>
            <view class="center_pro_top">

                <view><text>总设备数：</text>{{ total||0 }}</view>
                <view><text>已完成：</text>{{ finishNum||0 }}</view>
                <view v-if="!ok">正在操作：{{ device_sn||'' }}</view>
            </view>
            <view class="center_pro">
                <u-line-progress active-color="#2979ff" :percent="ProgressNum"></u-line-progress>
            </view>
            <view class="center_pro_bottom" v-if="ok">

                <text>总数量: {{ total||0 }} </text>
                <text>已完成: {{ finishNum||0 }} </text>
                <text>未完成: {{ (total - finishNum)||0 }} </text>
                <text>失败: {{ failNum||0 }}</text>
            </view>
            <view class="center_pro_btn">
                <BaseButton type="primary" @onClick="timeOt" :disapble="timeOut">取消</BaseButton>
            </view>
        </view>

    </BasePopup>
</template>

<script>
import BasePopup from "./BasePopup.vue";
import BaseButton from "./BaseButton.vue";
export default {
    name: "BaseProGress",
    components: {
        BaseButton,
        BasePopup,
    },
    props: {
        total: {
            type: [Number, String], //按钮宽度
            default: 0,
        },
        titles: {
            type: String,
            default: '批量启动'
        }
    },
    // 在子组件中声明 message 属性，用于接收父组件传递的参数
    mounted() {
        this.isShowProgress = false
        // 监听来自父组件的名为 "customEvent" 的事件
        // console.log('自定义事件监听',this.total)
        uni.$off('customEvent');
        uni.$off('customEventfalse');
        uni.$on('customEvent', this.handleCustomEvent);
        uni.$on('customEventfalse', this.customEventfalse);
    },
    beforeDestroy() {
        // 在组件销毁前，移除事件监听
        this.isShowProgress = false
    },
    data() {
        return {
            finishNum: 0,//已经完成的设备数
            device_sn: '',//正在操作的设备编号
            cancel: false,//控制取消按钮显示
            failNum: 0,//失败的数
            ProgressNum: 0,//进度
            timeOut: false,//暂停
            ok: false,//完成
            isShowProgress: false,
            isBind: false,
            errorList: []

        };
    },
    methods: {
        customEventfalse() {
            this.isShowProgress = false
        },
        close() {
            this.$emit('errorChange', this.errorList);
            this.isShowProgress = false
            this.clear()
            if (this.isBind) {

                this.isShowErr("操作完成", 1, '', true);
            } else {
                this.isShowTwo("", 1);
            }

        },
        timeOt() {
            this.timeOut = true
            this.ok = true
            this.cancel = true

        },
        /* 批量管理 */
        async sendRequests(dataList, datas, interval, requestCallback, parame, requestCallbackTwo) {
            this.clear()

            for (const url of dataList) {
                if (!this.timeOut&&this.isShowProgress) {
                    this.device_sn = url.device_sn

                    let data = {
                        ...datas,
                        device_sn: this.device_sn,

                    }
                    let parames = {
                        ...parame,
                        device_sn: this.device_sn,
                    }
                    if (url.mid) {
                        data['mid'] = url.mid
                        parames['mid'] = url.mid
                    } else if (url.id) {
                        parames['mid'] = url.id
                    }
                    if (url.room_num) {
                        data['room_num'] = url.room_num
                        parames['room_num'] = url.room_num
                    }

                    try {
                        // 在这里处理请求的响
                        // const response = await this.$u.api.startUM(data)
                        await requestCallback(data);
                        if (parame) {
                            await requestCallbackTwo(parames);
                        }
                        this.finishNum++

                    } catch (error) {
                        // 在这里处理请求的错误
                        console.error(`请求 ${url} 失败，错误信息:`, error);
                        this.failNum++
                        this.errorList.push(url)
                    }
                    let time = this.ProgressNum * 1 + Math.ceil(100 / this.total)
                    if (time >= 100) {
                        this.ProgressNum = 100
                        this.cancel = true
                        this.ok = true
                    } else {
                        this.ProgressNum = time * 1
                    }
                    // 在每个请求之后等待一段时间
                    await this.sleep(interval);

                }
            }

        },
        // 辅助函数：等待指定的时间
        sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        },
        clear() {
            this.finishNum = 0
            this.ProgressNum = 0
            this.failNum = 0
            this.cancel = false
            this.timeOut = false
            this.ok = false
            this.errorList = []
        },
        handleCustomEvent(ataList, data, insave, requestCallback, isBind, parames, requestCallbackTwo) {

            this.isShowProgress = true
            this.isBind = isBind
            this.sendRequests(ataList, data, insave, requestCallback, parames, requestCallbackTwo);
        },
    },
    options: { styleIsolation: "shared" }, //组件必须加,才能修改内部样式
};
</script>

<style lang="scss" scoped>
.cent_progres {
    padding: 0 30rpx;
    margin-top: 28rpx;

    .center_pro_title {
        text-align: center;
    }

    .center_pro_top {
        // display: flex;
        width: 100%;

        // flex-wrap: wrap;
        view {
            margin: 5rpx 0;

            // flex: 1;
            // border: 1px solid #000;
            // width:100%;
            text {
                width: 100rpx;
            }
        }
    }

    .center_pro_bottom {
        display: flex;

        >text {
            // border: 1px solid #000;
            margin-right: 25rpx;
        }
    }

    .center_pro {
        // height: 18rpx;
        // border: 1px solid #000;
        // display: flex;
        // justify-content: center;
        margin-bottom: 10rpx;
    }

    .center_pro_btn {
        // border: 1px solid #000;
        margin: 20rpx 0;
    }
}


.defaultStyle {
    ::v-deep .u-btn {
        height: 88rpx;
        font-size: $font-size-xlarge;

        &::after {
            border: none;
        }
    }
}

.primary {
    ::v-deep .u-btn {
        color: $textWhite;
        background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
    }
}

.eixt {
    ::v-deep .u-btn {
        color: $textWhite;
        background: linear-gradient(268deg, #ff2919 0%, #ff5223 99%);
    }
}

.default {
    ::v-deep .u-btn {
        color: $textBlack;
        background: $uni-bg-color;
        border: 2rpx solid #c8c8c8;
    }
}

.theme {
    ::v-deep .u-btn {
        color: $textWhite;
        background: $themeComColor;
        font-size: $font-size-middle;
        height: 78rpx;
    }
}
</style>