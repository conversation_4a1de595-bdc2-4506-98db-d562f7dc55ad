<template>
    <view class="RemoteEquipment">
        <BaseNavbar :title="title"></BaseNavbar>
        <view class="remote">
            <!-- 顶部区域 -->

            <!-- 内容区域 -->
            <view class="remote_content">
                <!-- 内容区域顶部 -->
                <!-- <view class="content_top"> </view> -->
                <view class="remote_top" v-if="type == 1">
                    <view><text class="text">旧主板号：</text>{{ motherboard_sn }}</view>
                    <view class="m_t">新主板号：
                        <input type="text" class="input" v-model="motherboard_new_sn">
                    </view>
                </view>
                <view class="remote_top" v-else>
                    <view>设备码：{{ device_sn }}</view>
                    <view>旧二维码：{{ device_sn }}</view>
                    <view class="m_t">新二维码：
                        <input type="text" class="input" v-model="vCode">
                    </view>
                </view>
                <!-- 内容区域扫码部分 -->
                <view class="content_center">
                    <view @click="sweep()">
                        <view class="bilud">
                            <img src="./../static/img/sweep.png" alt="" />
                        </view>
                        <view>
                            <test class="center_2">扫一扫绑定二维码</test>
                        </view>
                    </view>
                </view>

                <!-- 内容区域内容咧白哦部分 -->
                <view class="content_btn">
                    <view>
                        <BaseButton type="eixt" @onClick="clicke()">绑定二维码</BaseButton>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseButton from '@/components/base/BaseButton.vue'
import { getUrlParams, getUrlDynamicData } from '@/common/tools/utils.js'
import BaseProGressList from '@/components/base/BaseProGressList.vue'
/* #ifdef H5 */
import wx from 'weixin-js-sdk'
/* #endif */
export default {
    components: {
        BaseNavbar,
        BaseButton,
        BaseProGressList,
    },
    data() {
        return {
            device_sn: '',
            vCode: '',
            motherboard_sn:'',
            motherboard_new_sn:'',
            type:1,
        }
    },

    methods: {
        /* 防抖 */
        setTime() {
            let timestamp = new Date().valueOf()
            if (timestamp - this.lastOrderTime < 5000) {
                uni.showToast({
                    title: `其他指令正在执行请等${5 - ((timestamp - this.lastOrderTime) / 1000).toFixed(0) + 1
                        }秒后进行其他操作`,
                    icon: 'none',
                    duration: 1500,
                })
                // this.lastOrderTime = timestamp;
                return false
            } else {
                this.lastOrderTime = timestamp
                return true
            }
        },

        /*  扫码 */
        sweep() {
            /* #ifndef H5 */
            uni.scanCode({
                success: async ({ result, scanType, charSet, path }) => {
                    let systematicCode
                    let vscode = ''
                    let device_sn = ''
                    let mid = ''
                    if (result.includes('vscode')) {
                        systematicCode = getUrlParams(result, 'vscode')
                        vscode = systematicCode
                    } else if (result.includes('device_sn')) {
                        systematicCode = getUrlDynamicData(result, 'device_sn')
                        device_sn = systematicCode
                    } else if (result.includes('mid')) {
                        systematicCode = getUrlDynamicData(result, 'mid')
                        mid = systematicCode
                    } else {
                        return this.promptError()
                    }

                    // let data = {
                    //     mid:'1',
                    //     device_sn:systematicCode
                    // }
                    // this.dataList.push(data)

                    let data = {
                        vscode,
                        device_sn,
                        mid,
                    }

                    // this.getUMDdetail({ ...data })
                    this.vCode = systematicCode
                    console.log('扫描二位码解析的信息', systematicCode)
                },
                fail: (error) => { },
            })
            /* #endif */
            /* #ifdef H5 */
            if (typeof AlipayJSBridge != 'undefined') {
                let that = this
                AlipayJSBridge.call(
                    'scan',
                    {
                        type: 'qr', // 扫描类型  qr 二维码  / bar 条形码
                        // actionType: "scanAndRoute",// 如果只是扫码,拿到码中的内容，这项不用设置都可以
                    },
                    (res) => {
                        // alert(JSON.stringify(res));
                        if (res.error == 10) {
                            // 错误码为10：用户取消操作
                            Toast('取消操作')
                        } else if (res.error == 11) {
                            // 错误码为11：扫码失败
                            Toast('网络异常，请重试')
                        } else {
                            // res.codeContent为扫码返回的结果
                            // window.location.replace(res.codeContent)
                            let systematicCode
                            let result = decodeURIComponent(res.codeContent)
                            let data = {
                            }
                            if (result.includes('vscode')) {
                                // alert(1)
                                systematicCode = data['vscode'] = getUrlParams(result, 'vscode')
                                // alert(data.vscode)
                            } else if (result.includes('device_sn')) {
                                // alert(2)
                                systematicCode = data['device_sn'] = getUrlDynamicData(result, 'device_sn')
                                // alert(data.device_sn)
                            } else if (result.includes('mid')) {
                                // alert(2)
                                systematicCode = data['mid'] = getUrlDynamicData(result, 'mid')
                                // alert(data.mid)
                            } else {
                                return that.promptError()
                            }
                            this.vCode = systematicCode

                            // that.getUMDdetail({ ...data })

                        }
                    },
                )
            } else if (typeof WeixinJSBridge != 'undefined') {
                let uri = location.href.split('#')[0]
                // let uri = this.vUrl
                let data = {
                    url: uri,
                }
                let that = this

                this.$u.api.getJsSign(data)
                    .then((res) => {
                        wx.config({
                            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                            appId: res.appId, // 必填，公众号的唯一标识
                            timestamp: res.timestamp, // 必填，生成签名的时间戳
                            nonceStr: res.nonceStr, // 必填，生成签名的随机串
                            signature: res.signature, // 必填，签名
                            jsApiList: ['scanQRCode'], // 必填，需要使用的JS接口列表, 这里只需要调用扫一扫
                        })
                        wx.ready(function () {
                            wx.scanQRCode({
                                needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                                scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
                                success: (res) => {
                                    // 扫码成功，跳转到二维码指定页面（res.resultStr为扫码返回的结果）
                                    // window.location.replace(res.resultStr);
                                    setTimeout(() => {
                                        // window.location.replace(res.resultStr)
                                        let result = decodeURIComponent(res.resultStr)
                                        let systematicCode
                                        if (result.includes('vscode')) {
                                            systematicCode = data['vscode'] = getUrlParams(result, 'vscode')
                                        } else if (result.includes('device_sn')) {
                                            systematicCode = data['device_sn'] = getUrlDynamicData(result, 'device_sn')
                                        } else if (result.includes('mid')) {
                                            systematicCode = data['mid'] = getUrlDynamicData(result, 'mid')
                                        } else {
                                            return that.promptError()
                                        }
                                        // that.getUMDdetail({ ...data })
                                        this.vCode = systematicCode
                                    }, 20)
                                },
                            })
                        })
                    })
                    .catch((err) => {
                        console.log('错误结果', err)
                    })
            } else {
                // uni.navigateTo({
                //   url: `/pagesD/Scan/Scan?from=batch&hotel_id=${this.hotel_id}`,
                // })
                alert('请使用微信打浏览器打开')
            }
            /* #endif */
        },
        //二维码错误提示
        promptError() {
            this.isShowErr('请扫码正确的二维码,二维码信息')
        },
        //绑定二维码
        async clicke() {
            if (!this.vCode) {
                this.isShowErr('请扫码正确的二维码,二维码信息')
            } else {
                try {
                    let data = {
                        vsCode: this.vCode,
                        device_sn: this.device_sn
                    }
                    await this.$u.api.bindPseudoCode(data)
                    this.isShowSuccess('操作成功',1)
                } catch (error) {
                    console.log('错误信息', error)
                }
            }
            // uni.setStorageSync("bindId_key", data);
        },

    },
    onLoad(opt) {
        console.log(opt, '>>>>>>>>>>>>>>>>>')
        if (opt?.from == 'device') {
            this.device_sn = opt.device_sn
        }
    },
    onShow() {
        /*  #ifndef H5 */
        let pages = getCurrentPages()
        let currPage = pages[pages.length - 1] // 当前页
        if (currPage.data.isDoRefresh == true) {
            // 是否刷新
            this.isShowTwo('', 1)
        }
        if (currPage.data.item) {
            // 有值
            // 修改listData中值
            this.checkHotel = currPage.data.item
            this.hotelNewName = this.checkHotel.hotelName
            this.hotel_new_id = this.checkHotel.id
        }
        /*#endif */
        /*  #ifdef H5 */
        if (this.vCurrPage.isDoRefresh == true) {
            // 是否刷新
            this.isShowTwo('', 1)
        }
        this.checkHotel = this.vCurrPage.item
        this.hotelNewName = this.checkHotel.hotelName
        this.hotel_new_id = this.checkHotel.id
        /*#endif */

    },
}
</script>
<style lang="scss">
page {
    background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.input{
    width: 420rpx;
    border: 1rpx solid #bebdbd;
    padding: 0 !important;

}
.RemoteEquipment {
    width: 100%;
    min-height: 100vh;
    box-sizing: border-box;
    overflow: auto;

    #specialEffect {
        transition: all 1.2ms ease-in;
    }

    #specialEffect:hover {
        background: linear-gradient(100deg, #46a1ff 0%, rgb(255, 91, 91) 100%);
    }

    .remote {
        width: 100%;
        height: 100%;
        padding: 10rpx 25rpx 0rpx 25rpx;
        box-sizing: border-box;

        .remote_top {
            // width: 500rpx;
            // padding: 20rpx 40rpx;
            width: 90%;
            margin: 30rpx auto;
            color: #666;
            // display: flex;
            margin-top: 20rpx;
            border-radius: 15rpx;
            font-size: 40rpx;
            padding: 15rpx 0;
            // border: 1px solid #000;
            // background-color: #e7e7e7;
            .text{
                width: 200rpx; 
            }
        }
        .m_t{
            margin-top: 20rpx;
            display: flex;
        }
        .content_center {
            // border: 1px solid #000;
            display: flex;
            justify-content: center;
            align-items: center;

            >view {
                font-size: 28rpx;
                font-weight: bolder;
                // border: 1px solid #000;
                text-align: center;
            }
        }

        .remote_txtt {
            margin-bottom: 10rpx;
            overflow-wrap: normal;
        }

        .content_sweep {
            margin: 20rpx 0;
            // border: 1px solid #000;
            height: 550rpx;
            padding: 15rpx;
            display: flex;
            text-align: center;
            // flex-wrap: wrap;
            // overflow-y: auto;
            justify-content: center;

            // align-items: center;
            // border: 1px solid #000;
        }

        .content_main {
            margin: 10rpx 0;
            // border: 1px solid #000;
            padding: 15rpx;
            overflow: scroll;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-content: flex-start;

            >view {
                height: 80rpx;
                border-radius: 15rpx;
                // flex:0 1 0;
                width: 330rpx;
                margin: 10rpx 0;
                // border: 1px solid #000;
                background-color: $pageBgColor;
                padding: 10rpx 10rpx;
                padding-left: 25rpx;
                font-size: 22rpx;
                position: relative;

                .content_icon {
                    position: absolute;
                    right: -7px;
                    top: -8px;
                }

                .content_lt {
                    position: absolute;
                    left: 0px;
                    top: 0px;
                    background-color: red;
                    // border: 1px solid #000;
                    font-size: 15rpx;
                    width: 40rpx;
                    height: 40rpx;
                    padding: 5rpx 5rpx;
                    color: white;

                    clip-path: polygon(0 0, 100% 0, 0 100%, 0 0);
                }

                .content_rt {
                    position: absolute;
                    right: 0px;
                    top: 0px;
                    // background-color: red;
                    // border: 1px solid #000;
                    font-size: 13rpx;
                    width: 120rpx;
                    height: 80rpx;
                    padding-left: 15rpx;
                    padding-top: 5rpx;
                    color: white;

                    // clip-path: polygon(100% 0, 100% 0, 100% 100%, 0 0);
                    image {
                        width: 100%;
                        height: 100%;
                    }
                }


                .greenb {
                    background-color: $themeComColor;
                    color: white;
                }

                .redt {
                    color: red;
                }

                .redb {
                    background-color: red;
                    color: white;
                }
            }

            .red {
                color: red;
            }

            .bacred {
                width: 25rpx;
                height: 15rpx;
                background-color: red;
                color: white;
                font-size: 15rpx;
            }

            .comten_mb {
                border-bottom: 2rpx solid rgb(219, 219, 219);
            }
        }

        .content_btn {
            width: 500rpx;
            margin-top: 25rpx;
            margin-bottom: 45rpx;
            margin: 50rpx auto;
        }

        .remote_content {
            width: 100%;
            min-height: 600rpx;
            max-height: auto;
            background: rgb(255, 255, 255);
            z-index: 1;
            border-radius: 20rpx;
            box-shadow: rgba(#333, 20%);
            overflow: hidden;
            margin-top: 15rpx;
            position: relative;

            img {
                width: 300rpx;
                height: 300rpx;
            }
        }

        .remote_txtt {
            font-size: 40rpx;
            font-weight: bolder;
        }
    }
}

.greent {
    color: $themeComColor;
}

.center_2 {
    padding: 10rpx 20rpx;
    border-radius: 15rpx;
    background-color: #46a1ff;
    color: #fff;
}

.flex2 {
    display: flex;
    justify-content: space-between;
    padding: 0 20rpx;
    margin-top: 15rpx;

    .title2 {
        color: #46a1ff;
    }
}

.flex {
    margin-top: 15rpx;
    display: flex;
    align-items: center;
    padding: 0 20rpx;

    .title1 {
        color: #46a1ff;
        font-size: 40rpx;
        font-weight: bold;
        margin-right: 20rpx;
    }

    .title2 {
        color: #333;
        font-size: 35rpx;
        font-weight: bold;
    }
}

.input {
    // border: 2rpx solid red;
    height: 80rpx;
    justify-content: space-between;
    padding: 0 20rpx;

    .img {
        height: 80%;
        width: 100rpx;
    }

    .test {
        background-color: #f2f2f2;
        padding: 20rpx;
        display: flex;
        align-items: center;
        width: 250rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #333;
        justify-content: center;
        border-radius: 10rpx;
    }

    .greent {
        color: #9a9a9a !important;
    }
}

.default {
    text-align: center;
    font-size: 26rpx;
}
</style>