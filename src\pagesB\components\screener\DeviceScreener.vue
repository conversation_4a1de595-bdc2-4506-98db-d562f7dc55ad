<template>
  <view class="screener">
    <view class="screener_list">
      <view class="screener_list_item">
        <view class="list_item">{{ vPointName }}</view>
        <view class="list_item">
          <BaseInput
            v-model="hotelName"
            @onClick="selectHotel"
            :placeholder="'请输入'+vPointName+'名称'"
            :disabled="true"
          />
        </view>
      </view>
      <view class="screener_list_item" >
        <view class="list_item">位置</view>
        <view class="list_item">
          <BaseInput v-model="roomNum" placeholder="请输入位置" />
        </view>
      </view>
      <view class="screener_list_item">
        <view class="list_item">拥有者</view>
        <view class="list_item">
          <BaseInput v-model="owner" placeholder="请输入拥有者/上级账号" />
        </view>
      </view>
      <view class="screener_list_item">
        <view class="list_item">设备编号</view>
        <view class="list_item">
          <BaseInput v-model="deviceSn" placeholder="请输入设备编号" />
        </view>
      </view>
      <view v-if="isShowScreenSn" class="screener_list_item">
        <view class="list_item">屏幕编号</view>
        <view class="list_item">
          <BaseInput v-model="screenSn" placeholder="请输入屏幕编号" />
        </view>
      </view>
      <view class="screener_list_item">
        <view class="list_item">设备状态</view>
        <view class="list_item">
          <BaseInput
            placeholder="请选择设备状态"
            type="select"
            :list="deviceStatusList"
            :selectValue="selectDeviceStatus.label"
            :index="selectDeviceStatus.value"
            @confirm="confirmDeviceStatus"
          />
        </view>
      </view>
      <view v-if="isShowBind" class="screener_list_item">
        <view class="list_item">绑定状态</view>
        <view class="list_item">
          <BaseInput
            placeholder="请选择绑定状态"
            type="select"
            :list="bindStatusList"
            :selectValue="selectBindStatus.label"
            :index="selectBindStatus.value"
            @confirm="confirmBindStatus"
          />
        </view>
      </view>
    </view>
    <view class="btn">
      <BaseButton :width="330" type="default" @onClick="resetData"
        >重置</BaseButton
      >
      <BaseButton :width="330" type="primary" @onClick="confirm"
        >确认</BaseButton
      >
    </view>
  </view>
</template>

<script>
import BaseInput from "@/components/base/BaseInput.vue";
import TimeSelect from "@/components/common/TimeSelect.vue";
import BaseButton from "@/components/base/BaseButton.vue";
export default {
  name: "DeviceScreener", //订单筛选
  props: {
    isShowBind: { type: Boolean, default: false },
    isShowScreenSn: { type: Boolean, default: false },
  },
  components: { BaseInput, TimeSelect, BaseButton },
  data() {
    return {
      hotelName: "",
      roomNum: "",
      owner: "",
      deviceSn: "",
      screenSn: "",
      hotel_id: "",
      
      deviceStatusList: [
        {
          value: 0,
          label: "全部",
        },
        {
          value: 1,
          label: "在线",
        },
        {
          value: 3,
          label: "离线",
        },
      ],
      selectDeviceStatus: {},
      bindStatusList: [
        {
          value: 0, //不显示
          label: "全部",
        },
        {
          value: 1, //已绑定
          label: "已绑定",
        },
        {
          value: 2, //未绑定
          label: "未绑定",
        },
      ],
      selectBindStatus: {},
    };
  },
  methods: {
    selectHotel() {
      uni.navigateTo({
        url: `/pagesB/place/SelectPlace?from=home_device_bind_hotel`,
      });
    },
 
    resetData() {
      this.hotel_id = "";
      this.hotelName = "";
      this.roomNum = "";
      this.owner = "";
      this.deviceSn = "";
      this.screenSn = "";
      this.selectDeviceStatus = this.deviceStatusList[0];
      this.selectBindStatus = this.bindStatusList[0];
    },
    confirm() {
      let data = {
        dianwei: this.hotel_id,
        room_num: this.roomNum,
        owner: this.owner,
        device_sn: this.deviceSn,
        status: this.selectDeviceStatus.value,
        is_bind_hotel: this.selectBindStatus.value,
        screen_sn: this.screenSn,
      };
      this.$emit("confirm", data);
    },
    confirmDeviceStatus(e) {
      this.selectDeviceStatus = e[0];
    },
    confirmBindStatus(e) {
      this.selectBindStatus = e[0];
    },
  },
  created() {
    this.selectDeviceStatus = this.deviceStatusList[0];
    this.selectBindStatus = this.bindStatusList[0];
  },
};
</script>

<style lang="scss" scoped>
.screener {
  padding: 50rpx 30rpx 30rpx;
  &_list {
    &_item {
      display: flex;
      align-items: center;
      .list_item {
        margin-bottom: 30rpx;
        &:first-child {
          width: 150rpx;
          margin-right: 20rpx;
          white-space: nowrap;
          font-size: $font-size-base;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: $textBlack;
        }
        &:last-child {
          width: 100%;
        }
      }
    }
  }
.screener_list_item{
  .list_item:nth-child(1){
    text-align: justify;
    height: 40rpx; /* 固定高度 */
    line-height: 40rpx; /* 与高度相同 */
    overflow: hidden; /* 防止溢出 */

    /* 优化后的伪元素方案 */
    &::after {
      content: '';
      display: inline-block;
      width: 100%;
      height: 0; /* 消除高度影响 */
      vertical-align: top; /* 对齐顶部 */
    }
  }
}
  .btn {
    display: flex;
    justify-content: space-between;
  }
}
</style>
