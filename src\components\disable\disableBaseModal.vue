<template>
    <view>
        <BaseModal :show.sync="newShow" @cancel="cancel" :content="index != 4 ? '您确定要启用该设备吗' : ''"
      :title="index != 4 ? '温馨提示' : ''" @confirm="confirmDelOff">
            <view>
                <view class="title">请选择要禁用的理由</view>
                <BaseRadio :radioIndex.sync="radioIndex" :list="reasonList" />
                <BaseInput v-if="radioIndex == 4" v-model="reason" placeholder="请输入禁用理由" />
            </view>
        </BaseModal>
    </view>
</template>
  
<script>

import BaseModal from "@/components/base/BaseModal.vue";
import BaseInput from "@/components/base/BaseInput.vue";
import BaseRadio from "../../components/base/BaseRadio.vue";
export default {
    components: {
        BaseModal,
        BaseInput,
        BaseRadio
    },
    data() {
        return {
            reasonList: [{
                title: "没电量了",
                name: "0",
                disabled: false,
                selectIndex: 0,

            }, {

                title: "无网络",
                name: "1",
                disabled: false,
                selectIndex: 1,

            }, {
                title: "没泡泡液",
                name: "2",
                disabled: false,
                selectIndex: 2,


            }, {

                title: "其他故障",
                name: "3",
                disabled: false,
                selectIndex: 3,

            }, {
                title: "其他理由",
                name: "4",
                disabled: false,
                selectIndex: 4,

            }],
            radioIndex: 0,
            reason: ''
        }
    },
    name: "disableBaseModal",
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        index:{
            type:[String,Number],
            default:4
        }
    },
    computed: {
        newShow: {
            get: function () {
                return this.show;
            },
            set: function (val) {
                return this.$emit("update:show", val);
            },
        },
    },
    methods: {
        confirmDelOff() {

            let title = ''

           
            if (this.radioIndex == 4) {
                title = this.reason
            } else {
                title = this.reasonList[this.radioIndex].title
            }
            this.$emit('confirmDelOff', title)
            // this.$emit('confirmDelOff');
        },
        cancel() {
            this.radioIndex = 0,
            this.reason = ''
           
            this.$emit('cancel')
        }
    },
};
</script>
  
<style lang="scss" scoped>
.title{
    margin-bottom:20rpx;
}
</style>