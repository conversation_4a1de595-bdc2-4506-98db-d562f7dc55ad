<template>
  <view class="wrap">
    <view class="flex">
      <view class="flex-right">
        <PageCard
          :listData="listData"
          :current="current"
          :id="id"
          :idFrom="idFrom"
          :isFromReplenish="isFromReplenish"
          :isFromDevice="isFromDevice"
          :loadingType="loadingType"
          @change="change"
        />
      </view>
    </view>

    <view
      class="preserve"
      :style="{ paddingBottom: vIphoneXBottomHeight + 'rpx' }"
    >
      <view class="flexRowBetween">
        <u-button
          type="primary"
          :text="isFromReplenish ? '确认' : '保存'"
          @click="confirm"
        ></u-button>
      </view>
    </view>

    <!-- 删除确认弹窗 -->
    <u-modal
      v-model="isShowDelModal"
      title="提示"
      content="已有推荐套餐，是否替换？"
      show-cancel-button
      @confirm="confirmReplace"
      @cancel="isShowDelModal = false"
    ></u-modal>
  </view>
</template>

<script>
import PageCard from "./PageCard.vue"

const NO_SELECTION_MESSAGE = "请选择套餐"
const ALREADY_SELECTED_MESSAGE = "已经选择了该套餐，无需替换"

export default {
  name: "DevicePageCard",
  components: {
    PageCard,
  },
  props: {
    listData: {
      type: Array,
      default() {
        return []
      },
    },
    idFrom: { type: String, default: "id" },
    isFromReplenish: { type: Boolean, default: false },
    isFromDevice: { type: Boolean, default: false },
    loadingType: { type: Number, default: 0 },
  },
  data() {
    return {
      current: -1,
      id: "",
      isShowDelModal: false,
    }
  },
  computed: {
    // 点位名称
    vPointName() {
      return "点位"
    },
    // iPhone X 底部安全区域高度
    vIphoneXBottomHeight() {
      return 0
    }
  },
  methods: {
    change(e) {
      this.current = e.current
      this.id = e.id
    },
    confirmReplace() {
      this.isShowDelModal = false
      this.$emit("ok", this.id)
    },
    confirm() {
      if (!this.id) {
        uni.showToast({
          title: NO_SELECTION_MESSAGE,
          icon: "none",
        })
        return
      }

      const recommendedItems = this.listData.filter((item) => {
        return (
          (this.idFrom === "ms_id" && item.is_recommend === 1) ||
          (this.idFrom !== "ms_id" && item.h_is_recommend === 1)
        )
      })

      if (
        recommendedItems.length === 1 &&
        recommendedItems[0][this.idFrom] == this.id
      ) {
        uni.showToast({
          title: ALREADY_SELECTED_MESSAGE,
          icon: "none",
        })
        return
      }
      
      if (recommendedItems.length > 0) {
        return (this.isShowDelModal = true)
      }

      this.$emit("ok", this.id)
    },
  },
  mounted() {
    setTimeout(() => {
      const recommendedItems = this.listData.filter((item) => {
        return (
          (this.idFrom === "ms_id" && item.is_recommend === 1) ||
          (this.idFrom !== "ms_id" && item.h_is_recommend === 1)
        )
      })
      
      if (recommendedItems.length > 0) {
        this.id = recommendedItems[0][this.idFrom]
      }
    }, 0)
  },
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
.wrap {
  padding: 30rpx;
}

.flex {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.flex-right {
  width: 650rpx;
}

.preserve {
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  background-color: $uni-bg-color;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999999;

  .flexRowBetween {
    width: 100%;
    flex-wrap: wrap;
  }
}
</style>
