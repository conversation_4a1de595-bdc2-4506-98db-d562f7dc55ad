<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="content">
      <!-- 富文本 -->
      <view class="msg-title">
          {{ msgTitle }}
      </view>
      <u-parse :html="content" :user-select="true"></u-parse>
    </view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
export default {
  components: { BaseNavbar },
  data() {
    return {
      title: "系统消息",
      id: 0,
      content: "",
      msgTitle:'',
    };
  },
  methods: {
    readMessage() {
      let data = {
        ids: this.id,
        read_status:1
      };
      this.$u.api.readMessage(data).then((res) => {
        this.content = res.data;
      });
    },
  },
  onLoad(opt) {
    if (opt?.id) {
      this.id = opt.id;
      this.title = opt.title;
      this.msgTitle = opt.msg;
      this.readMessage();
    }
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 30rpx;
  font-size: 30rpx;
}
.msg-title{
  font-size: 35rpx;
  font-weight: 600;
  margin-bottom: 20rpx
}
</style>