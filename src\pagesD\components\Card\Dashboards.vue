<template>
  <view>
    <BaseNavbar title="销售额统计" />
    <BaseSearch :placeholder="placeholderName" @search="search" listType="dashboards" />
    <!-- <BaseList listType="dashboards" @searchChange="searchChange" /> -->
    <view class="subsection" @click="handleSubsection">
      <u-subsection mode="subsection" :active-color="mainColor" :list="subsectionList" :current="current"
        @change="changeSubsection" />
      <u-calendar v-model="showCalendar" mode="range" :safe-area-inset-bottom="true" btn-type="error"
        :range-color="mainColor" :active-bg-color="mainColor" :change-year="false"
        @change="changeCalendar"></u-calendar>
    </view>
    <view class="profit">
      <view class="profit-title">
        周期：{{ dateTime.startDate }} 至 {{ dateTime.endDate }}
      </view>
      <UCharts :lineData="lineData" :chartsName="chartsName" />
    </view>
    <view class="info-card">
      <view class="info-card-title" :style="{ marginBottom: loadingType === 3 ? '20rpx' : 0 }">
        {{ titleName }}：{{ total }}
      </view>
      <ComList :loadingType="loadingType" v-if="loadingType !== 3">
        <block v-if="cardType === 'device'">
          <ChargeOrderCard v-for="item in listData" :key="item.id" :info="item" />
        </block>
        <block v-else>
          <DataAnalysisCard v-for="item in listData" :key="item.id" :info="item" :cardType="cardType" />
        </block>
      </ComList>
      <view class="info-card-box" v-else>
        <BaseEmpty top="0" />
      </view>
    </view>
    <BaseBackTop @onPageScroll="onPageScroll" :scrollTop="scrollTop"></BaseBackTop>
  </view>
</template>
<script>
import BaseNavbar from '../../components/base/BaseNavbar.vue'
import BaseSearch from '../../components/base/BaseSearch.vue'
import UCharts from './components/UCharts.vue'
import BaseEmpty from '../../components/base/BaseEmpty.vue'
import ComList from '../../components/list/ComList.vue'
import myPull from '../../mixins/myPull'

import ChargeOrderCard from '../components/Cards/ChargeOrderCard.vue'
import DataAnalysisCard from '../components/Cards/DataAnalysisCard.vue'
import { subtractDaysAndFormat } from '@/wxutil/times'
import uCalendar from '@/components/uni-calendar/u-calendar.vue'
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from '@/wxutil/list'
import BaseBackTop from '@/components/base/BaseBackTop.vue'
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    UCharts,
    BaseEmpty,
    ComList,
    ChargeOrderCard,
    DataAnalysisCard,
    uCalendar,
    // BaseList,
    BaseBackTop,
  },
  mixins: [myPull()],
  data() {
    return {
      scrollTop: 0,
      subsectionList: [
        //     {
        //     name: '昨天'
        // },
        {
          name: '今天',
          status: 1,
        },
        {
          name: '昨天',
          status: 2,
        },

        {
          name: '前天',
          status: 3,
        },
        {
          name: '本月',
          status: 4,
        },

        {
          name: '上月',
          status: 5,
        },
        {
          name: '自定义',
          isCustom: true,
        },
      ],
      current: 0,
      showCalendar: false,
      lineData: {},
      mainColor: '#fa3534',
      dateTime: {
        //显示时间
        startDate: '',
        endDate: '',
      },
      startDate: '', //查询开始时间
      endDate: '', //查询结束时间
      queryDate: 7, //查询天数
      cardType: 'user', //user->用户数据，hotel->场地方数据，device->设备数据
      hotel_id: undefined,
      device_sn: '',
      total: 0, //总数量
      searchVal: '',
      hotelName: '', //搜索
    }
  },
  computed: {
    placeholderName() {
      if (this.cardType == 'user') {
        // return `请输入${this.isExamine ? "通过" : "拒绝"}原因`;
        return `请输入${this.vPointName}名称`
      } else if (this.cardType == 'hotel') {
        return '请输入设备编号'
      } else if (this.cardType == 'device') {
        return '请输入订单编号'
      }
      return '请输入提示'
    },
    titleName() {
      if (this.cardType == 'user') {
        // return `请输入${this.isExamine ? "通过" : "拒绝"}原因`;
        return `${this.vPointName}数量`
      } else if (this.cardType == 'hotel') {
        return '设备数量'
      } else if (this.cardType == 'device') {
        return '订单数量'
      }
    },
    chartsName() {
      if (this.cardType == 'user') {
        // return `请输入${this.isExamine ? "通过" : "拒绝"}原因`;
        return '我的销售额'
      } else if (this.cardType == 'hotel') {
        return `${this.vPointName}销售额`
      } else if (this.cardType == 'device') {
        return '设备销售额'
      }
    },
  },
  methods: {
    onPageScroll(e) {
      this.scrollTop = e.scrollTop
    },
    handleSubsection() {
      if (this.current === 5 && !this.showCalendar) this.showCalendar = true
    },
    handleData(date, startDate, endDate) {
      // this.queryDate = date
      this.startDate = startDate
      this.endDate = endDate
      this.refresh()
    },
    changeSubsection(i) {
      this.current = i
      let selectItem = this.subsectionList[i]
      if (selectItem.isCustom) {
        this.showCalendar = true
      } else {
        let date = new Date()
        if (selectItem.status < 4) {
          this.startDate = this.endDate = subtractDaysAndFormat(
            selectItem.status - 1,
          )
        } else if (selectItem.status == 4) {
          this.endDate =
            date.getFullYear() +
            '-' +
            (date.getMonth() + 1) +
            '-' +
            date.getDate()
          this.startDate =
            date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + 1
        } else if (selectItem.status == 5) {
          let currentDate = new Date();
          let lastMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);

          this.startDate = lastMonthDate.getFullYear() + '-' + (lastMonthDate.getMonth() + 1) + '-1';
          this.endDate = lastMonthDate.getFullYear() + '-' + (lastMonthDate.getMonth() + 1) + '-' + lastMonthDate.getDate();
        }//当前日期的年 2022
        this.handleData('', this.startDate, this.endDate)
      }
    },
    async getList(page, done) {
      try {
        let data = {
          page,
          limit: 10,
          type: this.cardType,
          date: this.queryDate,
          start_time: this.startDate,
          end_time: this.endDate,
          keyword: this.searchVal,
        }
        if (this.cardType === 'hotel') {
          data['hotel_id'] = this.hotel_id
        } else if (this.cardType === 'device') {
          data['device_sn'] = this.device_sn
        }

        let res = await this.$u.api.getUserReport(data)
        if (page == 1) {
          // 第一页的时候，才传递总数
          this.lineData = res.table
          this.dateTime = {
            startDate: this.lineData[0]?.time_point,
            endDate: this.lineData[this.lineData?.length - 1]?.time_point,
          }
          this.total = res.total ?? 0
        }
        done(res.data)
      } catch (error) {
        console.log(error)
      }

    },

    changeCalendar(e) {
      let { startDate, endDate } = e
      this.handleData('', startDate, endDate)
    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(val) {
      this.searchVal = val
      let list = AddValueInObject(this.vServerList.dashboards, val)
      this.$u.vuex(`vServerList.dashboards`, list)
      this.refresh()
    },
  },
  onLoad(opt) {
    console.log('🚀 ~ opt，placeList', opt.from == 'placeList')
    this.cardType = opt?.cardType ?? 'user'
    this.hotel_id = opt?.hotel_id ?? undefined
    this.device_sn = opt?.device_sn ?? undefined

    this.endDate = this.startDate = subtractDaysAndFormat(0)
    this.handleData('', this.startDate, this.endDate)
  },
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
.subsection {
  background-color: #fff;
  padding: 20rpx;
}

.profit {
  &-title {
    padding: 20rpx;
    font-size: $font-size-middle;
    color: $textDarkGray;
  }
}

.info-card {
  &-title {
    padding: 20rpx;
    padding-bottom: 0;
    font-size: $font-size-middle;
    color: $textBlack;
    font-weight: 700;
  }

  &-box {
    padding: 20rpx;
    background-color: #fff;
  }
}
</style>
