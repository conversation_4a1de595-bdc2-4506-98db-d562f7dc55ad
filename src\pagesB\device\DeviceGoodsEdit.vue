<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="content">
      <view>
        <view class="title" v-if="false">当前选择商品</view>
        <BaseInput v-if="false" placeholder="请选择商品" rightText="arrow" v-model="goodsName" :disabled="true"
          @onClick="selectGoods" />
        <image class="goods-img" :src="goodsImg" />
      </view>
      <view>
        <view class="title">套餐名称</view>
        <BaseInput placeholder="请输入商品名" v-model="aliName" />
      </view>
      <view>
        <view class="title">{{ goodsInfo.shop_type == 1 ? '价格' : '起步价格' }}</view>
        <BaseInput placeholder="请输入价格" rightText="元" v-model="goodsPrice" />
      </view>
      <view>
        <view class="title">{{ goodsInfo.shop_type == 1 ? '会员价格' : '起步会员价格' }}</view>
        <BaseInput placeholder="请输入价格" rightText="元" v-model="vipPrice" />
      </view>
      <block v-if="goodsInfo.shop_type == 2">
        <view>
          <view class="title">预付总金额</view>
          <BaseInput placeholder="请输入价格" rightText="元" v-model="h_pre_pay_money" />
        </view>
      </block>


      <view v-if="false">
        <view class="title">最大库存</view>
        <BaseInput placeholder="请输入最大库存" v-model="goodsDefaultStock" />
      </view>
      <view v-if="false">
        <view class="title">单位</view>
        <BaseRadio :list="unitListRadio" :radioIndex.sync="selectUnitRadio" />
      </view>
      <block v-if="false">
        <view>
          <view class="title">免费长度(厘米/cm)</view>
          <BaseInput placeholder="请输入免费长度" v-model="towelNorms.free_lenth" />
        </view>
        <view>
          <view class="title">购买长度(厘米/cm)</view>
          <BaseInput placeholder="请输入购买长度" v-model="towelNorms.pay_lenth" />
        </view>
      </block>
      <!-- <block v-if="selectUnitRadio === 3">
        <view>
          <view class="title">游戏时长</view>
          <BaseInput
            placeholder="请输入游戏时间"
            v-model="game_time"
          />
        </view>
      </block> -->
      <block>
        <view>
          <view class="title">{{ goodsInfo.shop_type == 2 ? '起步' : '' }}{{ goodsId == 5 ? '驾驶' : '骑行' }}时长</view>
          <BaseInput placeholder="请输入游戏时间" v-model="game_time" rightText="分钟" />
        </view>
        <block v-if="goodsInfo.shop_type == 2">
          <view>
            <view class="title">超起步价后</view>
            <BaseInput placeholder="请输入价格" rightText="元/分" v-model="h_pre_per_minute" />
          </view>
        </block>
        <view>
          <view class="title">是否热卖</view>
          <BaseRadio :radioIndex.sync="listIndex" @changes="changeList" :list="list" />
        </view>
      </block>
      <view class="btn">
        <BaseButton type="primary" @onClick="confirm">确 认 修 改</BaseButton>
      </view>
    </view>
  </view>
</template>

<script>
import BaseButton from '../../components/base/BaseButton.vue'
import BaseInput from '../../components/base/BaseInput.vue'
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import BaseRadio from '../../components/base/BaseRadio.vue'
export default {
  components: { BaseNavbar, BaseButton, BaseInput, BaseRadio },
  data() {
    return {
      h_pre_per_minute: 1, //预付总金额
      h_pre_pay_money: 1, //超过金额
      title: '编辑套餐',
      goodsInfo: {},
      trackNum: Number, //套餐号
      goodsName: '',
      aliName: '',
      goodsPrice: 1,
      vipPrice: 0,
      goodsDefaultStock: '',
      goodsId: 1,
      device_sn: '', //设备编号
      isFromTemplatePlace: false, //来自商品模板

      original_img: '',
      unitListRadio: [
        {
          title: '数量',
          name: 1,
          selectIndex: 0,
        },
        {
          title: '长度(CM)',
          name: 2,
          selectIndex: 1,
        },
        {
          title: '时间(分钟)',
          name: 3,
          selectIndex: 2,
        },
      ],
      list: [
        {
          title: '否',
          name: 1,
          listIndex: 0,
        },
        {
          title: '是',
          name: 1,
          listIndex: 1,
        },

      ],
      listIndex: 0,
      selectUnitRadio: 3,
      towelNorms: {
        free_lenth: 0, //免费长度
        pay_lenth: 0, //购买长度
      },
      game_time: 1, // 游戏时长
      is_recommend: 0

    }
  },
  computed: {
    goodsImg() {
      return this.original_img || require('@/static/img/errorLoad.png')
    },
  },
  methods: {
    changeList(e) {
      this.listIndex = e
    },
    async confirm() {
      // console.log('起步条件',this.h_pre_per_minute,this.h_pre_pay_money*1+this.goodsPrice*1,this.h_pre_pay_money<(this.h_pre_per_minute*1+this.goodsPrice*1))
      if (this.game_time <= 0) {
        return this.isShowErr('骑行时长要大于0')
      } else if (this.goodsInfo.shop_type == 2&&this.h_pre_per_minute <= 0) {
        return this.isShowErr('超起步价要大于0')
      } else if (this.goodsInfo.shop_type == 2&&this.h_pre_pay_money <= 0) {
        return this.isShowErr('预付总金额要大于0')
      } else if (this.goodsPrice <= 0) {
        return this.isShowErr('价格要大于0')
      } 
      if (this.goodsInfo.shop_type == 2&&this.h_pre_pay_money<(this.h_pre_per_minute*1+this.goodsPrice*1)) {
        return this.isShowErr('预付总金额小于起步价加超起步价')
      }

      if (this.isFromTemplatePlace) {
        /*  #ifndef H5 */
        let pages = getCurrentPages()
        // let currPage = pages[pages.length - 1] //当前页面
        let prevPage = pages[pages.length - 2] //上一个页面
        //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
        prevPage.setData({
          item: {
            num: Number(this.trackNum),
            goods_id: this.goodsId,
            good_price: this.goodsPrice || 1,
            default_stock: Number(this.goodsDefaultStock) || 1,
            goods_name: this.aliName,

            original_img: this.original_img,
            // unitName: this.unitListRadio[this.selectUnitRadio - 1].title,
            unit: this.selectUnitRadio,
            free_lenth: this.towelNorms.free_lenth,
            pay_lenth: this.towelNorms.pay_lenth,
            game_time: this.game_time,
            vip_price: this.vipPrice,
            is_hot: this.listIndex,
            is_recommend: this.is_recommend,
            pre_pay_money: this.h_pre_pay_money,
            pre_per_minute: this.h_pre_per_minute,
          },
        })
        /*#endif */
        /*  #ifdef H5 */
        let data = {
          item: {
            num: Number(this.trackNum),
            goods_id: this.goodsId,
            good_price: this.goodsPrice || 1,
            vip_price: this.vipPrice||0,
            default_stock: Number(this.goodsDefaultStock) || 1,
            goods_name: this.aliName,

            original_img: this.original_img,
            // unitName: this.unitListRadio[this.selectUnitRadio - 1].title,
            unit: this.selectUnitRadio,
            free_lenth: this.towelNorms.free_lenth,
            pay_lenth: this.towelNorms.pay_lenth,
            game_time: this.game_time,
            is_hot: this.listIndex,
            is_recommend: this.is_recommend,
            pre_pay_money: this.h_pre_pay_money,
            pre_per_minute: this.h_pre_per_minute,

          },
        }
        this.vCurrPage.goodsInfo = data
        this.vCurrPage.isGetGoodsInfo = true
        /*#endif */
        console.log('prevPage', prevPage)
        uni.navigateBack({ delta: 1 })
      } else {
        let item = {
          num: Number(this.trackNum),
          good_id: this.goodsId,
          good_price: this.goodsPrice || 1,
          default_stock: Number(this.goodsDefaultStock) || 1,
          name: this.aliName,
          unit: this.selectUnitRadio,
          free_lenth: this.towelNorms.free_lenth,
          pay_lenth: this.towelNorms.pay_lenth,
          game_time: this.game_time,
          vip_price: this.vipPrice||0,
          is_hot: this.listIndex,
          is_recommend: this.is_recommend,
          pre_pay_money: this.h_pre_pay_money,
          pre_per_minute: this.h_pre_per_minute,
        }
        if (item.unit === 2 || item.unit === 3) {
          item = {
            ...item,
            ...this.towelNorms,
          }
        }
        let list = [item]
        let data = {
          device_sn: this.device_sn || '',
          items: list,
          delNotFound: false,
          shop_type: this.goodsInfo.shop_type,
        }
        try {
          await this.$u.api.saveUMShops(data)
          this.isShowSuccess('修改成功', 1, () => { }, true)
        } catch (error) {
          console.log('修改失败', error)
        }
      }
    },
    selectGoods() {
      uni.navigateTo({ url: `/pagesB/goods/GoodsList?from=deviceSelectGoods` })
    },
    setGoodsInfo() {
      this.goodsName = this.goodsInfo.goods_name
      this.aliName = this.goodsInfo.goods_name
      this.goodsPrice = this.goodsInfo.good_price||1
      if (this.goodsInfo.vip_price * 1) {
        this.vipPrice = this.goodsInfo.vip_price
      } else {
        this.vipPrice = ''
      }
      this.listIndex = this.goodsInfo.is_hot
      this.original_img = this.goodsInfo.original_img
      // this.goodsDefaultStock = this.goodsInfo.default_stock;
      // this.goodsId = this.goodsInfo.goods_id;
    },
  },
  onLoad(opt) {
    if (opt?.from == 'template_place') {
      this.isFromTemplatePlace = true
      /*  #ifndef H5 */
      let pages = getCurrentPages()
      // let currPage = pages[pages.length - 1] //当前页面
      let prevPage = pages[pages.length - 2] //上一个页面
      //直接调用上一个页面的setData()方法，把数据存到上一个页面中去
      prevPage.setData({
        item: {
        },
      })
      /*#endif */
      /*  #ifdef H5 */
      let data = {
        item: {
        },
      }
      this.vCurrPage.goodsInfo = data
      this.vCurrPage.isGetGoodsInfo = true
      /*#endif */
      console.log('prevPage', prevPage)
    }


    if (opt?.goodsInfo) {
      const decodedGoodsInfo = decodeURIComponent(opt.goodsInfo);
      this.goodsInfo = JSON.parse(decodedGoodsInfo);
      this.trackNum = opt.num
      this.device_sn = opt?.device_sn === 'undefined' ? '' : opt.device_sn
      this.setGoodsInfo()

      //设置单位单选框数据
      // this.selectUnitRadio = this.goodsInfo?.unit;
      this.towelNorms = {
        free_lenth: this.goodsInfo?.free_lenth, //免费长度
        pay_lenth: this.goodsInfo?.pay_lenth, //购买长度
      }
      console.log('传递的值', this.goodsInfo)
      this.is_recommend = this.goodsInfo?.is_recommend || 0 // 游戏时长
      this.game_time = this.goodsInfo?.game_time || 1 // 游戏时长

      this.game_time = this.goodsInfo?.game_time || 1 // 游戏时长
      this.h_pre_per_minute = this.goodsInfo?.pre_per_minute || 1 //超过每分钟多少钱
      this.h_pre_pay_money = this.goodsInfo?.pre_pay_money || 1 // 预付总金额
      this.vipPrice = this.goodsInfo?.vip_price || 0 // 会员价
      this.goodsPrice = this.goodsInfo?.good_price || 1 // 售价
    }
    this.goodsId = opt?.goodsId * 1 || 1

    // if(this.selectUnitRadio === "undefined" || this.selectUnitRadio == undefined ){
    //   this.selectUnitRadio = 1;
    // }
    // let data = {
    //   good_id: 1,
    // }
    try {
      // this.$u.api.getMyParentProducts(data).then((res) => {
      //   console.log('返回信息', res)
      //   this.original_img = res.data[0]?.original_img||''
      //   // this.$forceUpdate()
      // })
    } catch (err) {
      // console.log('返回错误信息', err)
    }
    // console.log('选择单位 2 ', this.selectUnitRadio, this.isFromTemplatePlace)
  },
  onShow(e) {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    console.log('isFromTemplatePlace', this.isFromTemplatePlace)
    if (currPage.data.isGetGoodsInfo) {
      currPage.data.isGetGoodsInfo = false
      this.goodsInfo = currPage.data.goodsInfo
      this.setGoodsInfo()
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isGetGoodsInfo) {
      this.vCurrPage.isGetGoodsInfo = false;
      this.goodsInfo = this.vCurrPage.goodsInfo;
      this.setGoodsInfo();
    }
    /*#endif */
  },
}
</script>

<style lang="scss" scoped>
.content {
  padding: 30rpx;

  .title {
    color: $textBlack;
    font-size: $font-size-middle;
    font-weight: bold;
    margin: 50rpx 0 20rpx;
  }

  .input {
    height: 80rpx;
    background-color: #f2f2f2;
  }

  .goods-img {
    width: 150rpx;
    height: 150rpx;
    border-radius: $imgRadius;
    // background-color: pink;
    margin-top: 30rpx;
  }

  .btn {
    margin-top: 50rpx;
  }
}
</style>
