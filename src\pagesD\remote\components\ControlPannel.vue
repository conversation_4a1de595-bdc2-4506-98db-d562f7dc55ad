<template>
  <view class="left-panel">
    <!-- 控制按钮组 -->
    <view class="button-group">
      <!-- wifi 状态显示 -->
      <view class="wifi-status" @click="handleWifiClick">
        <image
          class="wifi-icon"
          :src="wifiIcon"
          :class="{
            'wifi-connected': isDeviceConnected,
            'wifi-disconnected': !isDeviceConnected,
            breathing: isSearching,
          }"
        />
        <view
          class="wifi-text"
          :class="{
            'text-connected': isDeviceConnected,
            'text-disconnected': !isDeviceConnected,
          }"
        >
          {{ wifiStatusText }}
        </view>
      </view>
      <!-- 🎯 使用原生 picker 组件，支持10个选项 -->
      <!-- 主控制区域 -->
      <view class="main-controls">
        <view class="speed-control-group">
          <picker
            @change="onLowSpeedChange"
            :value="lowSpeedIndex"
            :range="lowSpeedOptions"
          >
            <view class="speed-button" :class="{ active: isLowSpeedActive }">
              中控低速
            </view>
          </picker>

          <picker
            @change="onHighSpeedChange"
            :value="highSpeedIndex"
            :range="highSpeedOptions"
          >
            <view class="speed-button"> 中控高速 </view>
          </picker>
        </view>
        <!-- 前进后退按钮组 -->
        <view class="movement-controls">
          <!-- 前进按钮 -->
          <view
            class="ctrl-btn forward"
            :class="{ active: isForwardActive }"
            @touchstart="forwardPress"
            @touchmove="handleForwardTouchMove"
            @touchend="forwardRelease"
            @touchcancel="forwardRelease"
          >
            <view class="button-background">
              <image
                src="../../static/前进.svg"
                :class="isForwardActive ? 'imagAc' : 'imag'"
                mode="aspectFit"
                alt="图标"
              />
              <view class="text">前进</view>
            </view>
          </view>
          <!-- 前退按钮 -->
          <!-- <button
            type="primary"
            :class="{ active: isForwardActive }"
            @touchstart="forwardPress"
            @touchend="forwardRelease"
            style="margin-bottom: 50rpx; margin-top: 50rpx"
          >
            前进按钮
          </button>
          <button
            type="primary"
            :class="{ active: isBackActive }"
            @touchstart="backPress"
            @touchend="backRelease"
          >
            后退按钮
          </button> -->
          <!-- 后退按钮 -->
          <view
            class="ctrl-btn back"
            :class="{ active: isBackActive }"
            @touchstart="backPress"
            @touchmove="handleBackTouchMove"
            @touchend="backRelease"
            @touchcancel="backRelease"
          >
            <view class="button-background">
              <image
                src="../../static/后退.svg"
                :class="isBackActive ? 'imagAc' : 'imag'"
                mode="aspectFit"
                alt="图标"
              />
              <view class="text">后退</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import toyCarBleUtil from "@/utils/toyCarBleUnifiedNew"
export default {
  props: {
    isForwardActive: Boolean, // 前进按钮状态
    isBackActive: Boolean, // 后退按钮状态
    bBleConnected: Boolean, // 蓝牙是否连接
    bluetoothOn: Boolean, // 手机蓝牙是否打开
    wxBluetoothAuth: Boolean, // 小程序蓝牙授权
    deviceSearchStatus: Object, // 🎯 设备搜索状态
  },
  data() {
    return {
      componentId: Math.random().toString(36).substr(2, 9), // 组件唯一标识
      forwardButtonRect: null, // 前进按钮位置信息
      backButtonRect: null, // 后退按钮位置信息
      isLowSpeedActive: false, // 低速按钮激活状态
      isHighSpeedActive: false, // 高速按钮激活状态
      lowSpeedValue: 25, // 低速值 (0-50)
      highSpeedValue: 75, // 高速值 (50-100)

      // 🎯 速度选择器数据（扩展到10个选项）
      lowSpeedOptions: [
        "10%",
        "20%",
        "30%",
        "40%",
        "50%",
        "60%",
        "70%",
        "80%",
        "90%",
        "100%",
      ],
      highSpeedOptions: [
        "50%",
        "55%",
        "60%",
        "65%",
        "70%",
        "75%",
        "80%",
        "85%",
        "90%",
        "95%",
      ],

      // 🎯 对应的数值数组
      lowSpeedValues: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
      highSpeedValues: [50, 55, 60, 65, 70, 75, 80, 85, 90, 95],

      // 🎯 picker 选择器的当前索引
      lowSpeedIndex: 0, // 默认选择10%（第一个选项）
      highSpeedIndex: 4, // 默认选择70%
    }
  },
  computed: {
    wifiStatusText() {
      // 🎯 优先判断权限状态
      if (!this.wxBluetoothAuth) {
        return "未授权"
      }
      if (!this.bluetoothOn) {
        return "蓝牙未打开"
      }

      // 🎯 蓝牙适配器未连接时，如果权限正常就显示正在搜索，否则显示未连接
      if (!this.bBleConnected) {
        if (this.wxBluetoothAuth && this.bluetoothOn) {
          return "正在搜索设备" // 权限正常，适配器正在初始化
        } else {
          return "未连接" // 权限或蓝牙有问题
        }
      }

      // 🎯 优化逻辑：考虑搜索时间，给搜索过程一些时间
      if (this.deviceSearchStatus) {
        if (this.deviceSearchStatus.deviceFound) {
          // 设备找到了，显示已连接
          return "已连接"
        } else if (this.deviceSearchStatus.isSearching) {
          // 正在搜索，简化逻辑：如果设备从未找到过，给足够时间搜索
          if (!this.deviceSearchStatus.deviceFound) {
            // 如果有lastFoundTime，计算搜索时间；否则认为刚开始搜索
            if (this.deviceSearchStatus.lastFoundTime) {
              const searchTime =
                Date.now() - this.deviceSearchStatus.lastFoundTime
              if (searchTime > 10000) {
                // 10秒后还没找到，显示未搜索到
                return "未搜索到设备"
              }
            }
            // 刚开始搜索或搜索时间不长，显示正在搜索
            return "正在搜索设备"
          } else {
            // 设备曾经找到过但现在离线，继续显示搜索
            return "正在搜索设备"
          }
        }
      }

      // 搜索还没开始或没有搜索状态，显示正在搜索
      return "正在搜索设备"
    },
    wifiIcon() {
      if (!this.bluetoothOn) {
        return require("../../static/wifi_of.svg")
      }
      if (!this.wxBluetoothAuth) {
        return require("../../static/wifi_of.svg")
      }
      if (!this.bBleConnected) {
        return require("../../static/wifi_of.svg")
      }

      // 🎯 优化逻辑：基于设备发现状态
      if (this.deviceSearchStatus && this.deviceSearchStatus.deviceFound) {
        // 设备曾经被找到过，显示连接图标
        return require("../../static/wifi.svg")
      }

      // 其他情况显示离线图标
      return require("../../static/wifi_of.svg")
    },

    // 🎯 设备是否真正连接（基于设备发现状态）
    isDeviceConnected() {
      if (!this.bluetoothOn || !this.wxBluetoothAuth || !this.bBleConnected) {
        return false
      }

      // 🎯 优化逻辑：基于设备是否曾经被找到
      return this.deviceSearchStatus && this.deviceSearchStatus.deviceFound
    },

    // 🎯 是否正在搜索设备（暂时不使用呼吸灯效果）
    isSearching() {
      return false // 暂时关闭呼吸灯效果
    },
  },
  watch: {
    // 🎯 监听关键 props 变化，强制更新
    wxBluetoothAuth: {
      handler(newVal) {
        // console.log("🔍 wxBluetoothAuth 变化:", newVal)
        this.$forceUpdate()
      },
      immediate: true,
    },
    bluetoothOn: {
      handler(newVal) {
        // console.log("🔍 bluetoothOn 变化:", newVal)
        this.$forceUpdate()
      },
      immediate: true,
    },
    bBleConnected: {
      handler(newVal) {
        // console.log("🔍 bBleConnected 变化:", newVal)
        this.$forceUpdate()
      },
      immediate: true,
    },
    deviceSearchStatus: {
      handler(newVal) {
        // console.log("🔍 deviceSearchStatus 变化:", newVal)
        this.$forceUpdate()
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    console.log(`🎯 ControlPanel 组件创建 [${this.componentId}]:`, {
      wxBluetoothAuth: this.wxBluetoothAuth,
      bluetoothOn: this.bluetoothOn,
      bBleConnected: this.bBleConnected,
      deviceSearchStatus: this.deviceSearchStatus,
    })
  },
  beforeDestroy() {
    console.log(`🎯 ControlPanel 组件销毁 [${this.componentId}]`)
  },
  methods: {
    // WiFi状态点击处理
    handleWifiClick() {
      this.$emit("reconnect-bluetooth")
    },

    // 前进按钮事件
    forwardPress() {
      this.getForwardButtonRect()
      this.$emit("forward-press")
    },
    forwardRelease() {
      this.forwardButtonRect = null
      this.$emit("forward-release")
    },

    // 前进按钮触摸移动处理
    handleForwardTouchMove(e) {
      if (!this.isForwardActive || !this.forwardButtonRect) return

      const touch = e.touches[0]
      const isInside = this.isPointInButton(
        touch.clientX,
        touch.clientY,
        this.forwardButtonRect
      )

      if (!isInside) {
        // 触摸点移出按钮区域，自动弹起
        console.log("🔄 前进按钮：触摸移出区域，自动弹起")
        this.forwardRelease()
      }
    },

    // 后退按钮事件
    backPress() {
      this.getBackButtonRect()
      this.$emit("back-press")
    },
    backRelease() {
      this.backButtonRect = null
      this.$emit("back-release")
    },

    // 后退按钮触摸移动处理
    handleBackTouchMove(e) {
      if (!this.isBackActive || !this.backButtonRect) return

      const touch = e.touches[0]
      const isInside = this.isPointInButton(
        touch.clientX,
        touch.clientY,
        this.backButtonRect
      )

      if (!isInside) {
        // 触摸点移出按钮区域，自动弹起
        console.log("🔄 后退按钮：触摸移出区域，自动弹起")
        this.backRelease()
      }
    },

    // 获取前进按钮的位置信息
    getForwardButtonRect() {
      const query = uni.createSelectorQuery().in(this)
      query
        .select(".ctrl-btn.forward")
        .boundingClientRect((rect) => {
          this.forwardButtonRect = rect
        })
        .exec()
    },

    // 获取后退按钮的位置信息
    getBackButtonRect() {
      const query = uni.createSelectorQuery().in(this)
      query
        .select(".ctrl-btn.back")
        .boundingClientRect((rect) => {
          this.backButtonRect = rect
        })
        .exec()
    },

    // 判断点是否在按钮区域内
    isPointInButton(x, y, buttonRect) {
      if (!buttonRect) return false

      const { left, top, right, bottom } = buttonRect
      return x >= left && x <= right && y >= top && y <= bottom
    },

    // 🎯 低速选择器变化处理
    async onLowSpeedChange(e) {
      const index = e.detail.value

      this.lowSpeedIndex = index
      const selectedValue = this.lowSpeedValues[index]
      this.lowSpeedValue = selectedValue
      this.isLowSpeedActive = true
      this.isHighSpeedActive = false
      console.log("🎯 低速选择器变化:", selectedValue)

      // 🎯 发送车面板低速指令
      await toyCarBleUtil.sendPanelLowSpeed(selectedValue)

      this.$emit("speed-change", selectedValue)

      uni.showToast({
        title: `低速: ${selectedValue}%`,
        icon: "none",
        duration: 1500,
      })
    },

    // 🎯 高速选择器变化处理
    async onHighSpeedChange(e) {
      const index = e.detail.value

      this.highSpeedIndex = index
      const selectedValue = this.highSpeedValues[index]
      this.highSpeedValue = selectedValue
      this.isHighSpeedActive = true
      this.isLowSpeedActive = false
      console.log("🎯 高速选择器变化:", selectedValue)

      // 🎯 发送车面板高速指令
      await toyCarBleUtil.sendPanelHighSpeed(selectedValue)

      this.$emit("speed-change", selectedValue)
      uni.showToast({
        title: `高速: ${selectedValue}%`,
        icon: "none",
        duration: 1500,
      })
    },
  },
}
</script>

<style scoped lang="scss">
.left-panel {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  // padding: 40rpx;
  margin-top: 120rpx;
  margin-left: 100rpx;
}
.text {
  font-size: 30rpx;
  color: white;
  font-weight: bold;
  position: relative;
  z-index: 10;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  margin-top: 10rpx;
}
.imagAc {
  width: 70rpx;
  height: 70rpx;
  /* 🎯 保持正方形比例，避免变形 */
  object-fit: contain;
  object-position: center;
  position: relative;
  z-index: 10;
  /* 激活状态红色高亮效果 */
  filter: brightness(1.5) saturate(2) hue-rotate(-10deg) contrast(1.3);
  transition: all 0.2s ease;
}

.imag {
  width: 70rpx;
  height: 70rpx;
  /* 🎯 保持正方形比例，避免变形 */
  object-fit: contain;
  object-position: center;
  position: relative;
  z-index: 10;
  transition: all 0.2s ease;
}
.button-background {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 5;
  width: 100%;
  height: 100%;
}
/* WiFi状态显示 */
.wifi-status {
  position: absolute;
  top: -130rpx; /* 调整位置，避免被导航栏遮挡 */
  left: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  box-sizing: border-box;
  cursor: pointer;
  z-index: 15; /* 提高层级，确保在导航栏之上 */

  .wifi-icon {
    width: 80rpx;
    height: 80rpx;
    transition: all 0.3s ease;

    &.wifi-connected {
      opacity: 1;
      filter: brightness(1.2);
    }

    &.wifi-disconnected {
      opacity: 0.6;
      filter: grayscale(0.5);
    }

    /* 🎯 呼吸灯效果 - 搜索设备时使用 */
    &.breathing {
      animation: breathing 2s ease-in-out infinite;
    }
  }

  .wifi-text {
    font-size: 24rpx;
    margin-top: 8rpx;
    text-align: center;
    transition: all 0.3s ease;

    &.text-connected {
      color: #4caf50;
      font-weight: bold;
    }

    &.text-disconnected {
      color: #999;
      font-weight: normal;
    }
  }
}

.button-group {
  height: 580rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 120rpx;
}

/* 主控制区域 - 前进后退和左右转在一行 */
.main-controls {
  display: flex;
  // flex-direction: column;
  align-items: center;
  gap: 30rpx;
  margin-top: 120rpx; /* 给WiFi状态留出空间 */
}

/* 前进后退按钮组 */
.movement-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40rpx;
  // margin-left: 20rpx;
}

/* 左右转向按钮组 */
.turn-controls {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20rpx;
}

.ctrl-btn {
  /* 保持原有样式 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 180rpx;
  font-size: 40rpx;
  font-weight: bold;
  color: white;
  background-color: rgba(128, 128, 128, 0.273);
  position: relative;
  /* 确保按钮内容在伪元素之上 */
  z-index: 5;
  /* 为伪元素定位 */
  overflow: hidden;
  /* 隐藏溢出效果 */
  border-radius: 12rpx;
  /* 🎯 确保触摸事件可靠 */
  pointer-events: auto;
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;

  /* 🎯 3D凸起效果 - 未按下状态 */
  box-shadow:
    /* 外部发光 */ 0 0 20rpx rgba(0, 255, 255, 0.3),
    /* 上方高光 */ 0 -4rpx 8rpx rgba(255, 255, 255, 0.1),
    /* 下方阴影 */ 0 6rpx 12rpx rgba(0, 0, 0, 0.4),
    /* 内部高光 */ inset 0 2rpx 4rpx rgba(255, 255, 255, 0.1),
    /* 内部阴影 */ inset 0 -2rpx 4rpx rgba(0, 0, 0, 0.2);

  border: 1rpx solid rgba(0, 255, 255, 0.6);

  /* 3D变换 */
  transform: translateY(0rpx);

  /* 添加流动光效动画 */
  &::before {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      45deg,
      transparent 25%,
      rgba(0, 255, 255, 0.2) 50%,
      transparent 75%
    );
    z-index: 0;
  }

  &::after {
    content: "";
    position: absolute;
    inset: 2rpx;
    border: 1rpx solid rgba(0, 255, 255, 0.3);
    border-radius: 10rpx;
    animation: pulse 2s ease-in-out infinite;
  }

  /* 🎯 快速响应的过渡效果 */
  transition: all 0.1s ease-out;
}

@keyframes flow {
  0% {
    transform: rotate(45deg) translateX(-50%);
  }

  100% {
    transform: rotate(45deg) translateX(50%);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* 🎯 呼吸灯动画 - WiFi搜索时使用 */
@keyframes breathing {
  0% {
    opacity: 0.4;
    transform: scale(1);
    filter: brightness(0.8);
  }

  50% {
    opacity: 1;
    transform: scale(1.1);
    filter: brightness(1.3);
  }

  100% {
    opacity: 0.4;
    transform: scale(1);
    filter: brightness(0.8);
  }
}

/* 激活状态高亮背景 */
.ctrl-btn.active {
  background-color: rgba(128, 128, 128, 0.8) !important;
  filter: brightness(1.6);
}

/* 🎯 速度控制组样式 */
.speed-control-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 40rpx;
  margin-left: 20rpx;
}

/* 🎯 picker 容器样式 */
.speed-picker-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

/* 🎯 速度按钮样式 */
.speed-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 80rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  background-color: rgba(128, 128, 128, 0.273);
  border-radius: 10rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  transition: all 0.2s ease;
  cursor: pointer;
  pointer-events: auto;
}

.speed-button:hover {
  background-color: rgba(128, 128, 128, 0.5);
  transform: scale(1.02);
}

.speed-button.active {
  background-color: rgba(255, 0, 0, 0.8) !important;
  filter: brightness(1.6);
}
</style>
