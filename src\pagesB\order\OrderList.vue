<template>
  <view>
    <view class="top">
      <BaseNavbar :title="title" />
      <PerformanceMonitor :duration="loadDuration" />

      <BaseSearch
        typeImg="screen"
        listType="orderList"
        placeholder="请输入订单编号/支付单号"
        @search="search"
        @onClickIcon="onClickIcon"
      />
      <!-- <BaseList listType="orderList" @searchChange="searchChange" /> -->
    </view>

    <scroll-view
      scroll-with-animation="true"
      style="width: 100%; height: 100%; position: relative"
    >
      <BaseTypeTab @change="tabChange" />
      <BaseDropdown
        :options-list="optionsList"
        :num="orderTotal"
        @change="changeDropdown"
        num-label="总订单数"
      ></BaseDropdown>
      <view class="subsection" @click="handleSubsection">
        <u-subsection
          mode="subsection"
          :active-color="mainColor"
          :list="subsectionList"
          :current="current"
          @change="changeSubsection"
        />
        <u-calendar
          v-model="showCalendar"
          mode="range"
          :safe-area-inset-bottom="true"
          btn-type="error"
          :range-color="mainColor"
          :active-bg-color="mainColor"
          :change-year="false"
          @change="changeCalendar"
        ></u-calendar>
      </view>

      <!-- 🔥 使用修复后的 OrderCard 组件，支持触底加载 -->
      <view class="order-list-container">
        <!-- 🔥 空状态显示 -->
        <view
          v-if="shouldShowEmptyState"
          class="empty-state"
          style="
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding-top: 100rpx;
          "
        >
          <image
            class="empty-image"
            src="/static/img/notYet.png"
            mode="aspectFit"
          />
          <text>暂无数据</text>
        </view>

        <!-- 🔥 订单列表 -->
        <template v-else>
          <!-- <OrderCard
          v-for="(item, index) in visibleData"
          :key="index"
          :order-data="item"
          @refund="retunOrder"
          @start="handleStart"
          @device-list="deviceList"
          @device-toggle="onOff"
        /> -->

          <!-- <KeepAlive></KeepAlive> -->
          <list class="order-list">
            <!-- 注意事项: 不能使用 index 作为 key 的唯一标识 -->
            <cell
              v-for="(item, index) in visibleData"
              :key="item.order_id"
              class="order-cell"
            >
              <view
                class="card"
                @click="cardClick(item)"
                style="box-shadow: none"
              >
                <view class="content">
                  <!-- 商品图片 -->
                  <image
                    class="img"
                    lazy-load="true"
                    :src="item.original_img || '/static/default-product.png'"
                    mode="aspectFill"
                  />

                  <!-- 退款标识 -->
                  <view class="imgdis" v-if="item.is_refund == 1">
                    <image
                      class="stock_icons"
                      src="@/pagesB/static/img/icon/refund.png"
                    />
                  </view>

                  <!-- 订单信息 -->
                  <view class="info flexColumnBetween">
                    <!-- 商品名称和时间 -->
                    <view class="goods_name flexRowBetween">
                      <view class="name">{{ item.good_name }}</view>
                      <view class="order-time">{{ item.add_time }}</view>
                    </view>

                    <!-- 数量/时长信息 -->
                    <view class="flexRowBetween">
                      <view>
                        <block v-if="item.prom_type === 8">
                          <view class="prepay-info">
                            <text
                              >实际时长:{{
                                item.realoutnum >= 0 ? item.realoutnum : 0
                              }}分钟</text
                            >
                            <text>起步时长: {{ item.pre_min_time }}分钟</text>
                            <text
                              >超时时长:{{
                                item.realoutnum - item.pre_min_time > 0
                                  ? item.realoutnum - item.pre_min_time
                                  : "0"
                              }}分钟</text
                            >
                          </view>
                        </block>
                        <block v-else>
                          <text>{{
                            item.unit === 3 ? "时长：" : "数量："
                          }}</text>
                          <text class="num">{{
                            item.unit === 3 ? item.outnum + "分钟" : item.outnum
                          }}</text>
                        </block>
                      </view>
                    </view>

                    <!-- 价格和状态 -->
                    <view class="flexRowBetween">
                      <view>
                        <block v-if="item.prom_type === 8">
                          <view class="prepay-pricing">
                            <text>实收:</text>
                            <text :style="{ color: 'red' }"
                              >¥{{
                                item.pay_status == 0 ? "0" : item.order_amount
                              }}</text
                            >
                            <text>起步: ¥{{ item.pre_min_price }}</text>
                            <text
                              >超时: ¥{{
                                item.pay_status == 0
                                  ? "0"
                                  : item.order_amount - item.pre_min_price > 0
                                  ? (
                                      item.order_amount - item.pre_min_price
                                    ).toFixed(2)
                                  : "0"
                              }}</text
                            >
                          </view>
                        </block>
                        <block v-else>
                          <text>价格：</text>
                          <text class="price">￥{{ item.order_amount }}</text>
                        </block>
                      </view>
                      <view
                        class="status flexRowAllCenter"
                        :style="{ width: '90rpx' }"
                      >
                        <image
                          v-if="item.order_status == 1 && item.is_refund == 0"
                          class="status_icon"
                          src="@/pagesB/static/img/icon/finish_icon.png"
                        />
                        <view
                          :style="{
                            color:
                              item.order_status == 1 && item.is_refund == 0
                                ? '#0EADE2'
                                : 'red',
                          }"
                        >
                          {{ getOrderStatus(item) }}
                        </view>
                      </view>
                    </view>

                    <!-- 预付信息 -->
                    <view class="myprepay-pricing" v-if="item.prom_type === 8">
                      <view class="prepay-row">
                        <text>预付: ¥{{ item.total_amount || "0.00" }}</text>
                        <text
                          >还车方式:{{ item.end_order_error_str || "" }}</text
                        >
                      </view>
                    </view>
                  </view>
                </view>

                <!-- 操作按钮 -->
                <view
                  class="flexButton"
                  v-if="
                    (item.is_show_refund == 1 && item.is_refund == 0) ||
                    item.order_status == 2
                  "
                >
                  <view class="returns" @click.stop="retunOrder(item)"
                    >退款</view
                  >
                  <view class="returns" @click.stop="handleStart(item)"
                    >远程启动</view
                  >
                  <view class="returns" @click.stop="deviceList(item)"
                    >启动其他</view
                  >
                  <view
                    class="returns"
                    :style="{
                      background:
                        item.use_status == 1
                          ? 'linear-gradient(268deg, #ff5b5b 0%,  #e61f1f 99%)'
                          : '',
                    }"
                    @click.stop="onOff(item)"
                  >
                    {{ item.use_status == 1 ? "禁用" : "启用" }}设备
                  </view>
                </view>

                <!-- 展开/收起按钮 -->
                <view
                  class="flexBottom border_top"
                  @click.stop="toggleDetails(item)"
                >
                  <view></view>
                  <view class="flexBottom">
                    <view :style="{ color: '#0EADE2' }"> 查看详情 </view>
                    <!-- <view
                      class="arrow"
                      :class="item.showDetails ? 'rotate' : ''"
                    >
                      <text class="arrow-icon">{{
                        item.showDetails ? "▲" : "▼"
                      }}</text>
                    </view> -->
                    <!-- <view
                      class="arrow"
                      :class="item.showDetails ? 'rotate' : ''"
                    >
                      <BaseIcon
                        color="#0EADE2"
                        :name="!item.showDetails ? 'arrow-down' : 'arrow-up'"
                        size="28"
                      />
                    </view> -->
                  </view>
                </view>

                <!-- 详细信息 -->
                <view v-show="item.showDetails" class="details">
                  <view>
                    <view>点位名称</view>
                    <view>{{ item.hotel_name }}</view>
                  </view>
                  <view v-if="item.addressDetail">
                    <view class="flex_white">详细位置</view>
                    <view class="textMaxTwoLine">{{ item.addressDetail }}</view>
                  </view>
                  <view>
                    <view>{{ item.unit === 3 ? "货道" : "货道" }}</view>
                    <view>{{ item.channel || "01" }}</view>
                  </view>
                  <view>
                    <view>设备编号</view>
                    <text user-select>{{ item.device_sn }}</text>
                  </view>
                  <view>
                    <view>订单编号</view>
                    <text user-select>{{ item.order_sn }}</text>
                  </view>
                  <view>
                    <view>支付单号</view>
                    <text user-select>{{ item.transaction_id }}</text>
                  </view>
                  <view v-if="item.pay_name == '汇付支付'">
                    <view>商户单号</view>
                    <text user-select>{{ item.party_order_id || "" }}</text>
                  </view>
                  <view v-if="item.pay_name == '汇付支付'">
                    <view>交易单号</view>
                    <text user-select>{{ item.out_trans_id || "" }}</text>
                  </view>
                  <view>
                    <view>买家名称</view>
                    <view>{{ item.buyer || item.openid }}</view>
                  </view>
                  <view>
                    <view>支付方式</view>
                    <view>{{ item.pay_name }}</view>
                  </view>
                  <view v-if="item.rake_back_platfrom">
                    <view>返利平台</view>
                    <view>{{
                      item.rake_back_platfrom === "xh"
                        ? "小程序平台"
                        : item.rake_back_platfrom || ""
                    }}</view>
                  </view>
                  <view>
                    <view>订单类型</view>
                    <view>{{ getOrderType(item) }}</view>
                  </view>
                  <view>
                    <view>交易时间</view>
                    <view>{{ item.add_time }}</view>
                  </view>
                  <block v-if="item.prom_type === 8">
                    <view>
                      <view>还车方式</view>
                      <view>{{ item.end_order_error_str || "" }}</view>
                    </view>
                    <view>
                      <view>预付</view>
                      <view>¥{{ item.total_amount }}</view>
                    </view>
                  </block>
                </view>
              </view>
            </cell>
          </list>

          <!-- 🔥 触底加载状态显示 -->
          <!-- <view class="load-more-status">
          <view v-if="loadingType === 1" class="loading">
            <text class="loading-text">正在加载更多...</text>
          </view>
          <view v-else-if="loadingType === 2" class="no-more">
            <text class="no-more-text">没有更多数据了</text>
          </view>
        </view> -->
          <!-- 🔥 触底加载状态显示 - 使用与ComLoadMore一致的样式 -->
          <ComLoadMore
            v-if="loadingType != 3 && loadingType != -1"
            :loading-type="loadingType"
          />
        </template>
      </view>
      <BasePopup :show.sync="isShowPopup" mode="top" :customStyle="customStyle">
        <view
          style="background-color: white; width: 100%; height: 140rpx"
        ></view>
        <OrderScreener
          @confirm="confirmQuery"
          @reset="handleReset"
          style="padding-top: 100rpx"
        />
      </BasePopup>
      <BaseModal
        :show.sync="isShowDelModalFree"
        @confirm="confirmDelReturn"
        title="温馨提示"
      >
        <view class="red">
          确认要退款给该用户吗，退款后将无法追回,
          <br />
          订单号{{ unBindItem.order_sn }},订单时间{{ unBindItem.add_time }}
        </view>
        <view class="title">1.请选择要退款的理由</view>
        <view class="modalTop">
          <BaseRadio
            :radioIndex.sync="radioIndex"
            :size="20"
            :list="reasonList"
          />
          <BaseInput
            v-if="radioIndex == 5"
            v-model="reason"
            placeholder="请输入其他理由"
          />
        </view>
        <view class="title">2.请选择要退款方式</view>
        <view>
          <u-radio-group
            v-model="value"
            @change="radioGroupChange"
            class="flexBottom"
          >
            <u-radio
              @change="radioChange"
              class="flexBottom_item"
              v-for="(item, index) in list"
              :key="item.order_id"
              :name="item.name"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </u-radio>
          </u-radio-group>
        </view>
      </BaseModal>
      <BaseModal :show.sync="isShowStartModal" @confirm="confirmStart">
        <view slot="default">
          <BaseInput v-model="length_time" placeholder="请输入启动时长(分钟)" />
        </view>
      </BaseModal>
      <disableBaseModal
        :show="isShowDelModalOff"
        @confirmDelOff="confirmDelOff"
        @cancel="cancel"
        :index="index"
      >
      </disableBaseModal>
      <!-- <BaseBackTop v-if="showBackTop" listShow @onScroll="onScroll">
      </BaseBackTop> -->
      <view v-show="showBackTop" @click="backTop" class="backTopbtn">
        <image class="image" src="@/static/img/icon/top.png" />
      </view>
    </scroll-view>
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue"
import BaseSearch from "@/components/base/BaseSearch.vue"
import BaseDropdown from "@/components/base/BaseDropdown.vue"
import ComList from "@/components/list/ComList.vue"
import OrderListCard from "@/pagesB/components/cards/OrderListCard.vue"
import OrderCard from "@/components/order/OrderCard.vue"
import myPull from "@/mixins/myPull.js"
import BasePopup from "@/components/base/BasePopup.vue"
import OrderScreener from "./components/OrderScreener.vue"
import BaseModal from "@/components/base/BaseModal.vue"
import BaseInput from "@/components/base/BaseInput.vue"
import disableBaseModal from "@/components/disable/disableBaseModal"
import { subtractDaysAndFormat } from "@/wxutil/times"
import uCalendar from "@/components/uni-calendar/u-calendar.vue"
// import BaseList from '@/components/base/BaseList.vue'
import { AddValueInObject } from "@/wxutil/list"
import BaseBackTop from "@/components/base/BaseBackTop.vue"
import BaseRadio from "../../components/base/BaseRadio.vue"
import BaseTabs from "@/components/base/BaseTabs.vue"
import BaseTypeTab from "@/components/base/BaseTypeTab.vue"
import ComLoadMore from "@/components/list/ComLoadMore.vue"
import BaseIcon from "@/components/base/BaseIcon.vue"
import PerformanceMonitor from "@/components/common/PerformanceMonitor .vue"
import performanceMixin from "@/mixins/performanceMixin"
export default {
  components: {
    BaseNavbar,
    BaseSearch,
    BaseDropdown,
    ComList,
    OrderListCard,
    OrderCard,
    BasePopup,
    OrderScreener,
    BaseModal,
    BaseInput,
    disableBaseModal,
    uCalendar,
    // BaseList,
    BaseBackTop,
    BaseRadio,
    BaseTabs,
    BaseTypeTab,
    ComLoadMore,
    BaseIcon,
    PerformanceMonitor,
  },
  data() {
    return {
      title: "订单管理",
      // 虚拟滚动配置
      virtualScrollOptions: {
        itemHeight: 180, // 每个订单项预估高度
        bufferSize: 5, // 缓冲区大小
        visibleCount: 10, // 可见区域数量
      },
      // 虚拟滚动配置
      virtualScrollConfig: {
        itemHeight: 180, // 预估每个订单项高度
        bufferSize: 5, // 缓冲区大小
        visibleCount: 10, // 可见区域数量
      },
      // 性能监控
      performance: {
        loadStart: 0,
        loadEnd: 0,
        memoryBefore: {},
        memoryAfter: {},
        fps: 0,
      },
      optionsList: [
        {
          title: "全部",
          options: [
            { label: "全部", value: 0, status: 0 },
            {
              label: "订单完成",
              value: 1,
              status: 1,
            },
            {
              label: "订单异常",
              value: 2,
              status: 2,
            },
            {
              label: "订单取消",
              value: 3,
              status: 3,
            },
            {
              label: "已退款",
              value: 4,
              status: 50,
            },
            {
              label: "待付款",
              value: 5,
              status: 4,
            },
          ],
          value: 0,
        },
      ],
      dianwei: "",
      device_sn: "",
      order_sn: "",
      order_status: 0,
      room_num: "",
      start_time: "",
      end_time: "",
      orderTotal: 0, //订单总数
      //popup显示状态
      isShowPopup: false,
      selectItem: {},
      isShowDelModalFree: false,
      list: [
        {
          name: "原路返回",
          disabled: false,
        },
        {
          name: "线下退款",
          disabled: false,
        },
      ],
      value: "原路返回",
      subsectionList: [
        //     {
        //     name: '昨天'
        // },
        {
          name: "今天",
          status: 1,
        },
        {
          name: "昨天",
          status: 2,
        },

        {
          name: "前天",
          status: 3,
        },
        {
          name: "本月",
          status: 4,
        },

        {
          name: "上月",
          status: 5,
        },
        {
          name: "自定义",
          isCustom: true,
        },
      ],
      current: 0,
      showCalendar: false,
      mainColor: "#fa3534",
      unBindItem: {},
      list: [
        {
          name: "原路返回",
          disabled: false,
        },
        {
          name: "线下退款",
          disabled: false,
        },
      ],
      value: "原路返回",

      isShowDelModalOff: false,
      isShowStartModal: false, //启动
      index: 4,
      selectIndex: 0,
      length_time: 1,
      hotelName: "", //搜索
      reasonList: [
        {
          title: "泡泡液已用完",
          name: "0",
          disabled: false,
          selectIndex: 0,
        },
        {
          title: "时间没到就停止",
          name: "1",
          disabled: false,
          selectIndex: 1,
        },
        {
          title: "没泡泡液",
          name: "2",
          disabled: false,
          selectIndex: 2,
        },
        {
          title: "启动失败",
          name: "3",
          disabled: false,
          selectIndex: 3,
        },
        {
          title: "没有电了",
          name: "4",
          disabled: false,
          selectIndex: 4,
        },
        {
          title: "其他理由",
          name: "5",
          disabled: false,
          selectIndex: 5,
        },
      ],
      radioIndex: 0,
      reason: "",
      customStyle: {
        top: 110 + this.vStatusBarHeight + this.vNavBarHeight + "rpx",
      },
      curTabIndex: 0,
      tabList: [],
      product_id: 0,
      prom_type: 0,
      showBackTop: false, // 控制显示状态
      scrollTimer: null, // 滚动计时器
      isScrollingToTop: false, // 标志是否正在滚动到顶部
      isPageLoading: false, // 页面加载状态
      isPageReady: false, // 🔥 页面是否准备就绪
      isRefreshing: false, // 🔥 防止重复刷新的标志
      disableAutoLoad: true, // 🔥 临时禁用自动加载
      isLoadingMore: false, // 🔥 防止连续触发加载更多
      cachedFirstPageData: null, // 🔥 只缓存第一页数据
      _isDestroying: false, // 🔥 页面销毁标志，用于阻止异步操作

      // 🔥 核心数据属性（之前缺失）
      listData: [], // 🔥 订单列表数据
      pagesData: [], // 🔥 分页数据
      page: 1, // 🔥 当前页码
      pageSize: 10, // 🔥 每页数据量
      loadingType: -1, // 🔥 加载状态

      // 🔥 虚拟滚动相关
      visibleStartIndex: 0, // 可见区域开始索引
      visibleEndIndex: 20, // 可见区域结束索引
      itemHeight: 180, // 每个订单项高度
      containerHeight: 600, // 容器高度

      // 🔥 渐进式渲染优化（安卓设备）
      isProgressiveRender: false, // 是否启用渐进式渲染
      progressiveRenderIndex: 0, // 渐进式渲染当前索引
      progressiveRenderTimer: null, // 渐进式渲染定时器
    }
  },
  computed: {
    // 🔥 支持触底加载的数据渲染：渲染所有已加载数据
    visibleData() {
      if (!this.listData || this.listData.length === 0) {
        return []
      }

      // 🔥 渲染所有已加载的数据，确保用户能滚动到底部触发加载更多
      // 性能优化通过其他方式实现，不限制数据显示
      return this.listData
    },

    // 🔥 简单判断：请求的数据没有就显示默认图片
    shouldShowEmptyState() {
      return !this.listData || this.listData.length === 0
    },
  },
  methods: {
    backTop() {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 2000,
      })
    },
    // 获取内存信息
    getMemoryInfo() {
      try {
        if (performance && performance.memory) {
          return {
            usedJSHeapSize:
              Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) +
              "MB",
            totalJSHeapSize:
              Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) +
              "MB",
            jsHeapSizeLimit:
              Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) +
              "MB",
          }
        }
        if (uni && uni.getSystemInfoSync) {
          const systemInfo = uni.getSystemInfoSync()
          return {
            platform: systemInfo.platform,
            system: systemInfo.system,
            memory: systemInfo.memory || "N/A",
          }
        }
        return { message: "无法获取内存信息" }
      } catch (error) {
        return { error: error.message }
      }
    },

    // 打印性能信息
    logPerformance() {
      const memInfo = this.getMemoryInfo()
    },

    handleReset() {
      // 重置筛选条件
      this.dianwei = ""
      this.device_sn = ""
      this.order_sn = ""
      this.start_time = subtractDaysAndFormat(0) // 今天
      this.end_time = subtractDaysAndFormat(0) // 今天
      this.prom_type = 0 // 重置为"全部"
      this.order_status = 0 // 重置订单状态筛选（全部）

      // 重置分段器选中状态为"今天" (index=0)
      this.current = 0

      // 刷新列表
      this.refresh()
    },

    tabChange(e) {
      this.product_id = e.id
      this.refresh()
    },

    // 🔥 极简 refresh 方法：直接重新加载第一页
    refresh(forceRefresh = false) {
      if (this.isRefreshing) return
      this.isRefreshing = true

      // 确保使用当前日期范围，特别是"今天"的情况
      if (this.current === 0) {
        const today = subtractDaysAndFormat(0)
        this.start_time = today
        this.end_time = today
      }

      // 🔥 简单直接：重置到第一页，重新加载
      this.page = 1
      this.listData = []
      this.pagesData = []
      this.loadingType = -1

      this.getList(this.page, (data) => {
        this.__pulldone(data)
        this.isRefreshing = false
      })
    },

    // 🔥 恢复正常的触底加载功能
    onBottom() {
      // 正常的触底加载逻辑
      if (
        this.loadingType !== 0 ||
        this.isScrollingToTop ||
        this.isLoadingMore
      ) {
        return
      }

      this.isLoadingMore = true
      this.loadingType = 1
      this.getList(this.page, this.__pulldone)
    },

    // 🔥 极简 __pulldone 方法：只缓存第一页
    __pulldone(data) {
      // 🔥 如果页面正在销毁，立即返回
      if (this._isDestroying) {
        return
      }

      if (!data || data.length === 0) {
        this.loadingType = this.listData.length === 0 ? 3 : 2
        this.isLoadingMore = false
        uni.stopPullDownRefresh()
        return
      }

      if (this.page === 1) {
        this.pagesData = [data]
        // 🔥 只缓存第一页数据
        this.cachedFirstPageData = {
          data: [...data],
          timestamp: Date.now(),
          timeRange: {
            current: this.current,
            start_time: this.start_time,
            end_time: this.end_time,
          },
        }
      } else {
        this.pagesData.push(data)
      }

      this.listData = [].concat(...this.pagesData)

      // 正常的触底加载逻辑
      this.loadingType = data.length < this.pageSize ? 2 : 0

      // 🔥 重置加载状态
      this.isLoadingMore = false

      uni.stopPullDownRefresh()
      this.page++
    },

    // 🔥 极速回到顶部：直接显示第一页，无滚动效果
    onScroll() {
      // 立即隐藏按钮
      this.showBackTop = false

      // 🔥 检查是否有第一页缓存
      const hasFirstPageCache =
        this.cachedFirstPageData &&
        this.cachedFirstPageData.timeRange.current === this.current &&
        this.cachedFirstPageData.timeRange.start_time === this.start_time &&
        this.cachedFirstPageData.timeRange.end_time === this.end_time

      if (hasFirstPageCache) {
        // 🔥 直接显示第一页缓存数据，无滚动效果
        this.pagesData = [this.cachedFirstPageData.data]
        this.listData = [...this.cachedFirstPageData.data]
        this.page = 1
        this.loadingType = 0

        // 🔥 直接跳转到顶部，无动画
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0, // 无动画
        })

        // 🔥 后台静默更新第一页数据
        setTimeout(() => {
          this.silentRefreshData()
        }, 500)
      } else {
        // 没有缓存，重新加载第一页
        this.page = 1
        this.loadingType = -1
        this.pagesData = []
        this.listData = []

        // 直接跳转到顶部
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0,
        })

        // 重新加载第一页
        this.refresh()
      }
    },

    // 🔥 简化的后台静默刷新：只更新第一页
    async silentRefreshData() {
      if (this._isDestroying) {
        return
      }

      try {
        let data = {
          dianwei: this.dianwei,
          device_sn: this.device_sn,
          room_num: this.room_num,
          start_time: this.start_time,
          end_time: this.end_time,
          order_sn: this.order_sn,
          order_status: this.order_status,
          product_id: this.product_id,
          prom_type: this.prom_type,
          page: 1,
        }

        let res = await this.$u.api.getOrderList(data)

        if (res.data) {
          // 🔥 只更新第一页数据和缓存
          this.cachedFirstPageData = {
            data: [...res.data],
            timestamp: Date.now(),
            timeRange: {
              current: this.current,
              start_time: this.start_time,
              end_time: this.end_time,
            },
          }

          // 如果当前显示的是第一页，则更新显示
          if (this.page === 1) {
            this.pagesData = [res.data]
            this.listData = [...res.data]
            this.orderTotal = res.total
            this.loadingType = res.data.length < (this.pageSize || 10) ? 2 : 0
          }
        }
      } catch (error) {}
    },

    // 🔥 删除复杂的缓存方法，简化逻辑

    // 🔥 判断当前是否为实时数据类型
    isLiveDataType() {
      // 🔥 "今天"的数据认为是实时数据，需要及时更新
      if (this.current === 0) {
        return true
      }

      // 🔥 如果有搜索条件，也认为是实时查询
      if (this.order_sn || this.dianwei || this.device_sn) {
        return true
      }

      // 🔥 如果不是"全部"状态的订单，也可能需要实时更新
      if (this.order_status !== 0) {
        return true
      }

      // 🔥 其他情况（昨天、前天、本月、上月等）认为是历史数据
      return false
    },

    // 🔥 隐藏缓存提示
    hideCacheHint() {
      this.showCacheHint = false
    },

    // 🔥 强制刷新最新数据
    forceRefresh() {
      this.showCacheHint = false

      // 清除第一页缓存
      this.cachedFirstPageData = null

      // 强制刷新
      this.refresh()
    },

    // 🔥 删除不需要的缓存键生成方法

    //   🔥 极简缓存策略：只缓存第一页数据
    saveToCache() {
      // 🔥 只缓存第一页数据，提升性能
      if (this.page === 1 && this.pagesData && this.pagesData.length > 0) {
        this.cachedFirstPageData = {
          data: [...this.pagesData[0]], // 只保存第一页数据
          timestamp: Date.now(),
          timeRange: {
            current: this.current,
            start_time: this.start_time,
            end_time: this.end_time,
          },
        }
      }

      // 🔥 不需要清理全量缓存，因为我们已经移除了它
    },

    // 🔥 智能加载第一页数据：优先使用缓存，后台更新
    async loadLatestFirstPage() {
      // 🔥 检查是否有相同时间范围的缓存数据
      const isCacheValid =
        this.cachedFirstPageData &&
        this.cachedFirstPageData.timeRange.current === this.current &&
        this.cachedFirstPageData.timeRange.start_time === this.start_time &&
        this.cachedFirstPageData.timeRange.end_time === this.end_time

      if (isCacheValid) {
        // 立即显示缓存的第一页数据
        this.pagesData = [this.cachedFirstPageData.data]
        this.listData = [...this.cachedFirstPageData.data]
        this.page = 2
        this.loadingType = 0

        // 🔥 移除强制更新，让Vue自动处理
        // this.$forceUpdate()

        // 后台静默更新最新数据
        this.silentUpdateFirstPage()
      } else {
        // 没有有效缓存，直接请求
        this.page = 1
        this.loadingType = -1
        this.pagesData = []
        this.listData = []

        try {
          let data = {
            dianwei: this.dianwei,
            device_sn: this.device_sn,
            room_num: this.room_num,
            start_time: this.start_time,
            end_time: this.end_time,
            order_sn: this.order_sn,
            order_status: this.order_status,
            product_id: this.product_id,
            prom_type: this.prom_type,
            page: 1,
          }

          let res = await this.$u.api.getOrderList(data)

          if (res.data) {
            this.pagesData = [res.data]
            this.listData = [...res.data]
            this.orderTotal = res.total
            this.page = 2
            this.loadingType = res.data.length < (this.pageSize || 10) ? 2 : 0

            // 缓存这次的数据
            this.cachedFirstPageData = {
              data: [...res.data],
              timestamp: Date.now(),
              timeRange: {
                current: this.current,
                start_time: this.start_time,
                end_time: this.end_time,
              },
            }
          } else {
            this.loadingType = 3
          }

          // 🔥 移除强制更新，让Vue自动处理
          // this.$forceUpdate()
        } catch (error) {
          this.loadingType = 3
        }
      }
    },

    // 🔥 后台静默更新第一页数据
    async silentUpdateFirstPage() {
      try {
        let data = {
          dianwei: this.dianwei,
          device_sn: this.device_sn,
          room_num: this.room_num,
          start_time: this.start_time,
          end_time: this.end_time,
          order_sn: this.order_sn,
          order_status: this.order_status,
          product_id: this.product_id,
          prom_type: this.prom_type,
          page: 1,
        }

        let res = await this.$u.api.getOrderList(data)

        if (res.data) {
          // 静默更新第一页数据
          this.pagesData[0] = res.data
          this.listData = [].concat(...this.pagesData)
          this.orderTotal = res.total

          // 更新缓存
          this.cachedFirstPageData = {
            data: [...res.data],
            timestamp: Date.now(),
            timeRange: {
              current: this.current,
              start_time: this.start_time,
              end_time: this.end_time,
            },
          }

          // 静默更新视图
          this.$forceUpdate()
        }
      } catch (error) {}
    },
    unpdata() {
      this.refresh()
    },
    // onPageScroll(e) {
    //   this.scrollTop = e.scrollTop
    // },
    cancel() {
      this.isShowDelModalOff = false
    },
    /* 禁用设备 */
    onOff(item) {
      if (item.use_status == 1) {
        this.index = 4
      } else if (item.use_status == 2) {
        this.index = 5
      }
      this.isShowDelModalOff = true
      this.device_sn = item.device_sn
      this.unBindItem = item
    },
    /* 禁用设备弹窗 */
    async confirmDelOff(text) {
      this.isShowDelModalOff = false
      let use_status = 0
      if (this.index == 4) {
        use_status = 2
      } else if (this.index == 5) {
        use_status = 1
      }
      let title = ""
      if (use_status == 2) {
        title = text
      }
      let params = {
        device_sn: this.device_sn,
        use_status,
        reason: title,
      }
      try {
        let res = await this.$u.api.setupdateMachineStatus(params)
        this.device_sn = ""
        // this.refresh();
        let index = this.listData.findIndex(
          (items) => items.order_id === this.unBindItem.order_id
        ) // 查找id为2的元素索引
        if (index !== -1) {
          // 如果找到了
          if (this.unBindItem.use_status == 1) {
            this.unBindItem.use_status = 2
            this.unBindItem.reason = title
          } else if (this.unBindItem.use_status == 2) {
            this.unBindItem.use_status = 1
            this.unBindItem.reason = title
          }
          this.listData.splice(index, 1, this.unBindItem)
        }
      } catch (error) {}
    },
    start(i, item) {
      // 补货管理才能有启动
      this.selectIndex = i
      this.isShowStartModal = true
      this.unBindItem = item
    },
    confirmStart() {
      // 开启游戏设备
      this.startGameDevice(this.selectIndex + 1, this.length_time)
    },
    async startGameDevice(channel, time) {
      // 启动设备
      // 启动设备

      let data = {
        device_sn: this.unBindItem.device_sn,
        channel: channel, // 货道
        length_time: time,
      }
      try {
        await this.$u.api.startUM(data)
        this.isShowSuccess("操作成功", 1, () => {}, true)
      } catch (error) {}
    },
    handleSubsection() {
      if (this.current === 5 && !this.showCalendar) this.showCalendar = true
    },
    changeCalendar(e) {
      let { startDate, endDate } = e
      this.handleData("", startDate, endDate)
    },
    handleData(date, startDate, endDate) {
      // this.queryDate = date
      this.start_time = startDate
      this.end_time = endDate

      // 🔥 清除第一页缓存，确保重新加载数据
      this.cachedFirstPageData = null

      this.refresh() // 重新加载数据
    },
    changeSubsection(i) {
      this.current = i
      let selectItem = this.subsectionList[i]

      if (selectItem.isCustom) {
        this.showCalendar = true
      } else {
        this.showCalendar = false
        let date = new Date()

        switch (selectItem.status) {
          case 1: // 今天
            const today = subtractDaysAndFormat(0)
            this.start_time = today
            this.end_time = today
            break
          case 2: // 昨天
            const yesterday = subtractDaysAndFormat(1)
            this.start_time = yesterday
            this.end_time = yesterday
            break
          case 3: // 前天
            const dayBeforeYesterday = subtractDaysAndFormat(2)
            this.start_time = dayBeforeYesterday
            this.end_time = dayBeforeYesterday
            break
          case 4: // 本月
            this.start_time = `${date.getFullYear()}-${(date.getMonth() + 1)
              .toString()
              .padStart(2, "0")}-01`
            this.end_time = `${date.getFullYear()}-${(date.getMonth() + 1)
              .toString()
              .padStart(2, "0")}-${new Date(
              date.getFullYear(),
              date.getMonth() + 1,
              0
            ).getDate()}`
            break
          case 5: // 上月
            const lastMonth = new Date(
              date.getFullYear(),
              date.getMonth() - 1,
              1
            )
            this.start_time = `${lastMonth.getFullYear()}-${(
              lastMonth.getMonth() + 1
            )
              .toString()
              .padStart(2, "0")}-01`
            this.end_time = `${lastMonth.getFullYear()}-${(
              lastMonth.getMonth() + 1
            )
              .toString()
              .padStart(2, "0")}-${new Date(
              lastMonth.getFullYear(),
              lastMonth.getMonth() + 1,
              0
            ).getDate()}`
            break
        }

        // if (selectItem.status < 4) {
        //   this.end_time = this.start_time = subtractDaysAndFormat(
        //     selectItem.status - 1,
        //   )
        // } else if (selectItem.status == 4) {
        //   this.end_time =
        //     date.getFullYear() +
        //     '-' +
        //     (date.getMonth() + 1) +
        //     '-' +
        //     date.getDate()
        //   this.start_time =
        //     date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + 1
        // } else if (selectItem.status == 5) {
        //   let currentDate = new Date();
        //   let lastMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);

        //   this.start_time = lastMonthDate.getFullYear() + '-' + (lastMonthDate.getMonth() + 1) + '-1';
        //   this.end_time = lastMonthDate.getFullYear() + '-' + (lastMonthDate.getMonth() + 1) + '-' + lastMonthDate.getDate();
        // }
        this.handleData("", this.start_time, this.end_time)
      }
    },
    radioChange(e) {},
    // 选中任一radio时，由radio-group触发
    radioGroupChange(e) {},
    async confirmDelReturn() {
      let title = ""
      if (this.radioIndex == 5) {
        title = this.reason
      } else {
        title = this.reasonList[this.radioIndex].title
      }
      let data = {
        order_id: this.unBindItem.order_id,
        money: this.unBindItem.order_amount,
        refund_reason: title,
        // money:'0.01'
      }
      if (this.value == "线下退款") {
        data["refund_type"] = 1
      } else {
        data["refund_type"] = 0
      }
      let that = this
      try {
        await this.$u.api.refundOrder(data)
        this.isShowSuccess("执行成功")
        setTimeout(() => {
          that.refresht()
        }, 2000)
      } catch (error) {}
    },

    retunOrder(item) {
      this.value = "原路返回"
      this.isShowDelModalFree = true
      this.unBindItem = item
    },

    refresht() {
      setTimeout(() => {
        this.refresh()
      }, 2500)
    },
    searchChange(val) {
      this.hotelName = val
      this.search(val)
    },
    search(val) {
      this.order_sn = val
      let list = AddValueInObject(this.vServerList.orderList, val)
      this.$u.vuex(`vServerList.orderList`, list)
      this.refresh()
    },
    changeDropdown(item) {
      this.optionsList = item

      this.order_status = item[0].options[item[0].value].status
      this.refresh()
    },
    async getList(page, done) {
      // 🔥 如果页面正在销毁，立即返回，避免阻塞
      if (this._isDestroying) {
        done([])
        return
      }

      const startTime = Date.now()

      try {
        let data = {
          dianwei: this.dianwei,
          device_sn: this.device_sn,
          room_num: this.room_num,
          start_time: this.start_time,
          end_time: this.end_time,
          order_sn: this.order_sn,
          order_status: this.order_status,
          product_id: this.product_id,
          prom_type: this.prom_type,
          page,
        }

        const apiStartTime = Date.now()

        let res = await this.$u.api.getOrderList(data)

        const apiEndTime = Date.now()

        if (page === 1) {
          this.orderTotal = res.total
        }

        done(res.data)

        const endTime = Date.now()
      } catch (error) {
        const endTime = Date.now()
        done([]) // 确保调用done，避免loading状态卡住
      }
    },
    //点击搜索框右边的icon图标
    onClickIcon(e) {
      if (e == 0) {
        this.isShowPopup = !this.isShowPopup
      }
    },
    //筛选确认查询
    confirmQuery(data) {
      // 保留当前的时间范围
      const currentStartTime = this.start_time
      const currentEndTime = this.end_time
      const currentSubsectionIndex = this.current

      this.dianwei = data.dianwei
      this.device_sn = data.device_sn
      this.prom_type = data.prom_type || 0

      // 仅在筛选器中明确设置了时间时才更新时间条件
      if (data.start_time && data.end_time) {
        this.start_time = data.start_time
        this.end_time = data.end_time
      } else {
        // 如果没有设置时间，则保持当前时间范围
        this.start_time = currentStartTime
        this.end_time = currentEndTime
      }

      this.order_sn = data.order_sn
      this.isShowPopup = false
      this.refresh()
    },
    /* 订单维护 */
    card(i) {
      let item = this.listData[i]
      // uni.navigateTo({ url: `/pagesB/order/OrderMaintain?from=''&order_sn=${this.listData[i].order_sn}` });
    },

    // 🔥 异步清理内存数据的方法
    clearAllMemoryData(async = false) {
      const doCleanup = () => {
        try {
          // 清理定时器
          if (this.scrollTimer) {
            clearTimeout(this.scrollTimer)
            this.scrollTimer = null
          }

          // 快速清理大数据对象（设为null比设为空数组更高效）
          this.pagesData = null
          this.listData = null
          this.pageHeight = null

          // 清理第一页缓存
          this.cachedFirstPageData = null

          // 清理其他可能的大对象
          this.unBindItem = null
          this.selectItem = null

          // 重置关键状态
          this.isRefreshing = false
          this.isPageReady = false
          this.isLoadingMore = false
          this.showBackTop = false
          this.isScrollingToTop = false
          this.showCacheHint = false

          // 重置分页相关
          this.page = 1
          this.loadingType = -1
          this.orderTotal = 0
        } catch (e) {}
      }

      if (async) {
        // 异步清理，不阻塞UI
        setTimeout(doCleanup, 0)
      } else {
        // 同步清理
        doCleanup()
      }
    },
    // 取消所有待处理请求
    cancelAllPendingRequests() {
      try {
        // 方法1: 使用uView API (如果可用)
        if (this.$u?.api?.cancelAllRequests) {
          this.$u.api.cancelAllRequests()
          return
        }

        // 方法2: 微信小程序原生方式
        if (typeof wx !== "undefined") {
          const currentPage = getCurrentPages().slice(-1)[0]
          if (currentPage?.__wxWebviewRequestTasks__) {
            currentPage.__wxWebviewRequestTasks__.forEach((task) => {
              try {
                task?.abort?.()
              } catch (e) {}
            })
          }
        }
      } catch (e) {}
    },

    // 🔥 初始化非关键资源
    initNonCriticalResources() {
      // 可以在这里初始化非关键资源
    },

    // 🔥 简化的订单状态获取
    getOrderStatus(item) {
      if (item.is_refund == 1) return "已退款"
      if (item.order_status == 1) return "完成"
      if (item.order_status == 2) return "异常"
      if (item.order_status == 3) return "订单取消"
      if (item.order_status == 4) return "已处理"
      if (item.pay_status == 0) return "待付款"
      if (item.pay_status == 1) {
        if (item.outstatus == 1) return "已出货"
        return item.unit == 3 ? "启动失败" : "出货失败"
      }
      return "未知状态"
    },

    // 🔥 获取状态样式类
    getStatusClass(item) {
      if (item.is_refund == 1) return "status-refund"
      if (item.order_status == 1) return "status-success"
      if (item.order_status == 2) return "status-error"
      return "status-normal"
    },

    // 🔥 判断是否显示操作按钮
    shouldShowActions(item) {
      return (
        (item.is_show_refund == 1 && item.is_refund == 0) ||
        item.order_status == 2
      )
    },

    // 🔥 启动设备方法
    handleStart(item) {
      this.selectItem = item
      this.isShowStartModal = true
    },

    // 🔥 设备列表方法
    deviceList(item) {
      // 可以在这里添加设备列表逻辑
    },

    // 🔥 订单卡片点击事件
    cardClick(item) {
      // 可以在这里添加订单详情跳转逻辑
      console.log("点击订单:", item.order_sn)
    },

    // 🔥 切换详情显示
    toggleDetails(item) {
      console.log("点击查看详情:", item.order_sn, "当前状态:", item.showDetails)

      // 确保 showDetails 属性存在并切换状态
      if (typeof item.showDetails === "undefined") {
        this.$set(item, "showDetails", true)
      } else {
        this.$set(item, "showDetails", !item.showDetails)
      }

      console.log("切换后状态:", item.showDetails)

      // 🔥 不需要强制更新，Vue 的响应式系统会自动处理
    },

    // 🔥 获取订单状态 - 与 OrderCard 完全一致
    getOrderStatus(item) {
      if (item.is_refund == 1) return "已退款"
      if (item.order_status == 1) return "完成"
      if (item.order_status == 2) return "异常"
      if (item.order_status == 3) return "订单取消"
      if (item.order_status == 4) return "已处理"
      if (item.pay_status == 0) return "待付款"
      if (item.pay_status == 1) {
        if (item.outstatus == 1) return "已出货"
        return item.unit == 3 ? "启动失败" : "出货失败"
      }
      return "未知状态"
    },

    // 🔥 获取订单类型
    getOrderType(item) {
      if (item.prom_type === 8) return "预付费订单"
      if (item.unit === 3) return "时长订单"
      return "普通订单"
    },
  },
  onLoad() {
    const startTime = Date.now()

    // 1. 性能监控初始化
    this.performance = {
      loadStart: startTime,
      memoryBefore: this.getMemoryInfo(),
    }

    // 2. 预加载关键资源
    const preloadImages = ["/static/loading.gif", "/static/empty.png"]
    const preloadPromises = preloadImages.map((img) => {
      return new Promise((resolve) => {
        wx.downloadFile({
          url: img,
          success: () => {
            resolve()
          },
          fail: resolve,
        })
      })
    })

    // 3. 并行初始化数据
    Promise.all(preloadPromises).then(() => {
      this.performance.loadEnd = Date.now()
      this.performance.memoryAfter = this.getMemoryInfo()
    })

    // 4. 延迟非关键初始化
    setTimeout(() => {
      this.initNonCriticalResources()
    }, 300)
  },

  onShow() {
    // 🔥 极简 onShow：只检查第一页缓存，快速显示
    this.isPageReady = true

    // 1. 检查是否有刷新标志
    /* #ifndef H5 */
    const pages = getCurrentPages()
    const currPage = pages[pages.length - 1]
    if (currPage.data.isDoRefresh) {
      currPage.data.isDoRefresh = false
      this.refresh()
      return
    }
    /* #endif */

    /* #ifdef H5 */
    if (this.vCurrPage?.isDoRefresh) {
      this.vCurrPage.isDoRefresh = false
      this.refresh()
      return
    }
    /* #endif */

    // 2. 🔥 只检查第一页缓存，快速显示
    const hasFirstPageCache =
      this.cachedFirstPageData &&
      this.cachedFirstPageData.timeRange.current === this.current &&
      this.cachedFirstPageData.timeRange.start_time === this.start_time &&
      this.cachedFirstPageData.timeRange.end_time === this.end_time

    if (hasFirstPageCache) {
      // 🔥 立即显示第一页缓存数据
      this.pagesData = [this.cachedFirstPageData.data]
      this.listData = [...this.cachedFirstPageData.data]
      this.page = 1
      this.loadingType = 0

      // 🔥 后台静默更新（延迟执行，避免阻塞）
      if (this.isLiveDataType()) {
        setTimeout(() => {
          this.silentRefreshData()
        }, 2000)
      }
      return
    }

    // 3. 没有缓存时，正常加载
    this.page = 1
    this.loadingType = -1
    this.pagesData = []
    this.listData = []

    // 设置默认时间范围（今天）
    if (!this.start_time || !this.end_time) {
      const today = subtractDaysAndFormat(0)
      this.start_time = today
      this.end_time = today
      this.current = 0
    }

    // 加载数据
    this.refresh()
  },

  onPageScroll(event) {
    // 如果正在滚动到顶部，则跳过状态更新
    if (this.isScrollingToTop) return

    // 清除之前的计时器
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer)
    }

    // 实时更新按钮显示状态
    this.showBackTop = event.scrollTop > 500

    // 🔥 移除强制更新，让Vue自动处理响应式更新
    // this.$forceUpdate() // 这是性能杀手！
  },

  // 🔥 终极解决方案：立即返回，不做任何操作
  onHide() {
    // 什么都不做，立即返回
  },

  // 🔥 触底加载更多
  onReachBottom() {
    this.onBottom()
  },

  onUnload() {
    // 标记页面正在销毁
    this._isDestroying = true

    // 清理所有缓存数据
    this.clearAllMemoryData()

    // 取消所有待处理请求
    this.cancelAllPendingRequests()

    // 重置关键状态
    this.listData = []
    this.pagesData = []
    this.pageHeight = []
    this.loadingType = -1
    this.page = 1

    // 清理定时器
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer)
      this.scrollTimer = null
    }
  },

  // 🔥 完全移除 beforeDestroy，这可能是主要的阻塞源
  // beforeDestroy() {
  //   // 移除这个钩子，让 Vue 自己处理
  // },

  mixins: [myPull(), performanceMixin],
}
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.border {
  border: 2rpx solid transparent;
  box-sizing: border-box;
  /* 包含边框和内边距在元素的总宽高内 */
}

.top {
  position: relative;
  z-index: 999;
}

.subsection {
  background-color: #fff;
  padding: 20rpx;
}

.modalTop {
  // height: 200rpx;
  font-size: 28rpx;
}

.title {
  margin: 15rpx 0;
}

.red {
  text-align: center;
  color: orangered;
  font-size: 20rpx;
}

.flexBottom {
  padding: 3rpx 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  // border: 1px solid #000;
  padding-bottom: 10rpx;

  ::v-deep .u-radio {
    margin-right: 80rpx;
  }
}

/* 🔥 缓存提示样式 */
.cache-hint {
  position: fixed;
  top: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  backdrop-filter: blur(10rpx);
  animation: fadeInOut 3s ease-in-out;
}

.cache-hint-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.cache-hint-text {
  color: #fff;
  font-size: 28rpx;
  text-align: center;
}

.cache-hint-action {
  color: #007aff;
  font-size: 26rpx;
  text-decoration: underline;
  text-align: center;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20rpx);
  }
  20% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  80% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20rpx);
  }
}

/* 🔥 完全复制 OrderCard 的样式 */
.order-list {
  flex: 1;
}

.order-cell {
  margin-bottom: 20rpx;
}

.order-list .card {
  box-shadow: none !important;
}
.card {
  padding: 20rpx 20rpx 0;
  background: #fff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;

  .content {
    position: relative;
  }

  .border-bottom {
    border-bottom: 2rpx solid #f0f0f0;
  }

  .border_top {
    border-top: 2rpx solid #f0f0f0;
  }

  .flexBottom {
    padding: 3rpx 0;
    display: flex;
    justify-content: center;
    padding-bottom: 10rpx;
  }

  .flexButton {
    border-top: 2rpx solid #f0f0f0;
    padding: 5rpx 0;
    display: flex;
    height: 70rpx;
    font-size: 20rpx;
    justify-content: space-between;

    .returns {
      text-align: center;
      width: 160rpx;
      font-weight: bold;
      padding: 8rpx 5rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(268deg, #1fbefd 0%, #46a1ff 99%);
      color: #fff;
    }
  }

  .content {
    box-sizing: content-box;
    display: flex;
    height: 150rpx;
    transition: all 0.4s;

    .img {
      width: 150rpx;
      height: 100%;
    }

    .info {
      flex: 1;
      margin-left: 20rpx;
      height: 100%;
      font-size: 24rpx;
      color: #666;

      .goods_name {
        .name {
          color: #333;
          font-size: 28rpx;
          font-weight: bold;
        }

        .order-time {
          font-size: 24rpx;
          color: #999;
        }

        .status {
          color: #0eade2;
          font-size: 22rpx;

          .status_icon {
            width: 30rpx;
            height: 30rpx;
            margin-right: 6rpx;
          }
        }
      }

      .num {
        color: #333;
      }

      .price {
        color: #ef0000;
      }

      .status {
        color: #0eade2;

        .arrow {
          transition: all 0.4s;
          margin-left: 6rpx;

          .arrow-icon {
            color: #0eade2;
            font-size: 24rpx;
            transition: all 0.3s;
          }
        }

        .rotate {
          transform: rotate(180deg);
        }

        .status_icon {
          width: 30rpx;
          height: 30rpx;
          margin-right: 6rpx;
        }
      }

      .arrow {
        transition: all 0.4s;
        margin-left: 6rpx;

        .arrow-icon {
          color: #0eade2;
          font-size: 24rpx;
          transition: all 0.3s;
        }
      }

      .rotate {
        transform: rotate(180deg);
      }
    }
  }

  .prepay-info {
    text {
      font-size: 20rpx;
      margin-right: 16rpx;
    }
  }

  .prepay-pricing {
    text {
      font-size: 20rpx;
      margin-right: 16rpx;
    }
  }

  .myprepay-pricing {
    .prepay-row {
      display: flex;
      gap: 18rpx;

      text {
        flex-shrink: 0;
        font-size: 20rpx;
        white-space: nowrap;
      }
    }
  }

  .details {
    padding-bottom: 30rpx;

    > view {
      margin-top: 30rpx;
      display: flex;
      justify-content: space-between;
      font-size: 26rpx;

      > view {
        &:first-child {
          color: #666;
        }

        &:last-child {
          color: #333;
        }
      }

      .flex_white {
        white-space: nowrap;
        margin-right: 20rpx;
      }
    }

    > view > text {
      font-size: 22rpx;
    }
  }
}

// style="
//           position: fixed;
//           right: 20rpx;
//           bottom: 60rpx;
//           width: 60rpx;
//           height: 30rpx;
//           background-color: pink;
//           z-index: 20;
//         "

.backTopbtn {
  width: 80rpx;
  height: 80rpx;
  position: fixed;
  bottom: 250rpx;
  right: 50rpx;
  transition: transform 0.3s ease;
  &:active {
    transform: scale(0.9);
  }
  image {
    width: 100%;
    height: 100%;
  }
}

.imgdis {
  position: absolute;
  right: -9px;
  bottom: 0px;
  width: 214rpx;
  height: 166rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

/* 🔥 通用布局类 */
.flexRowBetween {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flexColumnBetween {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.flexRowAllCenter {
  display: flex;
  justify-content: center;
  align-items: center;
}

.textMaxTwoLine {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 🔥 删除不需要的旧样式，使用 OrderCard 样式 */

/* 🔥 调试样式 */
.debug-info {
  padding: 20rpx;
  background: #f0f0f0;
  margin: 20rpx;
  border-radius: 10rpx;
}

.debug-info text {
  display: block;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
}

/* 🔥 简单容器样式 */
.simple-container {
  padding: 20rpx;
}

/* 🔥 触底加载状态样式 */
.load-more-status {
  padding: 40rpx 20rpx;
  text-align: center;
}

.loading {
  .loading-text {
    color: #666;
    font-size: 28rpx;
  }
}

.no-more {
  .no-more-text {
    color: #999;
    font-size: 26rpx;
  }
}

.empty {
  .empty-text {
    color: #ccc;
    font-size: 30rpx;
  }
}

/* 🔥 订单列表容器样式 */
.order-list-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 0 20rpx;
}
</style>
