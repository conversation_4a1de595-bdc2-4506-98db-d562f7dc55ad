<template>
  <view class="content" @click="goDetail">
    <view class="content-left">
      <image class="img" :src="imgIcon" />
    </view>
    <view class="content-right">
      <view class="content-right-type">
        <view class="content-right-type-title">{{ typeTitle }}</view>
        <view class="content-right-type-time">
          {{ $u.timeFormat(info.time, "yyyy-mm-dd hh:MM") }}</view
        >
      </view>
      <view class="copywrite textMaxOneLine">
        {{ info.wxtitle }}
      </view>
    </view>
    <view class="circle" v-if="info.read_status != 1"></view>
  </view>
</template>

<script>
export default {
  name: "NoticeListCard",
  props: { info: { type: Object, default: {} } },
  computed: {
    imgIcon() {
      if (this.info.type == 1) {
        this.typeTitle = "系统消息";
        return require("@/static/img/icon/notice-sys.png");
      } else if (this.info.type == 2) {
        this.typeTitle = "活动消息";
        return require("@/static/img/icon/notice-activity.png");
      } else {
        this.typeTitle = "其他消息";
        return require("@/static/img/icon/notice-other.png");
      }
    },
  },
  data() {
    return {
      typeTitle: "其他消息",
    };
  },
  methods: {
    goDetail() {
      this.$emit("setStatus");
      uni.navigateTo({
        url:
          "/pagesB/notice/NoticeDetail?id=" +
          this.info.id +
          "&title=" +
          this.typeTitle+
          "&msg=" +
          this.info.wxtitle
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  display: flex;
  align-items: center;
  &-left {
    width: 80rpx;
    height: 80rpx;
    flex-shrink: 0;
    .img {
      width: 100%;
      height: 100%;
    }
  }
  &-right {
    // flex: 1;
    flex: 1 1 auto;
    // width: 100%;
    padding: 30rpx;
    padding-left: 0;
    margin-left: 18rpx;
    border-bottom: 2rpx solid $dividerColor;
    &-type {
      display: flex;
      justify-content: space-between;
      &-title {
        color: $textBlack;
        font-size: $font-size-xlarge;
        font-weight: 700;
      }
      &-time {
        color: $textDarkGray;
      }
    }
    .copywrite {
      width: 570rpx;
      color: $textBlack;

      font-size: $font-size-base;
      margin-top: 20rpx;
    }
  }
  .circle {
    position: absolute;
    right: 0;
    top: 20rpx;
    width: 12rpx;
    height: 12rpx;
    background-color: $textWarn;
    border-radius: 50%;
  }
}
</style>