<template>
  <view>
    <BaseNavbar :title="title" />

    <view class="progres_box">
      <view class="progress_tatile">
        {{ vPointName || "" }}名称：{{ hotel_name || "" }}
      </view>
      <view class="progress_swiper">
        <image src="@/pagesD/static/img/progress_bc.png" />
        <view>
          <view>
            {{ total || 0 }}
          </view>
          <view>
            {{ "设备总数" }}
          </view>
        </view>
        <view>
          <view>
            {{ onlineList || 0 }}
          </view>
          <view>
            {{ "在线设备" }}
          </view>
        </view>
        <view>
          <view>
            {{ offlineList || 0 }}
          </view>
          <view>
            {{ "离线设备" }}
          </view>
        </view>
      </view>
      <view class="progress_main">
        <view class="progress_top">
          <view class="progress_mt">
            <view>{{ "场地方名称" }}</view>
            <view>{{ hotel_name || "" }}</view>
          </view>
          <view class="progress_mt">
            <view>{{ "详细地址" }}</view>
            <view>{{ address || "无" }}</view>
          </view>
          <view class="progress_mt">
            <view>{{ "拥有者" }}</view>
            <view>{{ user_login || "" }}</view>
          </view>
          <view class="progress_mt">
            <view>{{ "联系人电话" }}</view>
            <view>{{ phone || "" }}</view>
          </view>
        </view>
        <view class="progress_buttom">
          <view class="progres_bto">
            <BaseTypeTab @change="tabChange" />
            <view class="center_btn">
              <view v-if="vButtonPermis && [0].includes(product_id * 1)">
                <BaseButton type="primary" @onClick="clicke(1)">
                  批量补水
                </BaseButton>
              </view>
              <view v-if="vButtonPermis && [0].includes(product_id * 1)">
                <BaseButton type="primary" @onClick="clicke(2)">
                  批量启动
                </BaseButton>
              </view>
              <view v-if="vButtonPermisFour && [0].includes(product_id * 1)">
                <BaseButton type="primary" @onClick="clicke(3)">
                  开启灯光
                </BaseButton>
              </view>
              <view v-if="[0, 1, 2].includes(product_id * 1)">
                <BaseButton type="primary" @onClick="clicke(6)">
                  开启音乐
                </BaseButton>
              </view>

              <view v-if="[0].includes(product_id * 1)">
                <BaseButton type="primary" @onClick="clicke(4)">
                  关闭灯光
                </BaseButton>
              </view>
              <view v-if="[0].includes(product_id * 1)">
                <BaseButton type="primary" @onClick="clicke(12)">
                  开启提示音
                </BaseButton>
              </view>
              <view v-if="[0].includes(product_id * 1)">
                <BaseButton type="primary" @onClick="clicke(13)">
                  关闭提示音
                </BaseButton>
              </view>
              <view v-if="[0].includes(product_id * 1)">
                <BaseButton type="primary" @onClick="clicke(10)">
                  调节音量
                </BaseButton>
              </view>

              <view
                v-if="
                  vButtonPermisTwo &&
                  vButtonPermisFour &&
                  [0].includes(product_id * 1)
                "
              >
                <BaseButton type="primary" @onClick="clicke(7)">
                  批量绑定
                </BaseButton>
              </view>
              <view v-if="vButtonPermis && [1, 2].includes(product_id * 1)">
                <BaseButton type="primary" @onClick="clicke(8)">
                  批量出泡泡
                </BaseButton>
              </view>
              <view v-if="vButtonPermis && [3].includes(product_id * 1)">
                <BaseButton type="primary" @onClick="clicke(14)">
                  批量开炮
                </BaseButton>
              </view>
              <view v-if="vButtonPermis && [0].includes(product_id * 1)">
                <BaseButton type="primary" @onClick="clicke(14)">
                  出泡泡/开炮
                </BaseButton>
              </view>
              <view v-if="vButtonPermis && [0].includes(product_id * 1)">
                <BaseButton type="primary" @onClick="clicke(9)">
                  刷新状态
                </BaseButton>
              </view>
              <view
                v-if="
                  vButtonPermissions &&
                  vButtonPermisFour &&
                  [0].includes(product_id * 1)
                "
              >
                <BaseButton type="eixt" @onClick="clicke(5)">
                  解除绑定
                </BaseButton>
              </view>
              <view
                v-if="
                  vButtonPermissions &&
                  vButtonPermisFour &&
                  [0].includes(product_id * 1)
                "
              >
                <BaseButton type="eixt" @onClick="clicke(11)">
                  批量迁移
                </BaseButton>
              </view>
              <view v-if="[1, 2].includes(product_id * 1)"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <BaseProGressList
      @errorChange="errorChange"
      :titles="titles"
      :total="dataPageList[product_id].length"
    />
    <!-- 批量补水 -->
    <BaseModal :show.sync="isShowWaterModal" @confirm="confirmWater">
      <view slot="">可玩时间：{{ tiem }}</view>
      <view slot="default">
        <input
          class="isReplenish"
          v-model="WarterNum"
          placeholder="请输入补货水量(0.0-3.0)升"
        />
      </view>
    </BaseModal>
    <!-- 关闭提示音 -->
    <BaseModal
      :show.sync="isShowMusicStopModal"
      @confirm="confirmMusicStopLength"
    >
      <view slot="default" class="default">
        如果关闭提示音会减少互动效果将影响收入
      </view>
      <view slot="default" class="default"> 您确认要关闭提示音吗？ </view>
    </BaseModal>
    <!-- 批量启动 -->
    <BaseModal :show.sync="isShowStartModal" @confirm="confirmStart">
      <view slot="default">
        <BaseInput
          v-model="length_time"
          :disabled="vInputDisable"
          placeholder="请输入启动时长(分钟)"
        />
      </view>
    </BaseModal>
    <!-- 控制灯光 -->
    <BaseModal :show.sync="isShowLightModal" @confirm="confirmLight">
      <view slot="default" class="default">
        如果你用的是充电宝设备将会导致设备离线
      </view>
      <view slot="default" class="default"> 您确认要关闭灯光吗吗？ </view>
    </BaseModal>
    <!-- 灯光提示 -->
    <BaseModal :show.sync="isShowLight" @confirm="openLightEnd">
      <view slot="default" class="default">
        确定要开启商户下所有设备灯光吗
      </view>
    </BaseModal>
    <!-- 出泡泡提示 -->
    <BaseModal :show.sync="isShowStartMain" @confirm="comfirmStartMain">
      <TextLength :num="length_time"></TextLength>
    </BaseModal>
    <!-- 控制音乐时长 -->
    <BaseModal :show.sync="isShowMusicModal" @confirm="confirmMusicLength">
      <view slot="default">
        <BaseInput v-model="adjust_time" placeholder="请输入播放时长(分钟)" />
      </view>
    </BaseModal>

    <BaseModal
      :show.sync="isShowNameModal"
      :content="'您的设备将与' + vPointName + '解除绑定，是否继续解绑？'"
      @confirm="confirmUnBind"
    />
    <BaseModal
      :show.sync="isShowUpdataTime"
      content="是否刷新灯状态"
      @confirm="confirmUpdata"
    />
    <!-- 音量调节器 - 直接使用，不嵌套在 BaseModal 中 -->
    <VolumeAdjuster
      :show="isShowVolumeModal"
      :defaultVolume="constraintLength"
      :deviceSn="currentDeviceSn"
      @confirm="confirmVolumeAdjust"
      @cancel="cancelVolumeAdjust"
      @update:show="isShowVolumeModal = $event"
    />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue"
import BaseButton from "@/components/base/BaseButton.vue"
import BaseModal from "@/components/base/BaseModal.vue"
import BaseInput from "@/components/base/BaseInput.vue"
import BasePopup from "../../components/base/BasePopup.vue"
import BaseProGressList from "@/components/base/BaseProGressList.vue"
import TextLength from "@/components/text/TextLength.vue"
import myPull from "@/mixins/myPull.js"
import BaseTypeTab from "@/components/base/BaseTypeTab.vue"
import VolumeAdjuster from "@/components/common/VolumeAdjuster"
export default {
  components: {
    BaseNavbar,
    BaseButton,
    BaseModal,
    BaseInput,
    BasePopup,
    BaseProGressList,
    TextLength,
    BaseTypeTab,
    VolumeAdjuster,
  },
  porps: {
    info: {
      type: Object,
      default: {},
    },
  },

  data() {
    return {
      hotel_name: "", //点位名称
      // owner_login: "", //账户
      user_login: "", //拥有者
      phone: "",
      address: "",
      title: "批量管理",
      bac: require("@/pagesD/static/img/progress_bc.png"),
      WarterNum: 0, //补水量
      isShowcheckout: false, //批量添加水量弹窗
      isShowWaterModal: false, //添加水量弹窗
      isShowLight: false, //批量开启弹窗
      isShowStartModal: false, //批量启动
      isShowStartMain: false, //出泡泡超过三分提示
      isShowMusicModal: false, //是否显示输入音乐时长
      isShowLightModal: false, // 是否关灯
      isShowMusicStopModal: false, //关闭提示音
      isShowNameModal: false, //接触绑定
      isShowProgress: false, //进度条
      isShowUpdataTime: false, //更新灯状态弹窗
      adjust_time: 1, //播放时
      length_time: 1, //启动时常
      total: 0, //设备总数
      finishNum: 0, //已经完成的设备数
      device_sn: "", //正在操作的设备编号
      cancel: false, //控制取消按钮显示
      failNum: 0, //失败的数
      ProgressNum: 0, //进度
      timeOut: false, //暂停
      ok: false, //完成
      chargeRuleInfo: {},
      hotel_id: "",
      titles: "批量启动",
      dataList: [],
      onlineList: [],
      offlineList: [],
      isBind: false, //接触绑定
      dianwei: "",
      loading: false,
      user_name: "", //账号
      isShowVolumeModal: false, //音量弹窗
      constraintLength: 100, //音量
      product_id: 0,
      dataPageList: [],
      currentDeviceSn: "", // 添加当前设备SN
    }
  },
  methods: {
    tabChange(e) {
      // console.log('切换tab', e,)
      this.product_id = e.id
      // this.refresh()
    },
    /* 方法没有设备提示 */

    errorChange(list) {
      this.clear()
      // this.getList()
      // this.getTwoList()
    },
    timeOt() {
      this.timeOut = false
      this.ok = true
      this.cancel = true
    },
    /* 防抖 */
    setTime() {
      let timestamp = new Date().valueOf()
      if (timestamp - this.lastOrderTime < 5000) {
        uni.showToast({
          title: `其他指令正在执行请等${
            5 - ((timestamp - this.lastOrderTime) / 1000).toFixed(0) + 1
          }秒后进行其他操作`,
          icon: "none",
          duration: 1500,
        })
        // this.lastOrderTime = timestamp;
        return false
      } else {
        this.lastOrderTime = timestamp
        return true
      }
    },
    async clicke(i) {
      //判断列表是否为空
      // console.log()
      if (!this.dataPageList[this.product_id]) {
        await this.getTwoList()
      }
      // console.log('请求事件',this.dataPageList[this.product_id].length==0)
      if (this.dataPageList[this.product_id].length == 0) {
        return this.isShowErr("未绑定在线设备")
      }
      if (i !== 7) {
        this.loading = this.total < 1
      } else {
        this.loading = false
      }
      if (this.loading) {
        return this.isShowErr("未绑定设备")
      }

      if (i == 1) {
        this.confirmCheckout()
      } else if (i == 2) {
        this.start(0)
      } else if (i == 3) {
        this.openLight()
      } else if (i == 4) {
        this.openLightStop()
      } else if (i == 5) {
        this.onUnbind()
      } else if (i == 6) {
        this.openMusic()
      } else if (i == 7) {
        this.isShowTwo("", 1)
        this.Navigations(0)
      } else if (i == 8) {
        this.start(1)
      } else if (i == 9) {
        this.updataTime()
      } else if (i == 10) {
        this.openMusic()
      } else if (i == 11) {
        this.isShowTwo("", 1)
        this.Navigations(1)
      } else if (i == 12) {
        /* 开启提示音 */
        this.isShowTwo("", 1)
        this.MusicStart()
      } else if (i == 13) {
        /* 关闭提示音 */
        this.isShowTwo("", 1)
        this.MusicStop()
      } else if (i == 14) {
        this.start(1)
      }
    },
    /* 刷新灯的状态 */
    updataTime() {
      this.isShowUpdataTime = true
    },
    confirmUpdata() {
      this.titles = "批量更新灯的状态"
      // this.isShowProgress = true
      let data = {}
      const requestCallback = async (data) => {
        return this.$u.api.getAllStatus(data)
      }
      uni.$emit(
        "customEvent",
        this.dataPageList[this.product_id],
        data,
        3000,
        requestCallback
      )
    },
    /* 跳转批量绑定界面 */
    Navigations(i) {
      if (i == 0) {
        uni.navigateTo({
          url: `/pagesD/bindList/BindList?from=device&hotel_id=${this.hotel_id}&hotelName=${this.hotel_name}&user_login=${this.user_name}`,
        })
      } else if (i == 1) {
        let list = ""
        if (this.dataPageList[this.product_id].length > 0) {
          list = encodeURIComponent(
            JSON.stringify(this.dataPageList[this.product_id])
          )
        }
        uni.navigateTo({
          url: `/pagesD/bindList/BindList?from=move&hotel_id=${this.hotel_id}&hotelName=${this.hotel_name}&user_login=${this.user_name}&list=${list}`,
        })
      }
    },
    /* 批量添加水量弹窗 */
    confirmCheckout() {
      this.isShowWaterModal = true
    },
    /* 确认补水 */
    async confirmWater() {
      this.checkNumber(this.WarterNum)
      let data = {
        cbm: this.WarterNum,
      }
      setTimeout(() => {
        // this.isShowProgress = true

        const requestCallback = async (data) => {
          return this.$u.api.setUMAllStock(data)
        }
        uni.$emit(
          "customEvent",
          this.dataPageList[this.product_id],
          data,
          3000,
          requestCallback
        )
        // this.sendRequests(this.dataList, data, 3000, requestCallback);
      }, 700)
    },
    /* 补充水量 */
    checkNumber(item) {
      // this.WarterNum = item > 3 ? 3 : item < 0 ? 0 : item;
      // console.log(this.WarterNum, item, item * 1 > 3 || item * 1 < 0);
      if (item > 3) {
        this.WarterNum = 3.0
        this.isShowErr("最大为3升自动修改为3升")
        // return
      } else if (item < 0) {
        this.WarterNum = 0.0
        this.isShowErr("最小为0升自动修改为0升")
        // return
      } else if (!item) {
        this.WarterNum = 0.0
        // return
      } else if (this.WarterNum.toString().split(".").pop().length > 1) {
        this.WarterNum = Math.floor(item * 10) / 10
        this.isShowErr("最多一位小数补充为" + this.WarterNum)
        // return
      }
      // this.WarterNum = this.WarterNum.replace(/[1-3]/g,'')
    },
    // 游戏设备，启动游戏设备
    start(i) {
      // 补货管理才能有启动
      this.selectIndex = i
      this.isShowStartModal = true
      // console.log('启动设备结果参数 1 ：', i)
    },
    confirmStart() {
      // 开启游戏设备

      // this.isShowProgress = true
      if (this.length_time >= 3) {
        this.isShowStartMain = true
      } else {
        //出泡泡
        let times = 3000
        this.startGameDevice(this.selectIndex + 1, this.length_time, times)
      }
    },
    //出泡泡
    comfirmStartMain() {
      let times = 3000
      this.startGameDevice(this.selectIndex + 1, this.length_time, times)
    },
    startGameDevice(channel, time, intervalTime, dp = false) {
      // 启动设备
      // 启动设备

      let data = {
        channel: channel, // 货道
        length_time: time,
      }
      // console.log('启动设备结果参数 3 ：', channel, time)
      // for (let i = 0; i < this.dataList.length; i++) {

      // }
      this.titles = "批量启动"
      // 示例用法：每个请求之间等待1000毫秒（1秒）

      // const intervalBetweenRequests = 1000; // 1000毫秒 = 1秒
      // 定义回调函数
      const requestCallback = async (data) => {
        return this.$u.api.startUM(data)
      }
      uni.$emit(
        "customEvent",
        this.dataPageList[this.product_id],
        data,
        intervalTime,
        requestCallback
      )
      // this.sendRequests(this.dataList, data, 3000, requestCallback);

      // this.$u.api.startUM(data).then((res) => {
      //   console.log("启动设备结果4 ：", res);
      //   this.isShowSuccess("操作成功", 1, () => { }, true);
      // });
    },
    /* 开启灯光提示 */
    openLight() {
      this.isShowLight = true
    },
    // 开启控制灯
    openLightEnd() {
      if (!this.setTime()) {
        return
      }
      let data = {
        status: 1,
      }
      // console.log("批量开启灯光",);
      this.titles = "批量开启灯光"
      // this.isShowProgress = true

      const requestCallback = async (data) => {
        return this.$u.api.turnLight(data)
      }
      uni.$emit(
        "customEvent",
        this.dataPageList[this.product_id],
        data,
        3000,
        requestCallback
      )
      // this.sendRequests(this.dataList, data, 3000, requestCallback);
      // this.$u.api.turnLight(data).then((res) => {
      //   console.log("启动设备结果4 ：", res);
      //   this.light = false
      //   // this.isShowSuccess("操作成功", 1, () => { }, true);
      //   this.isShowTwo("操作成功", 1);
      // });
    },
    // 关闭控制灯
    openLightStop() {
      this.isShowLightModal = true
    },
    confirmLight() {
      if (!this.setTime()) {
        return
      }
      // 控制灯
      let data = {
        status: 0,
      }
      this.titles = "批量关闭灯光"
      this.isShowProgress = true

      const requestCallback = async (data) => {
        return this.$u.api.turnLight(data)
      }
      uni.$emit(
        "customEvent",
        this.dataPageList[this.product_id],
        data,
        3000,
        requestCallback
      )
      // this.sendRequests(this.dataList, data, 3000, requestCallback);
      // console.log("启动设备结果参数 3 ：", data);
      // this.$u.api.turnLight(data).then((res) => {
      //   console.log("启动设备结果4 ：", res);
      //   this.light = true
      //   // this.isShowSuccess("操作成功", 1, () => { }, true);
      //   this.isShowTwo("操作成功", 1);
      // });
    },

    /* 批量接触绑定 */
    onUnbind() {
      // 已经绑定了点位 弹出是否解绑
      // this.unBindItem = item;
      this.isShowNameModal = true
      this.isBind = true
    },
    // 播放音乐时长
    openMusic() {
      this.isShowMusicModal = true
    },
    //滑动事件
    sliderChange(e) {
      this.constraintLength = e.detail.value
    },
    //调节音量
    openMusic() {
      this.isShowVolumeModal = true
      // 如果需要，可以在这里设置当前设备的SN
      this.currentDeviceSn =
        this.dataPageList[this.product_id]?.[0]?.device_sn || ""
    },
    // 新的音量确认方法
    confirmVolumeAdjust(volume) {
      // 🔥 关闭弹窗
      this.isShowVolumeModal = false

      let data = {
        volume: volume,
      }
      const requestCallback = async (data) => {
        return this.$u.api.getUserVideo(data)
      }
      uni.$emit(
        "customEvent",
        this.dataPageList[this.product_id],
        data,
        3000,
        requestCallback
      )
    },

    confirmVolume() {
      let data = {
        volume: this.constraintLength,
      }
      const requestCallback = async (data) => {
        return this.$u.api.getUserVideo(data)
      }
      uni.$emit(
        "customEvent",
        this.dataPageList[this.product_id],
        data,
        3000,
        requestCallback
      )
    },
    // 取消音量调节
    cancelVolumeAdjust() {
      this.isShowVolumeModal = false
    },
    // 播放音乐时长
    confirmMusicLength() {
      if (!this.setTime()) {
        return
      }
      this.startMusic(this.adjust_time)
    },
    // 播放音乐
    startMusic(time) {
      this.isShowProgress = true

      this.titles = "批量播放音乐"
      let data = {
        // device_sn: this.device_sn,
        length_time: time, //获取子组件传来的数据
      }
      const requestCallback = async (data) => {
        return this.$u.api.startMusic(data)
      }
      uni.$emit(
        "customEvent",
        this.dataPageList[this.product_id],
        data,
        3000,
        requestCallback
      )
      // this.sendRequests(this.dataList, data, 3000, requestCallback);
      // this.$u.api.startMusic(data).then((res) => {
      //   console.log(res);
      //   this.isShowSuccess(res.msg);
      // });
    },
    confirmUnBind() {
      //确认解绑
      let data = {}
      this.titles = "批量接除绑定"
      // this.isShowProgress = true

      const requestCallback = async (data) => {
        return this.$u.api.unbindHotel(data)
      }
      // uni.$emit('customEvent', this.dataList, data, 3000, requestCallback)
      uni.$emit(
        "customEvent",
        this.dataPageList[this.product_id],
        data,
        3000,
        requestCallback
      )
      // this.sendRequests(this.dataList, data, 3000, requestCallback);
      // this.$u.api.unbindHotel(data).then((res) => {
      //   this.isShowSuccess("解绑成功", 0, () => this.refresh());
      // });
      // console.log("确认解绑");
    },
    // 辅助函数：等待指定的时间
    sleep(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms))
    },
    clear() {
      this.finishNum = 0
      this.ProgressNum = 0
      this.failNum = 0
    },
    async getTwoList() {
      let datas = {
        dianweiid: this.dianwei,
        hotel_id: this.hotel_id,
        limit: this.total,
        product_id: this.product_id,
        status: 1,
      }

      let res2 = await this.$u.api.getHotelMachines(datas)
      this.dataPageList[this.product_id] = res2.data
      // console.log('数组列表', this.dataPageList[this.product_id])
    },
    async getList() {
      try {
        let datas = {
          dianweiid: this.dianwei,
          hotel_id: this.hotel_id,
        }
        let res = await this.$u.api.getHotelMachines(datas)
        if (res.data) {
          this.dataList = res.data
        }
        this.total = res.total
        this.dataPageList = []
        this.onlineList = res.machine[0].Normal
        this.offlineList = res.machine[0].Failure
        if (res.total > 0) {
        } else {
          this.onlineList = this.offlineList = 0
        }
      } catch (error) {
        console.error("请求失败:", error)
        // 处理请求失败的情况
      }
    },
    /* 开启提示音 */
    MusicStart() {
      if (!this.setTime()) {
        return
      }
      let data = {
        status: 1,
      }
      this.titles = "批量开启提示音"
      // console.log("批量开启提示音", this.dataPageList[this.product_id],this.product_id);
      const requestCallback = async (data) => {
        return this.$u.api.turnTipMusic(data)
      }
      uni.$emit(
        "customEvent",
        this.dataPageList[this.product_id],
        data,
        3000,
        requestCallback
      )
    },
    /* 关闭提示音 */
    MusicStop() {
      this.isShowMusicStopModal = true
    },
    confirmMusicStopLength() {
      if (!this.setTime()) {
        return
      }
      // 控制灯
      let data = {
        status: 0,
      }
      this.titles = "批量关闭提示音"
      const requestCallback = async (data) => {
        return this.$u.api.turnTipMusic(data)
      }
      uni.$emit(
        "customEvent",
        this.dataPageList[this.product_id],
        data,
        3000,
        requestCallback
      )
    },
  },
  onLoad(opt) {
    if (opt?.from) {
      this.fromData = opt.from
      if (this.fromData == "edit") {
        let data = JSON.parse(decodeURIComponent(opt.data))
        this.hotel_name = data.hotelName
        this.phone = data.linkmanPhone
        this.user_login = data.to_user.user_login
        this.user_name = data.dw.user_login
        this.address = data.address
        this.total = data.machine_num
        this.hotel_id = data.id
        this.dianwei = data.dianwei
        this.clear()
        this.getList()
      }
    }
    if (this.vInputDisable) {
      this.length_time = this.vTime
    }
  },
  onShow() {
    /*  #ifndef H5 */
    let pages = getCurrentPages()
    let currPage = pages[pages.length - 1] // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      uni.$emit("customEventfalse")
      this.getList()
      this.isShowTwo("", 1)
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.getList()
      this.isShowTwo("", 1)
    }
    /*#endif */
  },
  computed: {
    tiem() {
      if (!this.vGamingTime > 0) {
        return 0
      }
      return this.WarterNum * this.vGamingTime
    },
  },
  mixins: [myPull()],
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
.text_center {
  text-align: center;
}

.default {
  text-align: center;
  font-size: 26rpx;
}

.cent_progres {
  padding: 0 30rpx;
  margin-top: 28rpx;

  .center_pro_title {
    text-align: center;
  }

  .center_pro_top {
    // display: flex;
    width: 100%;

    // flex-wrap: wrap;
    view {
      margin: 5rpx 0;

      // flex: 1;
      // border: 1px solid #000;
      // width:100%;
      text {
        width: 100rpx;
      }
    }
  }

  .center_pro_bottom {
    display: flex;

    > text {
      // border: 1px solid #000;
      margin-right: 25rpx;
    }
  }

  .center_pro {
    // height: 18rpx;
    // border: 1px solid #000;
    // display: flex;
    // justify-content: center;
    margin-bottom: 10rpx;
  }

  .center_pro_btn {
    // border: 1px solid #000;
    margin: 20rpx 0;
  }
}

.progres_box {
  padding: 40rpx 35rpx;

  .progress_tatile {
    text-align: center;
    font-size: 45rpx;
    font-weight: bolder;
    overflow: hidden;
    white-space: nowrap;
    /* Ensures that text doesn't wrap */
    text-overflow: ellipsis;
    /* Truncate text with an ellipsis */
  }

  .progress_swiper {
    margin-top: 35rpx;
    height: 150rpx;
    position: relative;
    z-index: 1;

    image {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
    }

    display: flex;
    justify-content: space-between;

    :nth-child(1) {
      font-size: 35rpx;
    }

    > view {
      height: 100%;
      color: white;
      font-weight: bolder;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      > view {
        margin: 10rpx 0;
      }
    }
  }

  .progress_main {
    // border: 1px solid #000;
    border-radius: 15rpx;
    // margin-top: 20rpx;
    background-color: white;
    // border: 1px solid #000;
    padding: 25rpx 20rpx;
    margin-top: 20rpx;

    .progress_top {
      // border: 1px solid #000;
      padding: 20rpx 0;

      .progress_mt {
        margin-bottom: 25rpx;
        display: flex;

        // border: 1px solid #000;
        > view {
          padding: 15rpx 20rpx;
          overflow: hidden; //超出的文本隐藏
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; //溢出不换行
        }

        > view:nth-child(1) {
          border-radius: 10rpx;
          color: #333;
          // border: 1px solid #000;
          width: 200rpx;
          background-color: $pageBgColor;
          text-align: justify;
          // height: 40rpx;
          /* 固定高度 */
          line-height: 40rpx;
          /* 与高度相同 */
          overflow: hidden;
          /* 防止溢出 */

          /* 优化后的伪元素方案 */
          &::after {
            content: "";
            display: inline-block;
            width: 100%;
            height: 0;
            /* 消除高度影响 */
            vertical-align: top;
            /* 对齐顶部 */
          }
        }

        > view:nth-child(2) {
          // border: 1px solid #000;
          width: 450rpx;
        }
      }

      border-bottom: 2rpx solid #999;
    }

    .center_btn {
      // padding: 0 10rpx;
      // border: 1px solid #000;

      margin-top: 20rpx;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      > view {
        width: 190rpx;
        font-size: 22rpx;
        // border: 1px solid #000;
        margin: 10rpx 5rpx;
        font-weight: bolder;
      }
    }
  }
}
</style>
