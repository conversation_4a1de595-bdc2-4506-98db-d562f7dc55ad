<template>
  <view>
    <BaseModal
      :show="show"
      @confirm="confirmVolume"
      @cancel="cancelVolume"
      @close="handleClose"
    >
      <view slot="default">
        <!-- 调节音量 -->
        <view class="wrap">
          <slider
            :value="volume"
            @change="sliderChange"
            min="0"
            max="100"
            step="1"
            activeColor="#FFCC33"
            backgroundColor="#000000"
            block-color="#8A6DE9"
            block-size="20"
          />
          <view style="margin-top: 15px; font-size: 15px">
            音量值:{{ volume }}
          </view>
        </view>
      </view>
    </BaseModal>
  </view>
</template>

<script>
import BaseModal from "../base/BaseModal.vue"

export default {
  components: {
    BaseModal,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    defaultVolume: {
      type: Number,
      default: 50,
    },
    deviceSn: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      volume: this.defaultVolume,
    }
  },
  watch: {
    defaultVolume(val) {
      this.volume = val
    },
    show(val) {
      if (val) {
        this.volume = this.defaultVolume
      }
    },
  },
  methods: {
    sliderChange(e) {
      this.volume = e.detail.value
    },
    confirmVolume() {
      this.$emit("confirm", this.volume)
      // 🔥 确认后自动关闭弹窗
      this.handleClose()
    },
    cancelVolume() {
      this.$emit("cancel")
      // 🔥 取消后自动关闭弹窗
      this.handleClose()
    },
    handleClose() {
      // 通知父组件关闭弹窗
      this.$emit("update:show", false)
    },
  },
}
</script>

<style lang="scss" scoped>
.wrap {
  padding: 10rpx 20rpx;
}
</style>
