<template>
  <view class="screener">

    <view class="screener_list_item">
      <view>{{ vPointName }}名称</view>
      <view>
        <BaseInput v-model="placeNmae" placeholder="请输入名称" />
      </view>
    </view>
    <view class="screener_list_item">
      <view>设备编号</view>
      <view>
        <BaseInput v-model="deviceSn" placeholder="请输入设备编号" />
      </view>
    </view>
    <view class="screener_list_item">
      <view>订单/支付单号</view>
      <view>
        <BaseInput v-model="orderSn" placeholder="请输入订单编号/支付单号" />
      </view>
    </view>
    <view class="screener_list_item">
      <view class="list_item">订单类型</view>
      <view class="list_item">
        <BaseInput placeholder="请选择订单类型" type="select" :list="deviceStatusList" :selectValue="selectDeviceStatus.label"
          :index="selectDeviceStatus.value" @confirm="confirmDeviceStatus" />
      </view>
    </view>
    <view class="screener_list_item">
      <view>筛选时间</view>
      <view>
        <TimeSelect v-model="startTime" placeholder="请选择开始时间" />
      </view>
    </view>
    <view class="screener_list_item">
      <view class="time_end_title">筛选时间</view>
      <view>
        <TimeSelect v-model="endTime" placeholder="请选择结束时间" />
      </view>
    </view>
    <view class="btn">
      <BaseButton :width="330" type="default" @onClick="resetData">重置</BaseButton>
      <BaseButton :width="330" type="primary" @onClick="confirm">确认</BaseButton>
    </view>
  </view>
</template>

<script>
import BaseInput from "@/components/base/BaseInput.vue";
import TimeSelect from "@/components/common/TimeSelect.vue";
import BaseButton from "@/components/base/BaseButton.vue";
export default {
  name: "OrderScreener", //订单筛选
  components: { BaseInput, TimeSelect, BaseButton },
  data() {
    return {
      placeNmae: "",
      deviceSn: "",
      orderSn: "",
      startTime: "",
      endTime: "",
      deviceStatusList: [
        {
          value: 0,
          label: "全部",
        },
        {
          value: 1,
          label: "普通订单",
        },
        {
          value: 2,
          label: "免费订单",
        },
        {
          value: 8,
          label: "预付订单",
        },
      ],
      selectBindStatus: { value: 0, label: "全部" },
      selectDeviceStatus: {},
    };
  },
  methods: {
    // 新增方法：获取默认开始时间（今天）
    getDefaultStartTime() {
      return this.formatDate(new Date());
    },
    // 新增方法：获取默认结束时间（今天）
    getDefaultEndTime() {
      return this.formatDate(new Date());
    },
    // 日期格式化辅助方法
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    confirmDeviceStatus(e) {
      this.selectDeviceStatus = e[0];
    },
    confirmBindStatus(e) {
      this.selectBindStatus = e[0];
    },
    resetData() {
      this.placeNmae = "";
      this.deviceSn = "";
      this.orderSn = "";
      this.startTime = "";
      this.endTime = "";
      this.selectDeviceStatus = this.deviceStatusList[0];
      // this.selectBindStatus = this.bindStatusList[0];
      // 修复undefined错误，确保数组存在且至少有一个元素
      this.selectDeviceStatus = this.deviceStatusList.length > 0 ? this.deviceStatusList[0] : {};
      // 如果存在bindStatusList也需要同样处理
      if (this.bindStatusList && this.bindStatusList.length > 0) {
        this.selectBindStatus = this.bindStatusList[0];
      }

      // 新增：通知父组件重置时间选择状态
      this.$emit("reset", {
        dianwei: "",
        device_sn: "",
        order_sn: "",
        start_time: "",
        end_time: "",
        prom_type: 0, // 重置为"全部订单"
      });

    },
    confirm() {
      let data = {
        dianwei: this.placeNmae,
        device_sn: this.deviceSn,
        order_sn: this.orderSn,
        start_time: this.startTime, // 默认今天
        end_time: this.endTime,       // 默认今天
        prom_type: this.selectDeviceStatus.value,
      };
      console.log(data, '筛选的数据');
      this.$emit("confirm", data);
    },
  },
};
</script>

<style lang="scss" scoped>
.screener {
  padding: 50rpx 30rpx 30rpx;

  &_list {
    &_item {
      display: flex;
      align-items: center;

      >view {
        margin-bottom: 30rpx;

        &:first-child {
          margin-right: 20rpx;
          white-space: nowrap;
          font-size: $font-size-base;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: $textBlack;
        }

        &:last-child {
          width: 100%;
        }
      }

      .time_end {
        opacity: 0;
      }
    }
  }

  .btn {
    display: flex;
    justify-content: space-between;
  }
}
</style>