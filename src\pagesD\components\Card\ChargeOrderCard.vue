<template>
  <view class="card">
    <view class="content border-bottom">
      <view class="info flexColumnBetween">
        <view class="goods_name flexRowBetween">
          <view class="name">{{info.order_type == 1 ? "充电订单":"普通订单"}}</view>
          <view class="order-time">{{
            $u.timeFormat(info.create_time * 1000, "yyyy-mm-dd hh:MM:ss")
          }}</view>
        </view>
        <view class="flexRowBetween">
          <view>
            <text>{{info.unit == 3 ? "时长：":"数量："}}</text><text class="num">{{ info.duration }}</text>
          </view>
          <view>{{ info.unit == 3 ? (info.rule_unit == 2 ? "小时" : "分钟") : "" }}</view>
        </view>
        <view class="flexRowBetween">
          <view>
            <text>价格：</text>
            <text class="price">￥{{ info.amount }}</text>
          </view>
          <view class="status flexRowAllCenter" @click="show = !show">
            <block v-if="info.prom_type == 2">
              <view>免费</view>
            </block>
            <block v-else>
              <image
                v-if="info.pay_status == 2"
                class="status_icon"
                src="@/pagesB/static/img/icon/finish_icon.png"
              />
              <view :style="{ color: info.pay_status == 2 ? '#0EADE2' : 'red' }">
                {{ pay_status[info.pay_status] }}
              </view>
            </block>
          </view>
        </view>
      </view>
    </view>
    <view class="details">
      <block v-if="info.order_type == 1">
        <view>
          <view>充电状态</view>
          <view>{{ charge_status[info.charge_status] }}</view>
        </view>
        <view v-if="info.pay_status == 2">
          <view>充电时间</view>
          <view>
            <view>
              {{ $u.timeFormat(info.start_time * 1000, "yyyy-mm-dd hh:MM:ss") }}
            </view>

            <view>
              {{ $u.timeFormat(info.end_time * 1000, "yyyy-mm-dd hh:MM:ss") }}
            </view>
          </view>
        </view>
      </block>
      <view>
        <view>{{ vPointName }}</view>
        <view>{{ info.hotelName }}</view>
      </view>
      <view>
        <view>设备编号</view>
        <view>{{ info.device_sn }}</view>
      </view>
      <view>
        <view>订单编号</view>
        <view>{{ info.charge_sn }}</view>
      </view>
      <view>
        <view>买家名称</view>
        <view>{{ info.buyer || info.openid }}</view>
      </view>
      <view>
        <view>支付方式</view>
        <view>{{ info.pay_name }}</view>
      </view>
      <view>
        <view>支付时间</view>
        <view>{{
          $u.timeFormat(info.pay_time * 1000, "yyyy-mm-dd hh:MM:ss")
        }}</view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: "ChargeOrderCard",
  props: {
    info: { type: Object, default: {} },
  },
  data() {
    return {
      pay_status: {
        1: "待支付",
        2: "支付成功",
      },
      charge_status: {
        1: "未充电",
        2: "充电中",
        3: "订单完成",
      },
      show: false,
    };
  },

  methods: {},
  onLoad() {},
};
</script>

<style scoped lang="scss">
.card {
  padding: 20rpx;
}
.card {
  padding: 20rpx;
  .border-bottom {
    border-bottom: 2rpx solid $dividerColor;
  }
  .content {
    box-sizing: content-box;
    display: flex;
    height: 150rpx;
    transition: all 0.4s;
    padding-bottom: 20rpx;
    .img {
      width: 150rpx;
      height: 100%;
      background: #bfbfbf;
    }
    .info {
      flex: 1;
      height: 100%;
      font-size: $font-size-small;
      color: $textDarkGray;
      .goods_name {
        .name {
          color: $textBlack;
          font-size: $font-size-middle;
          font-weight: bold;
        }
        .status {
          color: $themeComColor;
          font-size: $font-size-xsmall;
          .status_icon {
            width: 30rpx;
            height: 30rpx;
            margin-right: 6rpx;
          }
        }
      }

      .num {
        color: $textBlack;
      }
      .price {
        color: #ef0000;
      }
      .status {
        color: $themeComColor;
        .arrow {
          transition: all 0.4s;
          margin-left: 6rpx;
        }
        .rotate {
          transform: rotate(90deg);
        }
        .status_icon {
          width: 30rpx;
          height: 30rpx;
          margin-right: 6rpx;
        }
      }
    }
  }
  .details {
    > view {
      margin-top: 30rpx;
      display: flex;
      justify-content: space-between;
      font-size: $font-size-base;
      > view {
        &:first-child {
          color: $textDarkGray;
        }
        &:last-child {
          color: $textBlack;
        }
      }
      .flex_white {
        white-space: nowrap;
        margin-right: 20rpx;
      }
    }
  }
}
.again-open {
  justify-content: flex-end !important;
}
</style>
