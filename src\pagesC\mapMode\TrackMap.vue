<template>
    <view>
        <BaseNavbar :title="title" />
        <view class="map_layout">
            <map id="map" :latitude="latitude" :longitude="longitude" :scale="scale" :markers="calloutMarkers"
                :polyline="polyline">
            </map>
        </view>
    </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import { locationMixin } from "@/mixins/locationMixin";
import { globalKeys } from "@/global/globalKeys";
import BaseIcon from '@/components/base/BaseIcon.vue'
export default {
    components: {
        BaseNavbar,
        BaseIcon
    },
    mixins: [locationMixin, globalKeys],
    data() {
        return {
            title: "GPS路径",
            // 中心点经纬度
            latitude: 40.03245000000001,
            longitude: 116.272472,
            // 地图缩放等级
            scale: 18,
            // 路径数据
            polyline: [
                {
                    points: [{
                        latitude: 40.040129,
                        longitude: 116.274968
                    },
                    {
                        latitude: 40.038974,
                        longitude: 116.275214
                    },
                    {
                        latitude: 40.038974,
                        longitude: 116.275214
                    },
                    {
                        latitude: 40.038565000000006,
                        longitude: 116.272683
                    },
                    {
                        latitude: 40.03848200000001,
                        longitude: 116.27209500000001
                    },
                    {
                        latitude: 40.03836100000001,
                        longitude: 116.27074
                    },
                    {
                        latitude: 40.03832700000001,
                        longitude: 116.270515
                    },
                    {
                        latitude: 40.03807400000001,
                        longitude: 116.268038
                    },
                    {
                        latitude: 40.03801400000001,
                        longitude: 116.26763600000001
                    },
                    {
                        latitude: 40.03801400000001,
                        longitude: 116.26763600000001
                    },
                    {
                        latitude: 40.03790800000001,
                        longitude: 116.267508
                    },
                    {
                        latitude: 40.03450300000001,
                        longitude: 116.270961
                    },
                    {
                        latitude: 40.03419900000001,
                        longitude: 116.271221
                    },
                    {
                        latitude: 40.03396500000001,
                        longitude: 116.271401
                    },
                    {
                        latitude: 40.03245000000001,
                        longitude: 116.272472
                    }],
                    color: '#3875FF',
                    width: 8,
                    dottedLine: false,
                    arrowLine: false,
                    borderWidth: 0,
                    arrowLine: true
                }

            ],
            // 标记点数据
            calloutMarkers: [
                {
                    id: 1,
                    callout: {

                        content: '我',
                        padding: 12,
                        display: 'ALWAYS',
                        fontSize: 14,
                        textAlign: 'center',
                        borderRadius: 4,
                        borderWidth: 2,
                        bgColor: '#ffffff'


                    },
                    latitude: 40.03245000000001,
                    longitude: 116.272472,
                    width: 30,
                    height: 40
                },
                {
                    id: 2,
                    callout: {
                        content: '小猫',
                        padding: 12,
                        display: 'ALWAYS',
                        fontSize: 14,
                        textAlign: 'center',
                        borderRadius: 4,
                        borderWidth: 2,
                        bgColor: '#ffffff'

                    },
                    latitude: 40.040129,
                    longitude: 116.274968,
                    width: 30,
                    height: 40
                }
            ],
            mapCtx: {}

        };
    },
    methods: {

        run() {
            this.mapCtx.moveAlong({
                markerId: 2,
                path: this.polyline[0].points,
                duration: 10000,
                autoRotate: true,
                success(res) {
                    console.log(res)
                },
                fail(err) {
                    console.log(err)
                }
            })
        },

    },
    onLoad(options) {

        this.mapCtx = uni.createMapContext("map"); // map为地图的id
        this.run()

    },
};
</script>


<style lang="scss" scoped>
.map_layout {
    width: 100%;
    height: 90vh;
    background-color: #9a9a9a;

    map {
        width: 100%;
        height: 100%;
    }
}

.tabs_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .tabs {
        width: 100%;
    }

    .device_num {
        white-space: nowrap;
        margin-right: 10rpx;
        color: $themeComColor;
    }
}

.map_card {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    // top: 85vh;
    z-index: 99999;
    background-color: #fff;

    .map-card_line {
        margin: auto;
        height: 10rpx;
        width: 100rpx;
        background-color: #9a9a9a
    }

}

.scroll_card {

    // .mpa_card_item {
    //   display: flex;
    //   flex-direction: column;
    //   align-items: center;
    // }
    .scrool_list {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .map_card_title {
        width: 550rpx;
        margin-bottom: 10rpx;
        font-size: 35rpx;
        font-weight: bold;



    }

    .map-card_line {
        margin: auto;
        height: 10rpx;
        width: 100rpx;
        background-color: #9a9a9a
    }
}




.mpa_card_item {
    width: 700rpx;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    // border: 1rpx solid #333;
    height: 170rpx;
    border-radius: 15rpx;
    padding: 10rpx;
    margin: 10rpx;
    align-items: center;


    .map_card_title {
        width: 550rpx;
        margin-bottom: 10rpx;
        font-size: 35rpx;
        font-weight: bold;



    }

    .map_card_title_span {
        font-size: 25rpx;
        // color: #9a9a9a;
    }
}

.card_line {
    height: 2rpx;
    width: 700rpx;
    margin: auto;
    background-color: #9a9a9a;
    margin-bottom: 10rpx;
}

.map_card_msg {

    padding: 0 20rpx 20rpx 20rpx;

}

.map_card_btn {
    padding: 20rpx;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;


    .image {
        width: 50rpx;
        height: 50rpx;
        border-radius: 50%;
        overflow: hidden;
    }
}
</style>