<template>
  <view class="card" @click="lookDetails">
    <view class="imgdis card_item" v-if="info.use_status == 2 && (status == 0 || status == 3)">
      <image class="stock_icons"
        src="data:image/png;base64,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" />
    </view>
    <view class="point-name card_item">
      <view class="device-code card_item">
        <view class="card_last">设备编号：</view>
        <text user-select>{{ info.device_sn }}</text>
      </view>
      <view class="stock card_item">
        <view class="stock_item card_last" v-if="(status == 0 || status == 1) && info.is_pp_device">
          <view class="stop_flex stock_items">
            <view class="stock_top">
              <image class="stock_icons" :src="shui_src()" />
            </view>
            <view class="stock_width">
              <view class="stock_buttom" :style="{ color: colors[info.game_time_status] }">{{ num() }}</view>
              <view class="stock_warn" :style="{ color: colors[info.game_time_status] }">{{ gameStatusText() }}
              </view>
            </view>
          </view>
        </view>
        <view class="stock_item card_last" v-if="(status == 0 || status == 1) && info.is_cannon_device">
          <view class="stop_flex stock_items">
            <view class="stock_top">
              <image class="stock_icons" :src="yan_src()" />
            </view>
            <view class="stock_width">
              <view class="stock_yan" :style="{ color: colors2[info.water_status] }">{{ yanStatusText() }}</view>
              <view class="stock_warn" :style="{ color: colors2[info.water_status] }">烟油
              </view>
            </view>
          </view>
        </view>
        <view class="stock_item card_last" v-if="[status == 0 || status == 2] && info && info.isShowEle">
          <view class="stop_flex stock_items">
            <view class="stock_top_d">
              <view class="stock_zhe"
                :style="{ height: `${info.electricity_per.toFixed(0)}%`, backgroundColor: `${colors1[info.electricity_status]}` }">
              </view>
              <image class="stock_icond image"
                :src="info.electricity_status == 0 ?
    'data:image/png;base64,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' :
    'data:image/png;base64,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'" />
            </view>
            <view>
              <view class="stock_buttom" :style="{ color: colors1[info.electricity_status] }">{{
    info.electricity_per.toFixed(0) || 0 }}%
              </view>
              <view class="stock_warn" :style="{ color: colors1[info.electricity_status] }">{{ electricityStatusText()
                }}
              </view>
            </view>
          </view>
        </view>
        <view class="stock_item card_last" v-if="status == 0">
          <view class="stop_flex_s stock_items">
            <view class="stock_top">
              <view class="stock_buttom" :style="{ color: !info.csq_level_status ? '#fb2020' : '#3cc6ff' }">{{
    info.csq_level || 0 }}</view>
              <image class="image" :src="src()" />
            </view>
            <view>
              <view class="stock_warn" :style="{ color: !info.csq_level_status ? '#fb2020' : '#3cc6ff' }">信号</view>
            </view>
          </view>
        </view>

      </view>
    </view>
    <view class="point-name card_item">
      <view class="device-code card_item">
        <view class="card_last">系统码：</view>
        <text user-select @click.stop="navigator()">{{ info.vCode != 0 && info.vCode ? info.vCode : '未绑定' }}</text>
      </view>
    </view>
    <view class="point-name card_item">
      <view class="device-code card_item">
        <view class="card_last">{{ vPointName }}：</view>
        <view class="address textMaxTwoLine card_last" @click.stop="navigator(info)">{{ info.hotelName }}{{
    info.room_num ? "(" + info.room_num + ")" : ""
  }}</view>
      </view>
    </view>
    <view class="flexRowBetween card_item" v-if="info.deviceTypeName">
      <view class="device-code card_item">
        <view class="card_last">设备类型：</view>
        <view class="card_last">{{ info.deviceTypeName }}</view>
      </view>
    </view>
    <view class="point-name card_item">
      <view class="device-code card_item">
        <view class="card_last">最近成交：</view>
        <view class="address textMaxTwoLine card_last">{{ info.lastest_order_time_str }}</view>
      </view>
    </view>
    <view class="point-name card_item" v-if="info.use_status == 2 && (status == 0 || status == 3)">
      <view class="device-code card_item">
        <view class="card_last">禁用理由：</view>
        <view class="card_last" :style="{ color: 'red' }">{{ info.reason || '无' }}</view>
      </view>
    </view>
    <view class="flexRowBetween card_item" style="justify-content: flex-start">
      <view class="device-code card_item">
        <view class="card_item card_last">
          <text>{{ info.isGameDevice ? "游戏规则：" : info.isRechargeDevice ? "充电规则：" : "库存总数：" }}</text>
        </view>
        <view class="card_last"><text>{{ shopStatus() }}</text></view>
      </view>
    </view>

    <view class="point-name flex" v-if="info.isShowLEStauts">
      <view class="card_item">
        <view class="device_item card_last" v-if="info.is_car_device">
          <view>灯和音响：</view>
          <view :style="{ color: info.relay_two == 1 ? '#0EADE2' : 'red' }">{{ info.relay_two == 1 ? '开启' : '关闭'
            }}
          </view>
        </view>
        <view class="device_item card_last" v-else-if="info.is_pp_device">
          <view>灯状态：</view>
          <view :style="{ color: info.light_status == 1 ? '#0EADE2' : 'red' }">{{ info.light_status == 1 ? '开启' : '关闭'
            }}
          </view>
        </view>
      </view>

      <view class="card_item font_m" v-if="info.time_on_off && !info.is_cannon_device">
        <view class="device_item">
          <view>开机时间段:</view>
          <view>{{ info.time_on_off
            }}
          </view>
        </view>
      </view>
      <view class="card_item">
        <view class="device_item card_item" v-if="info.is_car_device">
          <view class="card_last">泡泡车：</view>
          <view class="card_last" :style="{ color: info.relay_three == 1 ? '#0EADE2' : 'red' }">{{ info.relay_three == 1 ?
    '开启' : '关闭'
            }}</view>
        </view>
        <view class="device_item card_item" v-else>
          <view class="card_last">提示音状态：</view>
          <view class="card_last" :style="{ color: linghtStatus() ? '#0EADE2' : 'red' }">{{ linghtStatus() ? '开启' : '关闭'
            }}</view>
        </view>
      </view>
    </view>

    <!-- <view class="point-name" v-if="info.use_status==1">
      <view class="device-code">
        <view>灯光开启时间：</view>
        <view>{{ info.startTime||'09:00' }} - {{ info.endTime||'10:00' }}</view>
      </view>
    </view> -->
    <view class="card_item">
      <view class="online card_item">
        <block>
          <image class="online_icon" :src="line_src()" />
          <view :class="info.status == 1 ? 'theme-color' : 'text-warn'">
            {{ info.status == 1 ? '在线' : info.status == 2 ? '异常' : info.status == 3 ? '离线' : '' }}</view>
        </block>
      </view>

      <view class="themeComColor card_item" @click.stop="lookDetails">查看详情</view>
    </view>
    <view class="updata" v-if="info.isShowLEStauts && !info.is_car_device">
      <BaseUpdata title="状态" :time="info.report_time" @onClick="updataClick" :refresh="manyUpdata" iosSize="35">
      </BaseUpdata>
    </view>
  </view>
</template>

<script>
import BaseUpdata from "@/components/base/BaseUpdata.vue";
import { navigateToReplenishList } from '@/wxutil/navgate.js'
export default {
  components: {
    BaseUpdata,
  },
  name: "ReplenishListCard",
  props: {
    info: {
      type: Object,
      default: function () {
        return {};
      }
    },
    status: { type: Number, default: 0 }
  },
  data() {
    return {
      electricity: 1700,
      manyUpdata: false,
      colors: {
        0: '#bfbfbf',
        10: '#fb2020',
        20: '#fb9611',
        30: '#38d33a',
      },
      colors2: {
        0: '#fb2020',
        1: '#3cc6ff',
      },
      colors1: {
        0: '#bfbfbf',
        10: '#fb2020',
        15: '#fb9611',
        20: '#3cc6ff',
        30: '#38d33a',
      },
      imageMap: {
        start30: 'data:image/png;base64,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',
        start25: 'data:image/png;base64,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',
        start22: 'data:image/png;base64,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',
        start19: 'data:image/png;base64,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',
        start16: 'data:image/png;base64,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',
        start0: 'data:image/png;base64,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'
      }
    }
  },

  methods: {
    navigator(item) {
      console.log('跳转信息', item)
      if (item && item.dianweiid && item.hotel_id)
        uni.navigateTo({
          url: `/pagesB/device/DeviceList?from=place&dianwei=${item.dianweiid}&hotel_id=${item.hotel_id}`,
        })
    },
    linghtStatus() {
      if (this.info && this.info.tip_music) {
        if (this.info.tip_music.split(',')[0] == 0) {
          return false
        } else {
          return true
        }
      } else {
        return false
      }
    },
    async lookDetails() {
      try {
        await navigateToReplenishList("/pagesB/device/DeviceGoodsList?from=replenish&device_sn=" +
          this.info.device_sn,)
      } catch (error) {
        console.log(error)
      }
    },
    /* 更新时间 */
    updataClick() {
      this.$emit('updataTime');

    },
    shopStatus() {
      // 状态
      if (this.info && this.info.shopstatus) {
        return this.info.shopstatus.replace(/\\n/g, "\n");
      } else {
        return "未设置";
      }
    },
    num() {
      if (this.info && this.info.remain_game_time > 0) {
        return (this.info.remain_game_time / this.vGamingTime).toFixed(1)
      } else {
        return 0
      }
    },
    src() {
      if (this.info && this.info.csq_level) {
        if (this.info.csq_level >= 28) {
          return this.imageMap["start30"];
        } else if (this.info.csq_level >= 25) {
          return this.imageMap["start25"];
        } else if (this.info.csq_level >= 22) {
          return this.imageMap["start22"];
        } else if (this.info.csq_level >= 19) {
          return this.imageMap["start19"];
        } else if (this.info.csq_level >= 16) {
          return this.imageMap["start16"];
        } else {
          return this.imageMap["start0"];
        }
      } else {
        return this.imageMap["start0"];
      }
    },
    line_src() {

      const status = this.info && this.info.status ? this.info.status : 1;
      const iconMap = {
        1: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB0AAAAZCAMAAAD63NUrAAAAbFBMVEUAAAAPreIOruMNreIMreIPruEOreIQr+QOreIOreIOreINruMNreMNrOINreIMq+IAtvMNreIPpeQOreIOreIOreIOruIOrOIOreINrOIOruIOrOMOreIOreIOrOMQrOATp+IOreISreQOreKC3TJ4AAAAI3RSTlMAZ5DRPnf4MJXyxIhkPDgpBF8J+puNasy4rKV+fVtIMQ3cHOBJIAcAAACxSURBVCjPtdDJEoIwEEXRBygyhFHmWd///6PRSoExYeldpFJ9qjcNZGPgWqoFZGvIk/ociBg5wjGaA3IG7rzBWs/prZldY14B39wtvBGAa9eCjE09sMrP1JEIaFoOQ7ljgF8loxyyRqGmSHzJCk1FGjLaBOnCUMUPsoapinc89Ljk6sXGJanUKOAEeOyW9PIpSeSjvqkIKYCFZ7UbgKTzbLVxif+XOV81Ra7hk3qVmr8AXwoYSV2JFagAAAAASUVORK5CYII=',
        2: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAaCAMAAACTisy7AAAAk1BMVEUAAADvAADuAADwAADvAADvAADuAADvAADuAADvAADvAADzAAD/AADwAADvAADwAADvAADvAADuAADyAADvAADwAADwAADvAADwAADxAADpAADvAADvAADvAADwAADvAADwAADwAADvAADvAADvAADuAADuAADsAADwAADvAADvAADvAADwAADwAADyAADuAADvAADF669fAAAAMHRSTlMAq4llgHwt/KmdPhQF6a2Ft1FLGPDXxZ4yIwvlz7ykmJVzb2JcWjko9+Pa0UM0Jw+n0PDWAAAA2klEQVQoz7XSyW7CQBBF0ecRpwfPxgMQZsic9/9fFyOkJO5uxIor1aJ0NrUooNploV0WCQCd5o0yYM4339WKFCBPcDZnBLK/LpWBG4aYMb4uOpniE6M/LJkqDFIqB57LAxvsydejhd818cFciiJnYGJAAZTML2culIHPy3HSglqi0NLArFb43KqOS0R6MPCLeawAxHyv19a1xQvZIEzFgr2FkIG/hWgHqZN/eMa0qsOl9YhkDGfeiA1Xh31gdfTJEmLGG7UATq3nLMBjU7vUS35LN5OX6a2vvN8PEWEhuxVMXA0AAAAASUVORK5CYII=',
        3: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAaCAMAAACTisy7AAAAk1BMVEUAAADvAADuAADwAADvAADvAADuAADvAADuAADvAADvAADzAAD/AADwAADvAADwAADvAADvAADuAADyAADvAADwAADwAADvAADwAADxAADpAADvAADvAADvAADwAADvAADwAADwAADvAADvAADvAADuAADuAADsAADwAADvAADvAADvAADwAADwAADyAADuAADvAADF669fAAAAMHRSTlMAq4llgHwt/KmdPhQF6a2Ft1FLGPDXxZ4yIwvlz7ykmJVzb2JcWjko9+Pa0UM0Jw+n0PDWAAAA2klEQVQoz7XSyW7CQBBF0ecRpwfPxgMQZsic9/9fFyOkJO5uxIor1aJ0NrUooNploV0WCQCd5o0yYM4339WKFCBPcDZnBLK/LpWBG4aYMb4uOpniE6M/LJkqDFIqB57LAxvsydejhd818cFciiJnYGJAAZTML2culIHPy3HSglqi0NLArFb43KqOS0R6MPCLeawAxHyv19a1xQvZIEzFgr2FkIG/hWgHqZN/eMa0qsOl9YhkDGfeiA1Xh31gdfTJEmLGG7UATq3nLMBjU7vUS35LN5OX6a2vvN8PEWEhuxVMXA0AAAAASUVORK5CYII='
      };
      return iconMap[status];
    },
    shui_src() {
      const status = this.info && this.info.game_time_status ? this.info.game_time_status : 0;
      const iconMap = {
        0: 'data:image/png;base64,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',
        10: 'data:image/png;base64,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',
        20: 'data:image/png;base64,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',
        30: 'data:image/png;base64,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'
      };
      return iconMap[status];
    },
    yan_src() {
      const status = this.info && this.info.water_status ? this.info.water_status : 0;
      const iconMap = {
        0: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC4AAAA4CAYAAACCNsqxAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAABLlpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQ1IDc5LjE2MzQ5OSwgMjAxOC8wOC8xMy0xNjo0MDoyMiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0RXZ0PSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VFdmVudCMiIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDozNTNFREQ2MDA2QTgxMUVFQjcyRkNBNjU1Q0IxMDQ3QiIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDozNTNFREQ1RjA2QTgxMUVFQjcyRkNBNjU1Q0IxMDQ3QiIgZGM6Zm9ybWF0PSJpbWFnZS9wbmciIHhtcDpDcmVhdGVEYXRlPSIyMDIzLTA2LTA3VDExOjA0OjQyKzA4OjAwIiB4bXA6TW9kaWZ5RGF0ZT0iMjAyMy0wNi0wOVQxNzozMDowMSswODowMCIgeG1wOk1ldGFkYXRhRGF0ZT0iMjAyMy0wNi0wOVQxNzozMDowMSswODowMCIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ0MgMjAxOSAoV2luZG93cykiPiA8eG1wTU06SGlzdG9yeT4gPHJkZjpTZXE+IDxyZGY6bGkgc3RFdnQ6YWN0aW9uPSJjb252ZXJ0ZWQiIHN0RXZ0OnBhcmFtZXRlcnM9ImZyb20gaW1hZ2UvanBlZyB0byBhcHBsaWNhdGlvbi92bmQuYWRvYmUucGhvdG9zaG9wIi8+IDwvcmRmOlNlcT4gPC94bXBNTTpIaXN0b3J5PiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0iNzc3ODNGMDA0OTU4NTQ2RjAyMjdBNEE0QkMyRjdGNkUiIHN0UmVmOmRvY3VtZW50SUQ9Ijc3NzgzRjAwNDk1ODU0NkYwMjI3QTRBNEJDMkY3RjZFIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+twFc/wAABDtJREFUeNrsml1IVEEUx8+627rbtm7WpihphgqZBomYKfoSEaVkBT1Uz33Qcw+BUVCB4UNBDxG99FBJ9KIgGVhJBT5kZdmHIalF6lLrZma2a1er7ZzLuTKu19B2bns3PPBj7p2dnf3fM2fOzFy1AIATOYfsQRaBXAsjN5HDyBf+jVNIOn/+i9skIBbkJ9dbuYy8DyLnkVq6uchfNpIWZL3E/rbTU4bY68eQ2+wFP/IjCk+TB1OQfKSe60YRD/KWPU8CLiPrkJM8MneQpchuZBB5yN/digyzt8uQMzZhGOgH3vN1GrILcSOT8xBM/SnsgOcM9X+FRZN9Rh7z9QiXHcgTZILv6QG+Cv22cDnIpWJjYXZkGQs/iFySEN+nkRPIVSSLY5vMJbSxc7lMGCnNcSGhHd1/EL77zSZ8GGCvaKLbkBfI4nmIHUeykS3IcaQVecAPkYns50mo2SoutdHwCg9kiRhJskQuM0ThNMSb+JqGrSIKbzciO5FqFk52gEf0kdDuLLIaaef7OmQJMsQZ5YIwP8iakc3IPtXtPFFWIJV8/TLKMLnH/dRJTq9ruN9J0eNOHlrKKgVIL9LHDedjKzmbkN2QLNwpRMeUx4u4ci/HajR5lh7+CMi3Iu5/2uTUJsN1Tk9ViOMvOlc4VJ6BgWaLWJ41e8OY1iwcKq6amhrIzMw0s1bo7++H2lp1mxJMgDi1BeELwv934TbZHWZkZIDX64VEu32q7tPwMAQCARgdHTWXcBK7rqBALf9kfr8fOp4+hYGBgdgLLywshA3FxXNqm5qaCpXbtkFfXx/cbW2NbYyLITFXy87Oho0lJfE5OfPy8sCRmGiOyRkMBqG3t1edjIqiQJLbDbm5uWqIRJodR2olzglqHzPhJPhhe7uuiK7Xr2Hnjh264r3Ll8dOOHm3s7MTvivKrG1edXXpCo9pVpmLx5RZHmpsbMzck3N1VpZu/aDPZ17hKSkpavrTW4yiWUkTjBZdVVmpZhDRJiYm4N79++baq2iWk5MDFeXluqKbb92Ket9iiPD8tWuhHEVHmiZ6aGjIfLtDCg890SMjI9DU1PTHtBnTGC8rLTVctHThHo9nxkJD4SFbtHThSUlJM+poCytbtHTheiskedz0Z07KFrThEs2ol0zSs8q1+vqFU/4/9TidavJwAVrFIdLT06PuyU0tnERXV1dDcnLytANyWlpa1IdjQ0OFdoGiaLGeVlTTCne73bO/DYjiYGy48Lfv3unWUy4PSNhYGZrH29rapi062o5Q9uopPatQBiG013EyXrf904OEUYLjfgGKa+Hq3zctFovpxQoaLQma161Wq+mFCxqtJPojXXV3d5teuKDRT1mF/lHgaENDA4TDYfW1ghmNTlKkke26ulI7HA56lHA8wFrdWrS709PTL42Pj+8KhUIOM3rc6XR+d7lcjT6f7xDejv0WYAC+xb4zTMd3ngAAAABJRU5ErkJggg==',
        1: 'data:image/png;base64,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'
      };
      return iconMap[status];
    },
    electricityStatusText() {
      switch (this.info.electricity_status) {
        case 10:
          return '缺电';
        case 15:
          return '警告';
        case 20:
          return '正常';
        case 30:
          return '满电';
        default:
          return '未知';
      }
    },
    gameStatusText() {
      switch (this.info.game_time_status) {
        case 10:
          return '缺水';
        case 20:
          return '警告';
        case 30:
          return '充足';
        default:
          return '未知';
      }
    },
    yanStatusText() {
      switch (this.info.water_status) {
        case 0:
          return '无';
        case 1:
          return '有';
        default:
          return '未知';
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.font_m {
  font-size: 25rpx
}

.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device_item {
  display: flex;
  // width: 220rpx;
  // border: 1px solid #000;
}

.card {
  padding: 20rpx;
  position: relative;

  .updata {

    // width: 100%;
    // border: 1px solid #000;
    // text-align: center;
    display: inline-block;
    width: 100%;

  }

  >.card_item {
    font-size: $font-size-base;
    color: $textDarkGray;
    display: flex;
    justify-content: space-between;
    margin-bottom: 10rpx;

    &:last-child {
      margin-bottom: 0;
    }

    >.card_item {
      display: flex;

      >.card_last {
        &:last-child {
          color: $textBlack;
        }
      }

      .address {
        width: 500rpx;
      }
    }
  }

  .device-code {

    >.card_last:nth-child(1) {

      width: 160rpx;
      text-align: justify;
      height: 40rpx;
      /* 固定高度 */
      line-height: 40rpx;
      /* 与高度相同 */
      overflow: hidden;
      /* 防止溢出 */

      /* 优化后的伪元素方案 */
      &::after {
        content: '';
        display: inline-block;
        width: 100%;
        height: 0;
        /* 消除高度影响 */
        vertical-align: top;
        /* 对齐顶部 */
      }

      // border: 1px solid #000;
    }

    >.card_last:nth-child(2) {
      width: 480rpx;

    }
  }

  .stock {
    align-items: center;
    font-size: $font-size-xsmall;
    position: absolute;
    right: 10rpx;
    top: 20rpx;
    // border: 1px solid #000;
    align-items: flex-end;

    .stock_item {
      .stock_items {
        align-items: flex-end;
      }

      // border: 1px solid #000;
    }

    .stock_icond {
      width: 28rpx;
      height: 45rpx;
      margin-right: 6rpx;
      margin-left: 6rpx;
    }

    .stock_icons {
      width: 38rpx;
      height: 46rpx;
      margin-right: 6rpx;
      margin-left: 6rpx;
      vertical-align: bottom;

    }

    .stop_flex {
      // border: 1px solid #000;
      display: flex;
      margin-right: 5rpx;

      .stock_top {
        // border: 1px solid #000;

        .image {
          vertical-align: bottom;
        }
      }


    }


    .stop_flex_s {
      .stock_top {

        // display: flex;
        text-align: center;
        font-size: 20rpx;
        // align-items: center;
        // border: 1px solid #000;

        // vertical-align: bottom;
        .image {
          margin: auto;
          height: 20rpx;
          // border: 1px solid #000;
          width: 25rpx;
          // height: 100%;
          // width: 100%;
          vertical-align: bottom;
        }

        position: relative;

        .stock_buttom {
          font-size: 8rpx;
          position: absolute;
          left: 0rpx;
          top: -10rpx;
          width: 30rpx;
          height: 25rpx;
          // border: 1px solid #000;
          text-align: left;

        }
      }

    }

    .stock_top_d {
      // border: 1px solid #000;
      width: 35rpx;
      display: flex;
      position: relative;
      margin-right: 5rpx;
      height: 40rpx;

      // overflow: hidden;
      .stock_icond {
        margin: 0;
        padding: 0;
        width: 35rpx;
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 2;
        height: 46rpx;
      }

    }

    .stock_buttom {
      font-size: 16rpx;
      margin: 0 5rpx;

    }



    .stock_warn {
      // color: #ef0000;
      font-weight: bold;
    }

    .stock_yan {
      font-size: 20rpx;
    }

    .stock_adequate {
      color: $themeComColor;
    }

  }

  .stock_zhe {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 35rpx;
    height: 35rpx;
    background: #fb2020;
    z-index: 1;

  }

  .imgdis {
    position: absolute;
    right: 6px;
    bottom: 12px;
    width: 180rpx;
    height: 180rpx;

    // border: 1px solid #000;
    .stock_icons {
      width: 100%;
      height: 100%;
    }
  }

  .themeComColor {
    color: $themeComColor;
  }


}
</style>