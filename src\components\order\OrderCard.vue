<template>
  <view class="card" @click="handleClick">
    <view class="content">
      <!-- 商品图片 -->
      <image class="img" :src="orderData.original_img || defaultImg" />

      <!-- 退款标识 -->
      <view class="imgdis" v-if="orderData.is_refund == 1">
        <image class="stock_icons" src="@/pagesB/static/img/icon/refund.png" />
      </view>

      <!-- 订单信息 -->
      <view class="info flexColumnBetween">
        <!-- 商品名称和时间 -->
        <view class="goods_name flexRowBetween">
          <view class="name">{{ orderData.good_name }}</view>
          <view class="order-time">{{ orderData.add_time }}</view>
        </view>

        <!-- 数量/时长信息 -->
        <view class="flexRowBetween">
          <view>
            <block v-if="orderData.prom_type === 8">
              <view class="prepay-info">
                <text
                  >实际时长:{{
                    orderData.realoutnum >= 0 ? orderData.realoutnum : 0
                  }}分钟</text
                >
                <text>起步时长: {{ orderData.pre_min_time }}分钟</text>
                <text
                  >超时时长:{{
                    orderData.realoutnum - orderData.pre_min_time > 0
                      ? orderData.realoutnum - orderData.pre_min_time
                      : "0"
                  }}分钟</text
                >
              </view>
            </block>
            <block v-else>
              <text>{{ orderData.unit === 3 ? "时长：" : "数量：" }}</text>
              <text class="num">{{
                orderData.unit === 3
                  ? orderData.outnum + "分钟"
                  : orderData.outnum
              }}</text>
            </block>
          </view>
        </view>

        <!-- 价格和状态 -->
        <view class="flexRowBetween">
          <view>
            <block v-if="orderData.prom_type === 8">
              <view class="prepay-pricing">
                <text>实收:</text>
                <text :style="{ color: 'red' }">
                  ¥{{
                    orderData.pay_status == 0 ? "0" : orderData.order_amount
                  }}</text
                >
                <text>起步: ¥{{ orderData.pre_min_price }}</text>
                <text
                  >超时: ¥{{
                    orderData.pay_status == 0
                      ? "0"
                      : orderData.order_amount - orderData.pre_min_price > 0
                      ? (
                          orderData.order_amount - orderData.pre_min_price
                        ).toFixed(2)
                      : "0"
                  }}</text
                >
              </view>
            </block>
            <block v-else>
              <text>价格：</text>
              <text class="price">￥{{ orderData.order_amount }}</text>
            </block>
          </view>
          <view class="status flexRowAllCenter" :style="{ width: '90rpx' }">
            <image
              v-if="orderData.order_status == 1 && orderData.is_refund == 0"
              class="status_icon"
              src="@/pagesB/static/img/icon/finish_icon.png"
            />
            <view
              :style="{
                color:
                  orderData.order_status == 1 && orderData.is_refund == 0
                    ? '#0EADE2'
                    : 'red',
              }"
            >
              {{ getOrderStatus() }}
            </view>
          </view>
        </view>

        <!-- 预付信息 -->
        <view class="myprepay-pricing" v-if="orderData.prom_type === 8">
          <view class="prepay-row">
            <text>预付: ¥{{ orderData.total_amount || "0.00" }}</text>
            <text>还车方式:{{ orderData.end_order_error_str || "" }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view
      class="flexButton"
      v-if="
        (orderData.is_show_refund == 1 && orderData.is_refund == 0) ||
        orderData.order_status == 2
      "
    >
      <view class="returns" @click.stop="handleRefund">退款</view>
      <view class="returns" @click.stop="handleStart">远程启动</view>
      <view class="returns" @click.stop="handleDeviceList">启动其他</view>
      <view
        class="returns"
        :style="{
          background:
            orderData.use_status == 1
              ? 'linear-gradient(268deg, #ff5b5b 0%,  #e61f1f 99%)'
              : '',
        }"
        @click.stop="handleDeviceToggle"
      >
        {{ orderData.use_status == 1 ? "禁用" : "启用" }}设备
      </view>
    </view>

    <!-- 展开/收起按钮 -->
    <view class="flexBottom border_top" @click.stop="show = !show">
      <view></view>
      <view class="flexBottom">
        <view :style="{ color: '#0EADE2' }">查看详情</view>
        <!-- <view class="arrow" :class="show ? 'rotate' : ''">
          <BaseIcon
            color="#0EADE2"
            :name="!show ? 'arrow-down' : 'arrow-up'"
            size="28"
          />
        </view> -->
        <!-- <view class="arrow" :class="show ? 'rotate' : ''">
          <text class="arrow-icon">{{ show ? "" : "▼" }}</text>
        </view> -->
      </view>
    </view>

    <!-- 详细信息 - 与 OrderListCard 完全一致 -->
    <view v-if="show" class="details">
      <view>
        <view>点位名称</view>
        <view>{{ orderData.hotel_name }}</view>
      </view>
      <view v-if="orderData.addressDetail">
        <view class="flex_white">详细位置</view>
        <view class="textMaxTwoLine">{{ orderData.addressDetail }}</view>
      </view>
      <view>
        <view>{{ orderData.unit === 3 ? "货道" : "货道" }}</view>
        <view>{{ orderData.channel || "01" }}</view>
      </view>
      <view>
        <view>设备编号</view>
        <text user-select>{{ orderData.device_sn }}</text>
      </view>
      <view>
        <view>订单编号</view>
        <text user-select>{{ orderData.order_sn }}</text>
      </view>
      <view>
        <view>支付单号</view>
        <text user-select>{{ orderData.transaction_id }}</text>
      </view>
      <view v-if="orderData.pay_name == '汇付支付'">
        <view>商户单号</view>
        <text user-select>{{ orderData.party_order_id || "" }}</text>
      </view>
      <view v-if="orderData.pay_name == '汇付支付'">
        <view>交易单号</view>
        <text user-select>{{ orderData.out_trans_id || "" }}</text>
      </view>
      <view>
        <view>买家名称</view>
        <view>{{ orderData.buyer || orderData.openid }}</view>
      </view>
      <view>
        <view>支付方式</view>
        <view>{{ orderData.pay_name }}</view>
      </view>
      <view v-if="orderData.rake_back_platfrom">
        <view>返利平台</view>
        <view>
          {{
            orderData.rake_back_platfrom === "xh"
              ? "小程序平台"
              : orderData.rake_back_platfrom || ""
          }}
        </view>
      </view>
      <view>
        <view>订单类型</view>
        <view>{{ orderType }}</view>
      </view>
      <view>
        <view>交易时间</view>
        <view>{{ orderData.add_time }}</view>
      </view>
      <block v-if="orderData.prom_type === 8">
        <view>
          <view>还车方式</view>
          <view>{{ orderData.end_order_error_str || "" }}</view>
        </view>
        <view>
          <view>预付</view>
          <view>¥{{ orderData.total_amount }}</view>
        </view>
      </block>
    </view>
  </view>
</template>

<script>
import BaseIcon from "@/components/base/BaseIcon.vue"

export default {
  components: { BaseIcon },
  name: "OrderCard",
  props: {
    // 订单数据
    orderData: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    // 默认图片
    defaultImg: {
      type: String,
      default: "/static/default-product.png",
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      show: false, // 是否展开详细信息
    }
  },
  computed: {
    // 订单状态文本
    statusText() {
      const item = this.orderData
      if (item.is_refund == 1) return "已退款"
      if (item.order_status == 1) return "完成"
      if (item.order_status == 2) return "异常"
      if (item.order_status == 3) return "订单取消"
      if (item.order_status == 4) return "已处理"
      if (item.pay_status == 0) return "待付款"
      if (item.pay_status == 1) {
        if (item.outstatus == 1) return "已出货"
        return item.unit == 3 ? "启动失败" : "出货失败"
      }
      return "未知状态"
    },

    // 订单类型
    orderType() {
      const item = this.orderData
      if (item.prom_type === 8) return "预付费订单"
      if (item.unit === 3) return "时长订单"
      return "普通订单"
    },
  },
  methods: {
    // 卡片点击
    handleClick() {
      this.$emit("click", this.orderData)
    },

    // 退款操作
    handleRefund() {
      this.$emit("refund", this.orderData)
    },

    // 启动操作
    handleStart() {
      this.$emit("start", this.orderData)
    },

    // 启动其他设备
    handleDeviceList() {
      this.$emit("device-list", this.orderData)
    },

    // 设备开关操作
    handleDeviceToggle() {
      this.$emit("device-toggle", this.orderData)
    },

    // 获取订单状态 - 与 OrderListCard 完全一致
    getOrderStatus() {
      const item = this.orderData
      if (item.is_refund == 1) return "已退款"
      if (item.order_status == 1) return "完成"
      if (item.order_status == 2) return "异常"
      if (item.order_status == 3) return "订单取消"
      if (item.order_status == 4) return "已处理"
      if (item.pay_status == 0) return "待付款"
      if (item.pay_status == 1) {
        if (item.outstatus == 1) return "已出货"
        return item.unit == 3 ? "启动失败" : "出货失败"
      }
      return "未知状态"
    },
  },
}
</script>

<style lang="scss" scoped>
/* 🔥 完全复制原来 OrderListCard 的样式 */
.card {
  padding: 20rpx 20rpx 0;
  background: #fff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);

  .content {
    position: relative;
  }

  .border-bottom {
    border-bottom: 2rpx solid #f0f0f0;
  }

  .border_top {
    border-top: 2rpx solid #f0f0f0;
  }

  .flexBottom {
    padding: 3rpx 0;
    display: flex;
    justify-content: center;
    padding-bottom: 10rpx;
  }

  .flexButton {
    border-top: 2rpx solid #f0f0f0;
    padding: 5rpx 0;
    display: flex;
    height: 70rpx;
    font-size: 20rpx;
    justify-content: space-between;

    .returns {
      text-align: center;
      width: 160rpx;
      font-weight: bold;
      padding: 8rpx 5rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(268deg, #1fbefd 0%, #46a1ff 99%);
      color: #fff;
    }
  }

  .content {
    box-sizing: content-box;
    display: flex;
    height: 150rpx;
    transition: all 0.4s;

    .img {
      width: 150rpx;
      height: 100%;
    }

    .info {
      flex: 1;
      margin-left: 20rpx;
      height: 100%;
      font-size: 24rpx;
      color: #666;

      .goods_name {
        .name {
          color: #333;
          font-size: 28rpx;
          font-weight: bold;
        }

        .order-time {
          font-size: 24rpx;
          color: #999;
        }

        .status {
          color: #0eade2;
          font-size: 22rpx;

          .status_icon {
            width: 30rpx;
            height: 30rpx;
            margin-right: 6rpx;
          }
        }
      }

      .num {
        color: #333;
      }

      .price {
        color: #ef0000;
      }

      .status {
        color: #0eade2;

        .arrow {
          transition: all 0.4s;
          margin-left: 6rpx;

          .arrow-icon {
            color: #0eade2;
            font-size: 24rpx;
            transition: all 0.3s;
          }
        }

        .rotate {
          transform: rotate(180deg);
        }

        .status_icon {
          width: 30rpx;
          height: 30rpx;
          margin-right: 6rpx;
        }
      }
    }
  }

  .prepay-info {
    text {
      font-size: 20rpx;
      margin-right: 16rpx;
    }
  }

  .prepay-pricing {
    text {
      font-size: 20rpx;
      margin-right: 16rpx;
    }
  }

  .myprepay-pricing {
    .prepay-row {
      display: flex;
      gap: 18rpx;

      text {
        flex-shrink: 0;
        font-size: 20rpx;
        white-space: nowrap;
      }
    }
  }

  .details {
    padding-bottom: 30rpx;

    > view {
      margin-top: 30rpx;
      display: flex;
      justify-content: space-between;
      font-size: 26rpx;

      > view {
        &:first-child {
          color: #666;
        }

        &:last-child {
          color: #333;
        }
      }

      .flex_white {
        white-space: nowrap;
        margin-right: 20rpx;
      }
    }

    > view > text {
      font-size: 22rpx;
    }
  }
}

.imgdis {
  position: absolute;
  right: -9px;
  bottom: 0px;
  width: 214rpx;
  height: 166rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

/* 🔥 通用布局类 */
.flexRowBetween {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flexColumnBetween {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.flexRowAllCenter {
  display: flex;
  justify-content: center;
  align-items: center;
}

.textMaxTwoLine {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
