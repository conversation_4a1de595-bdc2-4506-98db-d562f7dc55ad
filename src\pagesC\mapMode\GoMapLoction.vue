<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="map_layout">
      <map v-if="!isTrack" :scale="scale" :longitude="curLongitude" :latitude="curLatitude" :markers="markers" id="map"
        :show-location="true" @markertap="onMarkerTap" @tap.stop="onMapTap" style="width: 100%; height: 90vh;" />
      <map v-else id="mapTrack" :latitude="latitude" :longitude="longitude" :scale="scale" :markers="calloutMarkers"
        :polyline="polyline">
      </map>
    </view>
    <view class="map_card" @touchstart="onTouchStart" @touchend="onTouchEnd" :style="{ height: cardHeight + 'rpx' }">
      <view class="map-card_line"></view>
      <view class="list_title" v-if="cardHeight > 250 && nearbyMachineList.length > 0">
        设备信息
      </view>
      <view class="map_card_msg" v-if="cardHeight > 250 && nearbyMachineList.length > 0">
        <view class="map_card_title">{{ vPointName }}：: {{ nearbyMachineNewList.hotelName ?
      nearbyMachineNewList.hotelName : ''
          }}{{ nearbyMachineNewList.room_num ? '(' + nearbyMachineNewList.room_num + ')' : '' }}
        </view>
        <view class="map_card_title_span">拥有者： {{ nearbyMachineNewList.user.user_login
          }}{{
      nearbyMachineNewList.user.user_nickname
        ? ' ( ' + nearbyMachineNewList.user.user_nickname + ' )'
        : ''
    }}</view>
        <view class="map_card_title_span">设备状态：<span
            :style="{ color: nearbyMachineNewList.use_status == 1 ? '#0EADE2' : 'red' }">
            {{ nearbyMachineNewList.use_status == 1 ? '启用' : '禁用'
            }}{{
      nearbyMachineNewList.use_status == 1
        ? ''
        : `（原因：${nearbyMachineNewList.reason ? nearbyMachineNewList.reason : '无'}）`
    }}
          </span></view>
      </view>
      <view class="card_line" v-if="cardHeight > 250 && nearbyMachineList.length > 0"></view>
      <view class="mpa_card_item">
        <view class="map_card_left">
          <view class="map_card_title">设备编号:{{ nearbyMachineNewList.device_sn || '' }}
          </view>
          <view class="map_card_title_span">系统码：{{ nearbyMachineNewList.pseudocode || '' }}</view>
          <view class="map_card_title_span">设备类型：{{ nearbyMachineNewList.deviceTypeName || '' }}</view>
        </view>
        <view class="map_card_btn" @touchend="clickGoTrack" @touchstart="clickGoTrackStart">
          <image class="image" src="../static/img/轨迹.png" />
          <view>轨迹</view>
        </view>
        <view class="map_card_btn" @touchend="clickGoLocation" @touchstart="clickGoLocationStart">
          <image class="image" src="../static/img/location.jpeg" />
          <view>导航</view>
        </view>
      </view>
      <!-- <view class="card_line" v-if="cardHeight > 250 && nearbyMachineList.length > 0"></view> -->

      <!-- <view class="card_line" v-if="cardHeight > 250 && nearbyMachineList.length > 0"></view> -->
      <view class="list_title" v-if="cardHeight > 250 && nearbyMachineList.length > 0">
        {{ vPointName }}设备定位
      </view>
      <view class="card_line" v-if="cardHeight > 250 && nearbyMachineList.length > 0"></view>
      <scroll-view v-if="cardHeight > 250 && nearbyMachineList.length > 0" class="scroll_card" :scroll-y="true" style=""
        :style="{ height: '700rpx' }">

        <view v-for="(item, i) in nearbyMachineList" :key="i" class="scrool_list">
          <view class="mpa_card_item">
            <view class="map_card_left">
              <view class="map_card_title">设备编号:{{ item.device_sn || '' }}</view>
              <view class="map_card_title_span">系统码{{ item.pseudocode || '' }}</view>
              <view class="map_card_title_span">设备类型：{{ item.deviceTypeName || '' }}</view>
            </view>
            <view class="map_card_btn" @click="goLocation(item)">
              <image class="image" src="../static/img/location.jpeg" />
              <view>导航</view>
            </view>
          </view>
          <!-- <view class="card_line" v-if="cardHeight > 250"></view> -->
        </view>

      </scroll-view>
      <view :style="{height: '80rpx' }"></view>
    </view>


  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import { locationMixin } from "@/mixins/locationMixin";
import { globalKeys } from "@/global/globalKeys";
import BaseIcon from '@/components/base/BaseIcon.vue'
export default {
  components: {
    BaseNavbar,
    BaseIcon
  },
  mixins: [locationMixin, globalKeys],
  data() {
    return {
      title: "GPS定位",
      //map上下文
      mapCtx: {},
      //地图坐标信息
      markers: [],
      fromData: "", //来源渠道
      isFromDevice: false, //来自设备
      isFromScreen: false, //来自屏幕
      curClickMarkerMachine: {}, //点击的marker设备信息
      nearbyMachineList: [],
      nearbyMachineNewList: {},
      unBindItem: {},
      hotel_id: '',
      dianweiid: '',
      cardHeight: 250, // 初始高度  
      startY: 0, // 触摸开始时的Y坐标  
      endY: 0,   // 触摸结束时的Y坐标  
      clickStartY: 0,
      clickEndY: 0,
      // 地图上下文
      mapContext: null,
      // 设备列表
      isTrack: false,
      // 中心点经纬度
      latitude: 40.03245000000001,
      longitude: 116.272472,
      // 地图缩放等级
      scale: 18,
      // 路径数据
      polyline: [
        {
          points: [],
          color: '#3875FF',
          width: 8,
          dottedLine: false,
          arrowLine: false,
          borderWidth: 0,
          arrowLine: true
        }

      ],
      // 标记点数据
      calloutMarkers: [
        {
          id: 1,
          callout: {

            content: '终点',
            padding: 12,
            display: 'ALWAYS',
            fontSize: 14,
            textAlign: 'center',
            borderRadius: 4,
            borderWidth: 2,
            bgColor: '#ffffff',

            width: 40,

          },
          iconPath:
            "../static/img/终点.png",
          latitude: 40.03245000000001,
          longitude: 116.272472,
          width: 30,
          height: 40
        },
        {
          id: 2,
          callout: {
            content: '设备',
            padding: 12,
            display: 'ALWAYS',
            fontSize: 14,
            textAlign: 'center',
            borderRadius: 4,
            borderWidth: 2,
            bgColor: '#ffffff',


          },
          iconPath:
            "../static/img/tank1.png",
          latitude: 40.040129,
          longitude: 116.274968,
          width: 30,
          height: 40
        }
      ],

    };
  },
  methods: {
    //创建行动轨迹
    run() {
      this.mapCtx.moveAlong({
        markerId: 2,
        path: this.polyline[0].points,
        duration: 10000,
        autoRotate: true,
        success(res) {
          console.log(res)
        },
        fail(err) {
          console.log(err)
        }
      })
    },
    onTouchStart(event) {
      this.startY = event.touches[0].clientY;
      // console.log('触摸开始时的Y坐标', this.startY);

    },
    onTouchEnd(event) {
      this.endY = event.changedTouches[0].clientY;
      // console.log('触摸结束时的Y坐标', this.endY);

      // 计算滑动的方向  
      const deltaY = this.startY - this.endY;
      // 根据滑动的方向设置高度  
      if (deltaY > 20) {
        // 向上滑动，增加高度到700  
        // console.log('向上滑动更改高度',)
        this.cardHeight = 1200;
      } else if (deltaY < -100) {
        // 向下滑动或减少滑动（deltaY < 0），减少高度到200  
        // console.log('向下滑动或减少滑动',)
        this.cardHeight = 250;
      }
      // 重置startY和endY为0，为下一次触摸做准备  
      this.startY = 0;
      this.endY = 0;
    },
    clickGoLocationStart(event) {
      this.clickStartY = event.touches[0].clientY;
      // console.log('点击了导航1', this.clickStartY);

    },
    clickGoLocation(event) {
      this.clickEndY = event.changedTouches[0].clientY;

      // 计算滑动的方向  
      const deltaY = this.clickStartY - this.clickEndY;
      // console.log('点击了导航2', this.nearbyMachineNewList.m_lat,this.nearbyMachineNewList)
      if(!this.nearbyMachineNewList.m_lat&&!this.nearbyMachineNewList.m_lon){
        return this.isShowErr('该设备没有定位')

      }
      // 根据滑动的方向设置高度  
      // console.log('触摸导航', deltaY,deltaY==0);
      if (deltaY == 0) {
        // console.log('点击了导航2', this.nearbyMachineNewList);
        this.goLocation(this.nearbyMachineNewList)

      }
      // 重置clickStartY和clickEndY为0，为下一次触摸做准备  
      this.clickStartY = 0;
      this.clickEndY = 0


    },
    clickGoTrackStart(event) {
      this.clickStartY = event.touches[0].clientY;
    },
    clickGoTrack(event) {
      this.clickEndY = event.changedTouches[0].clientY;
      // console.log('触摸结束时的Y坐标', this.clickEndY);
      // 计算滑动的方向  
      const deltaY = this.clickStartY - this.clickEndY;
      // 根据滑动的方向设置高度  
      if (deltaY == 0) {
        this.title = '行动轨迹'
        this.isTrack = true
        this.getUserUMs();

      }
      // 重置clickStartY和clickEndY为0，为下一次触摸做准备  
      this.clickStartY = 0;
      this.clickEndY = 0

    },

    //处理经纬度小数点
    truncateDecimal(num, decimalPlaces) {
      let numStr = num.toString();
      let decimalPos = numStr.indexOf('.');
      if (decimalPos === -1) {
        return num;
      }
      let integerPart = numStr.substring(0, decimalPos);
      let decimalPart = numStr.substring(decimalPos + 1, decimalPos + 1 + decimalPlaces);
      while (decimalPart.length < decimalPlaces) {
        decimalPart += '0';
      }
      return parseFloat(integerPart + '.' + decimalPart);
    },
    //去导航
    goLocation(item) {

      if (this.isTrack) {
        const mapCtx = uni.createMapContext('mapTrack');
        console.log('点击导航mapTrack', mapCtx)
        mapCtx.openMapApp({
          // longitude:30.375596, // 经度
          latitude: item.m_lat * 1, // 纬度
          longitude: item.m_lon * 1, // 经度
          destination: item.device_sn,
        });
      } else {
        const mapCtx = uni.createMapContext('map');
        console.log('点击导航map', mapCtx)
        this.mapCtx.openMapApp({
          // latitude: 30.375596, // 纬度
          // longitude: 114.320833, // 经度
          latitude: item.m_lat * 1, // 纬度
          longitude: item.m_lon * 1, // 经度
          destination: item.device_sn,
        });

      }
      // console.log('点击导航mapTrack', item.m_lat * 1, item.m_lon * 1)


    },
    getUserUMs() {
      // this.isShowCard = false;
      //获取数据
      // console.log('获取数据', this.isTrack)
      if (this.isTrack) {
        let data = {
          device_sn: this.nearbyMachineNewList.device_sn
        };
        this.$u.api.getUmGpsList(data).then((res) => {
          // this.total = res.total;
          // console.log('获取设备列表', res.data)
          if(res.data.length<=0){
            return this.isShowErr('暂无行动轨迹')
          }
          if (res.data.length > 0) {
            this.polyline[0].points = res.data.map(item => ({
              latitude: item.lat * 1,
              longitude: item.lon * 1,
            }));


            this.latitude = this.calloutMarkers[1].latitude = this.polyline[0].points[0].latitude;
            this.longitude = this.calloutMarkers[1].longitude = this.polyline[0].points[0].longitude;
            this.calloutMarkers[0].latitude = this.polyline[0].points[res.data.length - 1].latitude;
            this.calloutMarkers[0].longitude = this.polyline[0].points[res.data.length - 1].longitude;
            // console.log('获取设备列表', this.polyline[0].points)

            this.mapCtx = uni.createMapContext("mapTrack"); // map为地图的id
            this.run();

          }
          // if (this.nearbyMachineList > 0) {
          //   this.initMarkers();
          // } else {
          //   this.isShowErr('暂无定位设备')
          // }
        });
      } else if (this.isFromDevice) {
        this.initMarkers();
      } else {
        let data = {
          hotel_id: this.hotel_id,
          dianweiid: this.dianweiid,
          is_have_gps: 1
        };
        this.$u.api.getHotelMachines(data).then((res) => {
          // this.total = res.total;
          // console.log('获取设备列表', res.data)
          this.nearbyMachineList = res.data;
          this.nearbyMachineNewList = res.data[0];
          if (this.nearbyMachineList > 0) {
            this.initMarkers();
          } else {
            this.isShowErr('暂无定位设备')
          }
        });
      }


    },
    //点击了marker
    onMarkerTap(e) {
      let machineInfoList = this.nearbyMachineList.filter(
        (item) => item.id == e.markerId
      );
      this.nearbyMachineNewList = machineInfoList?.[0];
    },
    //点击了地图
    onMapTap() {

    },
    //初始化标注点
    initMarkers() {
      //console.log('locationMixin initMarkers,目标经度=',this.targetLongitude,',纬度=',this.targetLatitude)
      let markerArr = this.nearbyMachineList.map((item) => ({
        id: item.id,
        latitude: item.m_lat,
        longitude: item.m_lon,
        iconPath:
          "../static/img/car.png",
        width: 40,
        height: 40,
      }));
      this.markers = markerArr;
    },
    // 初始化定位权限
    getLocPermission() {
      this.initLocPermission(() => {
        this.getCurrentLocation(() => {
          this.getUserUMs();

        });
      });
    },

  },
  onLoad(options) {
    if (options?.from == 'device') {
      let data = JSON.parse(decodeURIComponent(options.item))
      // console.log('传递的设备信息', data)

      this.device_sn = data.device_sn
      this.nearbyMachineList[0] = data
      this.nearbyMachineNewList = data
      this.isFromDevice = true

    } else if (options?.from == 'place') {
      this.hotel_id = options.hotel_id
      this.dianweiid = options.dianwei
      this.title = options.hotelName + this.title
      // console.log('传递的信息', options, this.hotel_id, this.dianweiid)

    } else if (options?.from == 'track') {
      this.isTrack = true


    }
    this.mapCtx = uni.createMapContext("map"); // map为地图的id
  },
  onShow(e) {
    /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.isDoRefresh == true) {
      // 是否刷新
      currPage.data.isDoRefresh = false;

      this.getUserUMs();
    }
    /*#endif */
    /*  #ifdef H5 */
    if (this.vCurrPage.isDoRefresh == true) {
      // 是否刷新
      this.vCurrPage.isDoRefresh = false;

      this.getUserUMs();
    }
    /*#endif */
  },
  mounted() {
    setTimeout(() => {
      this.getLocPermission();
    }, 1000);
  },
};
</script>


<style lang="scss" scoped>
.map_card {
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  overflow: hidden;
}

.map_layout {
  width: 100%;
  height: 90vh;
  background-color: #9a9a9a;

  map {
    width: 100%;
    height: 100%;
  }
}

.list_title {
  width: 715rpx;
  margin: 10rpx auto;
  font-size: 35rpx;
  font-weight: bold;
}

.tabs_box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  .tabs {
    width: 100%;
  }

  .device_num {
    white-space: nowrap;
    margin-right: 10rpx;
    color: $themeComColor;
  }
}

.map_card {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  // top: 85vh;
  z-index: 99999;
  background-color: #fff;

  .map-card_line {
    margin: auto;
    height: 10rpx;
    width: 100rpx;
    background-color: #9a9a9a
  }

}

.scroll_card {
  position: absolute;
  bottom: 20rpx;

  // .mpa_card_item {
  //   display: flex;
  //   flex-direction: column;
  //   align-items: center;
  // }
  .scrool_list {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .map_card_title {
    width: 500rpx;
    margin-bottom: 10rpx;
    font-size: 35rpx;
    font-weight: bold;



  }

  .map-card_line {
    margin: auto;
    height: 10rpx;
    width: 100rpx;
    background-color: #9a9a9a
  }
}




.mpa_card_item {
  width: 730rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  // border: 1rpx solid #333;
  height: 170rpx;
  border-radius: 15rpx;
  padding: 10rpx;
  margin: 10rpx;
  align-items: center;


  .map_card_title {
    width: 480rpx;
    margin-bottom: 10rpx;
    font-size: 35rpx;
    font-weight: bold;



  }

  .map_card_title_span {
    font-size: 25rpx;
    // color: #9a9a9a;
  }
}

.card_line {
  height: 2rpx;
  width: 700rpx;
  margin: auto;
  background-color: #9a9a9a;
  margin-bottom: 10rpx;
}

.map_card_msg {

  padding: 0 20rpx 20rpx 20rpx;

}

.map_card_btn {
  padding: 20rpx;
  // margin-left: 10rpx;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;


  .image {
    width: 50rpx;
    height: 50rpx;
    border-radius: 50%;
    overflow: hidden;
  }
}
</style>