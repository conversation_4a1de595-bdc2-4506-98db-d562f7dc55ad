<template>
    <view class="bindHotelCard">
        <view class="locationRoom">
            <view class="location-left">编号</view>
            <view class="rideo-input">
                <input type="text" v-model="number" />
            </view>
            <view class="right-txt">
                号
            </view>

        </view>
        <view class="color">
            <view class="location-left">颜色</view>
            <view class="ridio">
                <BaseRadio :radioIndex.sync="radioIndex" width="33" :list="reasonList" />
            </view>
        </view>

        <view class="locationRoom">
            <input type="text" placeholder="请输入其他信息" v-model="room_num" />
        </view>
    </view>

</template>

<script>

import BaseRadio from "@/components/base/BaseRadio.vue";


export default {
    components: { BaseRadio },
    name: "LoctionType",
    props: {
        str: {
            type: String,
            default: ''

        },
        numbers: {
            type: [String, Number],
            default: 1,
        },
        isCar: {
            type: Boolean,
            default: false
        },
        isPPJ: {
            type: Boolean,
            default: false
        }

    },


    data() {
        return {
            reasonList: [],
            ppjList: [{
                title: "蓝色",
                name: "0",
                disabled: false,

            },
            {

                title: "绿色",
                name: "1",
                disabled: false,

            },
            {

                title: "紫色",
                name: "2",
                disabled: false,

            },
            {

                title: "红色",
                name: "3",
                disabled: false,

            },
            {

                title: "白色",
                name: "4",
                disabled: false,

            },
            {

                title: "粉色",
                name: "5",
                disabled: false,

            },
            {

                title: "黄色",
                name: "6",
                disabled: false,

            },


            ],
            radioIndex: 0,
            carList: [
                {
                    title: "迷彩橙色",
                    name: "0",
                    disabled: false,

                },
                {

                    title: "迷彩蓝色",
                    name: "1",
                    disabled: false,

                },
                {

                    title: "迷彩绿色",
                    name: "2",
                    disabled: false,

                },
                {

                    title: "迷彩红色",
                    name: "3",
                    disabled: false,

                },
                {
                    title: "橙色",
                    name: "4",
                    disabled: false,

                },
                {

                    title: "蓝色",
                    name: "5",
                    disabled: false,

                },
                {

                    title: "绿色",
                    name: "6",
                    disabled: false,

                },
                {

                    title: "红色",
                    name: "7",
                    disabled: false,

                },

            ],
            kqdList: [{
                title: "绿色",
                name: "0",
                disabled: false,

            },

            ],
            number: 1,
            room_num: ''
        }
    },
    //方法
    computed: {
        location_str() {
            if (this.reasonList.length < 1) {
                return ""
            }
            // console.log('this.reasonList',this.reasonList)
            // console.log('this.radioIndex',this.reasonList[this.radioIndex].title)

            if (this.room_num) {
                return this.number + '号-' + this.reasonList[this.radioIndex].title + '-' + this.room_num.replace(/-/g, '')
            } else {
                return this.number + '号-' + this.reasonList[this.radioIndex].title
            }
        },

    },
    watch: {
        location_str: {
            handler(newVal) {
                this.$emit('locationStr', newVal, this.number);
            },
            immediate: true
        },

        numbers: {
            handler(newVal) {
                this.number = newVal

            },
            immediate: true
        },
        str: {
            handler(newVal) {
                console.log('传递的值', newVal)
                this.updata(newVal)

            },
            immediate: true
        }

    },
    //组件挂载
    mounted() {
        // const str=this.location_str
        // console.log('str',str)
        // setTimeout(()=>{
        //     this.$emit('locationStr', str, this.floor, this.number);
        // })
        setTimeout(() => {
            if (this.isCar) {
                this.reasonList = this.carList
            } else if (this.isPPJ) {
                this.reasonList = this.ppjList
            } else {
                this.reasonList = this.kqdList

            }
            console.log('str', this.str, this.isCar, this.isPPJ)
        }, 200)





    },
    methods: {
        updata(str) {

            let arr = str.split('-')
            if (arr.length > 1) {
                this.number = arr[0].split('号')[0] || 1
                this.reasonList.forEach((item, index) => {
                    if (item.title == arr[1]) {
                        this.radioIndex = index || 0
                    }
                })
                this.room_num = arr[3] || ''
            } else {

                if (this.numbers) {
                    this.number = this.numbers
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.ridio {
    width: 550rpx;
}

.location-left {
    margin-right: 25rpx;
}

.color {
    padding: 25rpx 0;
    display: flex;
    margin-left: 35rpx;
    min-height: 100rpx;
    border-bottom: 1rpx solid #e5e5e5;
}

.hotelName {
    // height: 100rpx;
    padding: 25rpx 0;
    display: flex;
    flex-direction: row;
    align-content: center;
    align-items: center;
    margin-left: 35rpx;
    border-bottom: 1rpx solid #e5e5e5;
}

.rideo-chunk {
    width: 100rpx;
    text-align: center;
    border: 1rpx solid #cbcbcb;
    padding: 10rpx;
    margin-right: 10rpx;
    height: 60rpx;

}

.rideo-input {
    height: 60rpx;
    width: 100rpx;
    border: 1rpx solid #cbcbcb;
    padding: 10rpx;
    text-align: center;
    margin-right: 20rpx;
    display: flex;
    align-items: center;

}

.bindHotelCard {
    // margin-top: 20rpx;
    // height: 300rpx;
    width: 100%;
    background: white;
    font-size: 26rpx;
    color: $textBlack;
}

.locationRoom {
    height: 100rpx;
    // height: 150rpx;
    // padding: 25rpx 0;
    display: flex;
    flex-direction: row;
    align-content: center;
    align-items: center;
    margin-left: 35rpx;
    border-bottom: 1rpx solid #e5e5e5;
    // border-bottom: 0;
}
</style>