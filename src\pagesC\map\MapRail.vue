<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="map-box">
      <view class="search-box">
        <!-- <BaseSearch placeholder="请输入地址" @onClickIcon="onClickIcon" @search="searchAddress" :isShowSerch="false" /> -->
        <!-- <input type="text" v-model="searchQuery" placeholder="请输入地址" />
        <button @tap="searchAddress">搜索</button> -->
      </view>
      <map id="map" :longitude="longitude" :latitude="latitude" scale="18" @tap="bindtapMap" :markers="otherMarkers"
        :polyline="polyline" :polygons="polygons" show-location style="width: 100%; height: 55vh;">
      </map>
      <view class="but_box">
        <button @tap="addPolygons" :class="isAddStart ? 'add' : ''">添加</button>
        <button @tap="removePolygons" v-if="!isP">后退</button>
        <button @tap="AllPolygons">删除</button>
        <button @tap="successPolygons">完成</button>
      </view>
      <view class="remark">
        <view class="remark_title">
          注意事项：
        </view>
        <view class="remark_text">
          1.点击添加按钮,按钮变蓝色背景可以点击地图位置添加<br />
          2.添加电子围栏需要先删除已有的P点和电子围栏<br />
          3.删除电子围栏会删除已有的P点<br />
          4.电子围栏和P点都只能添加一个<br />
          5.点击完成按钮完成添加
        </view>

      </view>


    </view>
  </view>
</template>

<script>
import BaseNavbar from '@/components/base/BaseNavbar.vue';
import { locationMixin } from "@/mixins/locationMixin";
import BaseSearch from "@/components/base/BaseSearch.vue";
export default {
  components: {
    BaseNavbar,
    BaseSearch
  },
  data() {
    return {
      title: "电子围栏",
      polygons: [],
      polygonLabels: [], // 用于存储多边形中心的文字标注
      currentPolygonIndex: -1, // 当前正在创建的多边形索引
      isCreatingPolygon: false, // 用于判断是否在创建多边形
      isCreating: false,//是否可以创建多边形
      nearbyMachineList: [],
      longitude: 0,
      latitude: 0,
      searchQuery: "", // 搜索框的值
      polygonMarkers: [], // 用于存储多边形的标记点
      markers: [],
      hotel_id: '',
      isP: false,
      isAddP: false,
      isAddMarker: false,
      isAddStart: false,


    }
  },
  methods: {
    // 点是否在多边形内
    isPointInPolygon(point, polygonPoints) {
      // 将点的经纬度转换为平面坐标（这里简单假设为直接使用经纬度，但实际应用中可能需要投影）  
      const testX = point.longitude * 1;
      const testY = point.latitude * 1;

      // 变量初始化  
      let isInside = false;
      let j = polygonPoints.length - 1; // 多边形的最后一个点的索引  

      // 遍历多边形的每条边  
      for (let i = 0; i < polygonPoints.length; i++) {
        // 获取当前点和下一个点的坐标  
        const xi = polygonPoints[i].longitude * 1;
        const yi = polygonPoints[i].latitude * 1;
        const xj = polygonPoints[j].longitude * 1;
        const yj = polygonPoints[j].latitude * 1;

        // 检查测试点是否在当前边的下方  
        if (((yi > testY) !== (yj > testY)) &&
          (testX < (xj - xi) * (testY - yi) / (yj - yi) + xi)) {
          isInside = !isInside;
        }

        // 更新下一个点的索引  
        j = i;
      }

      // 返回结果  
      return isInside;
    },
    //计算中心点
    calculateCenterPoint(coordinates) {
      if (coordinates.length === 0) {
        return null; // 或者你可以选择抛出一个错误  
      }

      let sumLat = 0;
      let sumLon = 0;

      // 遍历坐标数组，累加纬度和经度  
      coordinates.forEach(coord => {
        sumLat += coord.latitude * 1;
        sumLon += coord.longitude * 1;
      });

      // 计算平均值  
      const avgLat = sumLat / coordinates.length;
      const avgLon = sumLon / coordinates.length;

      // 返回中心点坐标  
      return { latitude: avgLat, longitude: avgLon };
    },

    searchAddress(val) {
      const key = '754BZ-2WFL3-PID3X-RHCYD-DDPCF-PCBQY'; // 替换为你的腾讯地图KEY
      const address = encodeURIComponent(val); // 对地址进行URL编码
      const province = encodeURIComponent(val); // 对省份进行URL编码
      const url = 'https://apis.map.qq.com/ws/place/v1/search';

      console.log("查询地址: ", address, val); // 调试信息
      console.log("请求URL: ", url); // 调试信息
      let that = this
      uni.request({
        url: url,
        method: 'GET', // 确保使用GET方法
        header: {
          'Content-Type': 'application/json' // 确保使用正确的Content-Type
        },
        data: {
          keyword: address, // 传递编码后的地址
          key: key,
          boundary: `region(${province}, 1)` // 传递编码后的省份
        },
        success: function (res) {
          console.log("返回结果: ", res); // 调试信息
          if (res.data.status === 0) {
            var pois = res.data.data;
            that.longitude = pois[0].location.lng
            that.latitude = pois[0].location.lat
            console.log(that.longitude)
            // that.initMarkers();
            // for (var i = 0; i < pois.length; i++) {
            //   console.log(pois[i].title);
            //   console.log(pois[i].location);

            // }
          } else {
            uni.showToast({
              title: '地址解析失败',
              icon: 'none'
            });
          }
        },
        fail: function (err) {
          console.error("请求错误: ", err);
          uni.showToast({
            title: '网络错误',
            icon: 'none'
          });
        }
      });
    },
    addPolygons() {
      this.isAddStart = false
      if (this.isCreatingPolygon) {
        console.log('上一个围栏还没结束');
        return;
      }
      if (this.isP) {
        // if (!this.isAddMarker) {
        //   return uni.showToast({
        //     title: '请先设置电子围栏',
        //     icon: 'none'
        //   });
        // }

        if (this.isAddP) {
          return uni.showToast({
            title: '请先删除之前的P点',
            icon: 'none'
          });
        }

      } else {
        if (this.isAddP) {
          return uni.showToast({
            title: '请先删除之前的P点',
            icon: 'none'
          });
        }
        if (this.isAddMarker) {
          return uni.showToast({
            title: '请先删除之前的电子围栏',
            icon: 'none'
          });
        }
      }
      this.isAddStart = true
      this.isCreating = true
      this.currentPolygonIndex = this.polygons.length;
      this.polygonMarkers = []; // 清空当前多边形的标记点
    },
    //添加可以画图
    creatPolygons() {
      //创建多边形围栏
      // 创建多边形围栏
      console.log('markers长度', this.polygonMarkers);

      if (this.polygonMarkers.length < 3) {
        this.isCreatingPolygon = true;
        console.log('索引', this.currentPolygonIndex)
        if (this.polygons[this.currentPolygonIndex]) {
          this.polygons.splice(this.currentPolygonIndex, 1);

        }
        return;
      }
      console.log('是不是这个map')
      let newArray = this.polygonMarkers.map(marker => ({
        latitude: marker.latitude,
        longitude: marker.longitude
      }));

      let params = {
        id: this.currentPolygonIndex,
        fillColor: "#1791fc66",
        strokeColor: "#FFF",
        strokeWidth: 2,
        zIndex: 3
      };

      let newPolygon = Object.assign({ points: newArray }, params);
      this.polygons[this.currentPolygonIndex] = newPolygon;
      this.isCreatingPolygon = false;
      console.log('polygons', this.polygons);

    },


    bindtapMap(e) {
      console.log('点击', this.isCreating)
      if (!this.isCreating) {
        return uni.showToast({
          title: '请先点击添加按钮',
          icon: 'none'
        });
      }


      let tapPoint = e.detail;
      let markers = this.polygonMarkers;

      let newContent = markers.length;
      let markerItem = {
        id: newContent,
        latitude: tapPoint.latitude.toFixed(6) * 1,
        longitude: tapPoint.longitude.toFixed(6) * 1,
        iconPath: '',
        width: '24px',
        height: '34px',
        rotate: 0,
        alpha: 1,
        zIndex: 3,
      };
      if (!this.isP) {
        markers.push(markerItem);
        this.polygonMarkers = markers;
        console.log('markers', this.polygonMarkers);
        this.creatPolygons();
      } else {
        let isp = true
        if (this.isAddMarker) {
          isp = this.isPointInPolygon(markerItem, this.polygons[0].points)
        }

        if (isp) {
          markerItem['id'] = 10001
          markerItem['iconPath'] = '../static/img/p.png'
          //添加p点图标

          // 检查是否已存在具有特定id的对象  
          const index = this.markers.findIndex(marker => marker.id === markerItem.id);

          // 如果不存在（index 为 -1），则添加新对象  
          if (index === -1) {
            this.markers.push(markerItem);
          } else {
            // 如果存在，则替换它  
            this.markers.splice(index, 1, markerItem);
          }
          console.log('markers', this.markers)
        } else {
          uni.showToast({
            title: '不在围栏内',
            icon: 'none'
          });

        }
      }


    },
    removePolygons() {
      // 删除所有围栏和标记
      if (this.polygonMarkers.length == 0) {
        return
      }
      this.polygonMarkers.pop();

      // console.log()

      console.log('删除', this.polygons[this.currentPolygonIndex])

      console.log('删除后的polygons', this.polygons);

      if (this.polygonMarkers.length < 3) {
        this.polygons.splice(this.currentPolygonIndex, 1)
      } else {
        if (this.polygons[this.currentPolygonIndex]) {
          this.polygons[this.currentPolygonIndex].points.pop();
        }
      }
      // this.isCreatingPolygon = false
      // this.isCreating = false;
    },
    async AllPolygons() {
      // 删除所有围栏和标记
      // this.polygonMarkers = [];
      // this.polygons = [];
      try {
        this.isAddStart = false
        this.isCreating = false;
        let data = {
          hotel_id: this.hotel_id
        }
        const index = this.markers.findIndex(marker => marker.id === 10001);
        // console.log('判断条件', this.isAddP, index == -1)
        if (this.isP) {
          if (index == -1) {
            return uni.showToast({
              title: '请先添加P点',
              icon: 'none'
            });
          } else {
            //如果没有p点信息也但地图上有p点清楚地图p点
            if (this.isAddP) {
              await this.$u.api.delPGeofence(data)
              this.isAddP = false
            }
            this.markers.splice(index, 1)
            // return 
          }

        } else {

          if (this.polygonMarkers.length > 0) {
            this.polygonMarkers = []
            this.polygons = []
            return
          }
          if (!this.isAddMarker && !this.isAddP) {
            return uni.showToast({
              title: '请先添加电子围栏',
              icon: 'none'
            });
          }
          if (this.isAddP && index !== -1) {
            await this.$u.api.delPGeofence(data)
            this.markers.splice(index, 1);
            this.isAddP = false
          }
          if (this.isAddMarker) {
            await this.$u.api.delGeofence(data)
            this.isAddMarker = false
          }
          this.polygons = []


        }
      }
      catch (err) {
        return uni.showToast({
          title: err,
          icon: 'none'
        });
      }



    },
    successPolygons() {
      this.isAddStart = false
      // 完成当前多边形的创建
      this.isCreating = false;

      if (!this.isP) {
        this.polygonMarkers = [];
        let point = []
        if (this.polygons && this.polygons[this.currentPolygonIndex] && this.polygons[this.currentPolygonIndex].points.length > 2) {
          point = this.polygons[this.currentPolygonIndex].points
        } else {
          return uni.showToast({
            title: '请先添加电子围栏',
            icon: 'none'
          });

        }
        let data = {
          hotel_id: this.hotel_id,
          name: '',
          remark: '',
          points: point
        }
        this.$u.api.addGeofence(data).then(res => {
          console.log('请求成功', res)
          this.getGeofence(this.hotel_id)
        })
        this.currentPolygonIndex = -1;

      } else {
        const index = this.markers.findIndex(marker => marker.id === 10001);
        if (index === -1) {
          return uni.showToast({
            title: '请先添加p点',
            icon: 'none'
          });
        }
        let point = [{
          latitude: this.markers[index].latitude,
          longitude: this.markers[index].longitude

        }]
        let data = {
          hotel_id: this.hotel_id,
          name: '',
          remark: '',
          points: point
        }
        this.$u.api.addPGeofence(data).then(res => {
          console.log('请求成功', res)
          this.getfence(this.hotel_id)
        })
      }


    },
    findMarkerIndex(lat, lng) {
      const tolerance = 0.00005; // 公差范围，用于判断点击点是否接近现有标记
      return this.polygonMarkers.findIndex(marker => {
        return Math.abs(marker.latitude - lat) < tolerance && Math.abs(marker.longitude - lng) < tolerance;
      });
    },

    //初始化地图
    initMarkers() {
      console.log('定位', this.latitude, this.longitude)
      // let markerArr = {
      //   id: 0,
      //   latitude: this.latitude,
      //   longitude: this.longitude,
      //   iconPath: this.icMarkerDefault,
      //   width: 40,
      //   height: 50,
      // }
      // console.log("🚀 ~ markerArr", markerArr)
      // this.markers.push(markerArr)
    },
    //获取位置信息
    getLocPermission() {
      // #ifdef MP-WEIXIN || MP-TOUTIAO
      this.initLocPermission(() => {
        this.getCurrentLocation(() => {
          this.longitude = this.curLongitude
          this.latitude = this.curLatitude
          this.initMarkers();

        });
      });
      //#endif
      // #ifdef MP-ALIPAY
      uni.showModal({
        title: "温馨提示：",
        content: "需要授权您的位置信息,为您展示附近机器,是否授权?",
        success: ({ confirm }) => {
          if (confirm) {
            this.getCurrentLocation(() => {
              this.initMarkers();
            });
          }
        },
      });

      //#endif
      // #ifdef H5
      uni.showModal({
        title: "温馨提示：",
        content: "需要授权您的位置信息,为您展示附近机器,是否授权?",
        success: ({ confirm }) => {
          if (confirm) {
            this.getCurrentLocation(() => {
              this.initMarkers();
            });
          }
        },
      });
      //#endif
    },
    //获取定位
    async getLocation(hotel_id) {
      try {
        let data = {
          hotel_id: hotel_id
        }
        const res = await this.$u.api.getMyHotels(data);
        this.nearbyMachineList[0] = res.data.find(item => item.id == hotel_id)


        this.longitude = this.nearbyMachineList[0].lon
        this.latitude = this.nearbyMachineList[0].lat


        console.log('数据筛选', this.nearbyMachineList, this.longitude, this.latitude)

        if (this.longitude && this.latitude) {
          console.log('经纬度', this.longitude, this.latitude)
        } else {
          this.getLocPermission()
        }
      }
      catch (error) {
        console.log(error);
      }
    },
    //获取电子围栏
    async getGeofence(hotel_id) {
      try {
        let data = {
          hotel_id: hotel_id
        }
        const res = await this.$u.api.getHotelGeofence(data);
        console.log('points', res)
        if (res.data.length > 0) {
          this.isAddMarker = true
        } else {
          this.isAddMarker = false

        }
        let arr = []
        //如果有进制添加围栏





        res.data.map(item => {
          let data = {
            latitude: item.latitude,
            longitude: item.longitude
          }
          arr.push(data)
          // this.polygons[0].points.push(data)
          // console.log('this.polygons',this.polygons)

        })

        let params = {
          id: this.currentPolygonIndex,
          fillColor: "#1791fc66",
          strokeColor: "#FFF",
          strokeWidth: 2,
          zIndex: 3
        };

        let newPolygon = Object.assign({ points: arr }, params);
        console.log('arr', arr)
        // 示例  
        const coordinates = arr;
        const centerPoint = this.calculateCenterPoint(coordinates);
        console.log('centerPoint', centerPoint)
        this.polygons[0] = newPolygon;
        // let markerArr = {
        //   id: 1,
        //   latitude: centerPoint.latitude,
        //   longitude: centerPoint.longitude,
        //   iconPath: this.icMarkerDefault,
        //   width: 40,
        //   height: 50,
        // }
        // console.log("🚀 ~ markerArr", markerArr)
        // this.markers.push(markerArr);
        // console.log('this.markers', this.markers)
        // // this.polygons[0].push(params)
        // console.log('数据筛选', res)

      }
      catch (error) {
        console.log('err', error);
      }
    },
    //获取p点
    async getfence(hotel_id) {
      try {
        let data = {
          hotel_id: hotel_id
        }
        const res = await this.$u.api.getHotelPGeofence(data);
        console.log('points', res)
        if (res.data.length > 0) {
          this.isAddP = true

        } else {
          this.isAddP = false

        }
        let markerItem = {
          id: 10001,
          latitude: res.data[0].latitude * 1,
          longitude: res.data[0].longitude * 1,
          width: '24px',
          height: '34px',
          iconPath: '../static/img/p.png',
          rotate: 0,
          alpha: 1,
          zIndex: 3,
        };

        // 检查是否已存在具有特定id的对象  
        const index = this.markers.findIndex(marker => marker.id === markerItem.id);

        // 如果不存在（index 为 -1），则添加新对象  
        if (index === -1) {
          this.markers.push(markerItem);
        } else {
          // 如果存在，则替换它  
          this.markers.splice(index, 1, markerItem);
        }
      }
      catch (error) {
        console.log(error);

      }



    },
  },
  mixins: [locationMixin],
  computed: {
    otherMarkers() {
      // 确保markers和polygonMarkers都是数组，或者如果不是数组/未定义，则默认为空数组  
      const safeMarkers = Array.isArray(this.markers) ? this.markers : [];
      const safePolygonMarkers = Array.isArray(this.polygonMarkers) ? this.polygonMarkers : [];

      // 现在可以安全地使用扩展运算符  
      console.log('otherMarkers', [...safeMarkers, ...safePolygonMarkers])
      return [...safeMarkers, ...safePolygonMarkers];
    }
  },
  onLoad(options) {
    this.mapCtx = uni.createMapContext("map"); // map为地图的id
    // this.getLocPermission()
    if (options.hotel_id) {
      this.hotel_id = options.hotel_id
      this.getLocation(options.hotel_id)
      this.getGeofence(options.hotel_id)
      this.getfence(options.hotel_id)
      if (options.from == 'p') {
        this.isP = true
        this.title = '添加P点'
      }


    }


  },
  // mixins: [myPull()],
}
</script>

<style lang="scss" scoped>
.but_box {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  margin-top: 20rpx;
  padding: 20rpx;

}

.remark {
  padding: 10rpx 30rpx;

  &_title {
    font-weight: bold;
  }

  &_text {
    font-size: 28rpx;
    color: #9a9a9a;
  }
}

.add {
  background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
  color: white;
  border: 0;
}
</style>
