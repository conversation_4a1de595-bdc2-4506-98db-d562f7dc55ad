<template>
  <view class="list">
    <slot></slot>
    <ComLoadMore
      v-if="loadingType != 3 && loadingType != -1"
      :loading-type="loadingType"
    />
    <BaseEmpty v-if="loadingType == 3" />
    <SafeBlock :height="bottom" />
  </view>
</template>

<script>
import BaseEmpty from "../base/BaseEmpty.vue"
import SafeBlock from "../common/SafeBlock.vue"
import ComLoadMore from "./ComLoadMore.vue"
export default {
  components: { ComLoadMore, BaseEmpty, SafeBlock },
  name: "ComList",
  props: {
    loadingType: {
      //上拉的状态：0-loading前；1-loading中；2-没有更多了
      type: [Number, String],
      default: 0,
    },
    bottom: {
      type: [Number, String],
      default: 20,
    },
  },
}
</script>

<style lang="scss" scoped>
.list {
  padding: 30rpx;
  box-sizing: border-box;
}
</style>
