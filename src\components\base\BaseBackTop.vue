<template>
  <view v-if="listShow" class="fiex">
    <image @click="onScroll" class="image" src="@/static/img/icon/top.png" />
  </view>
  <view v-else>
    <u-back-top
      :scroll-top="scrollTop"
      :top="top"
      :bottom="250"
      :icon-style="iconStyle"
      mode="circle"
      :custom-style="customStyle"
    >
      <image class="image" src="@/static/img/icon/top.png" />
    </u-back-top>
  </view>
</template>

<script>
export default {
  name: "BaseBackTop",
  props: {
    top: {
      type: [Number, String], //滚动距离
      default: "200",
    },
    tips: {
      type: String, //文字
      default: "顶部",
    },
    icon: {
      type: String, //图标
      default: "arrow-up",
    },
    scrollTop: {
      type: [Number, String], //按钮圆角 circle square
      default: 0,
    },
    listShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mode: "square",
      isLoading: false,
      iconStyle: {
        fontSize: "32rpx",
        color: "#2979ff",
      },
      customStyle: {},
    }
  },
  methods: {
    onPageScroll(e) {
      this.$emit("onPageScroll", e)
    },
    onScroll() {
      this.isLoading = true
      this.$emit("onScroll", () => {
        this.isLoading = false
        uni.hideLoading()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.fiex {
  width: 80rpx;
  height: 80rpx;
  position: fixed;
  bottom: 250rpx;
  right: 50rpx;
  transition: transform 0.3s ease;
  &:active {
    transform: scale(0.9);
  }
}

.loading-indicator {
  position: absolute;
  bottom: -40rpx;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 24rpx;
  color: #999;
}

.image {
  width: 100%;
  height: 100%;
}
</style>
