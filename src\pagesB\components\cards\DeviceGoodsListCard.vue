<template>
  <view class="goodsCard">
    <view class="content">
      <view class="cargo-way flexColumnAllCenter">
        <view class="way">{{
          String(info.num).length == 1 ? "0" + info.num : info.num
        }}</view>
        <view>{{ info.unit == 3 ? vCargoLanes : vCargoLanes }}</view>
      </view>
      <image class="goods-img" :src="info.original_img || vDefaultIcon" />
      <view class="goods-content flexColumnBetween">
        <view class="name">{{ info.goods_name || "点击并设置" + vCargoLanes }}</view>
        <block v-if="info.goods_name">
          <view>
            <text>{{ info.shop_type == 2 ? '起始' : '' }}售价：</text>
            <text class="text-red">{{ info.good_price || 0 }}元</text>
            <block v-if="info.vip_price * 1">
              <text style="margin-left: 10rpx;">会员价：</text>
              <text class="text-red">{{ info.vip_price || 0 }}元</text>
            </block>
            <block v-if="info.shop_type == 2">
              <view>
                <text>预付金：</text>
                <text class="text-red">{{ info.pre_pay_money || 0 }}元</text>
                <text style="margin-left: 10rpx;">超时：</text>
                <text class="text-red">{{ info.pre_per_minute || 0 }}元/分</text>
              </view>

            </block>
          </view>
          <view class="flex">
            <view>
              <text>是否热卖：</text>
              <text :class="info.is_hot ? 'text-red' : ''">{{ info.is_hot ? '是' : '否' }}</text>
            </view>
            <view>
              <text>是否推荐：</text>
              <text :class="info[midForm] ? 'text-red' : ''">{{ info[midForm] ? '是' : '否' }}</text>
            </view>
          </view>

          <block v-if="info.unit != 3">
            <view class="flexRowBetween">
              <!-- <view>
              <text>容量：</text>
              <text class="text-black">300</text>
            </view> -->
              <view>
                <text>库存：</text>
                <text class="text-black">{{ info.stock || 0 }}</text>
              </view>
              <view>
                <text>最大库存：</text>
                <text class="text-black">{{ info.default_stock || 0 }}</text>
              </view>
            </view>
          </block>
          <block v-if="info.unit === 3">
            <view class="flexRowBetween">
              <view>
                <text>{{ info.shop_type == 2 ? '起始' : '' }}{{ info.goods_id == 5 ? '驾驶' : '骑行' }}时长(分钟)：</text>
                <text class="text-black">{{ info.game_time || 0 }}</text>
              </view>
            </view>
          </block>
          <block v-if="info.unit === 2">
            <view class="flexRowBetween">
              <view>
                <text>免费长度(CM)：</text>
                <text class="text-black">{{ info.free_length || 0 }}</text>
              </view>
              <view>
                <text>支付长度(CM)：</text>
                <text class="text-black">{{ info.pay_length || 0 }}</text>
              </view>
            </view>
          </block>
        </block>

      </view>
    </view>
    <view class="btn flexRowBetween" v-if="from == 'replenish'">
      <block v-if="info.unit != 3">
        <BaseButton shape="circle" type="default" width="280" @onClick="supply">库存补充</BaseButton>
        <BaseButton shape="circle" type="default" width="280" @onClick="open">出 货</BaseButton>
      </block>
      <block v-if="info.unit == 3">
        <!-- <BaseButton shape="circle" type="default" width="280" @onClick="start"
          >启 动</BaseButton
        >
        <BaseButton shape="circle" type="default" width="280" @onClick="stop"
          >停 止</BaseButton
        > -->
      </block>
    </view>
    <view v-else class="btn flexRowBetween">
      <block v-if="vUserInfo.role_id != 6">
        <BaseButton type="default" width="310" @onClick="del">删 除</BaseButton>
        <BaseButton type="primary" width="310" @onClick="edit">编 辑</BaseButton>
      </block>
    </view>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
export default {
  components: { BaseButton },
  props: {
    info: {
      type: Object,
      default: function () {
        return {};
      }
    },
    index: { type: [Number, String], default: "01" },
    from: { type: String, default: "" },
    midForm: {
      type: String,
      default: 'is_recommend'
    }
  },
  methods: {
    del() {
      this.$emit("del", this.info);
    },
    edit() {
      this.$emit("edit", this.info);
    },
    supply() {
      this.$emit("supply");
    },
    open() {
      this.$emit("open");
    },
    start() {
      this.$emit("start");
    },
    stop() {
      this.$emit("stop");
    },
  },
};
</script>

<style lang="scss" scoped>
.goodsCard {
  background-color: $uni-bg-color;
  border-radius: $cardRadius;
  margin-bottom: 30rpx;
}
.flex{
  display: flex;
  justify-content: space-between;
}
.content {
  display: flex;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid $dividerColor;

  .cargo-way {
    color: $textBlack;
    font-size: $font-size-xlarge;
    font-weight: 700;
  }

  .goods-img {
    width: 150rpx;
    height: 150rpx;
    border-radius: $imgRadius;
    // background-color: $textGray;
    margin-left: 28rpx;
  }

  .goods-content {
    flex: 1;
    margin-left: 19rpx;
    font-size: $font-size-small;
    color: $textDarkGray;

    .name {
      color: $textBlack;
      font-size: $font-size-middle;
    }

    .text-black {
      color: $textBlack;
    }

    .text-red {
      color: $mainRed;
    }
  }
}

.btn {
  box-sizing: border-box;
  padding: 20rpx;
}
</style>