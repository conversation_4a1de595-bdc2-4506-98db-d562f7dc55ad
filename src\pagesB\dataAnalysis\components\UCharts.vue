<template>
  <view class="card-charts">
    <view class="total">{{ title }}：{{ totalNum }}</view>
    <!-- #ifndef H5 -->
    <canvas :canvas-id="id" :id="id" class="charts" @touchend="touchend" @touchstart="touchstart" :canvas2d="true"
      type="2d" @touchmove="touchmove" />
    <!-- #endif -->
    <!-- #ifdef H5 -->
    <canvas :canvas-id="id" :id="id" class="charts" />
    <!-- #endif -->
  </view>
</template>
<script>
//文档
// https://www.ucharts.cn/v2/#/
/*  #ifndef H5 */
import uCharts from "@qiun/ucharts";
/*#endif */
/*  #ifdef H5 */
import * as echarts from 'echarts';
/*#endif */

var uChartsInstance = {};
export default {
  name: "UCharts",
  props: {
    lineData: {
      type: Array, require: true, default: function () {
        return []
      }
    },
    chartsName: { type: String, default: "我的销售额" },
    title: { type: String, default: "总销售额" },
  },
  watch: {
    lineData: {
      handler: function (val) {
        if (val?.length == 0) return;
        this.getServerData();
      },
    },
  },
  computed: {
    totalNum() {
      return (
        this.lineData
          ?.reduce(
            (total, nowVal) =>
              (parseFloat(total) * 1000 + parseFloat(nowVal.amount) * 1000) /
              1000,
            0
          )
          ?.toFixed(2)+'元' || "0.00元"
      );
    },
  },
  data() {
    return {
      cWidth: 750,
      cHeight: 500,
      id: "ImmkADWetCJGHQRAYJhkqtmpFCKLoeWW",
      pixelRatio: 2,
    };
  },
  methods: {
    getServerData() {
      setTimeout(() => {
        //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
        let res = {
          categories: [],
          series: [{ name: this.chartsName, data: [] }],
        };
        this.lineData?.forEach((el) => {
          res.categories.push(el.time_point);
          res.series[0]?.data.push(el.amount);
        });
        this.drawCharts(this.id, res);
      }, 800)

    },
    /**
     *
     * @param {*} id
     * @param {*} data
     *  此方法解决层级过高问题
     *  此方法需要开启canvas2d="true" type="2d"  2d模式，必须要执行此方法
     *  此方法模拟器层级会过高
     *  无法真机调试
     *  必须真机预览才是最终正常结果
     *  https://www.ucharts.cn/v2/#/help/index，详情可以查看文档
     */
    drawCharts(id, data) {
      /*  #ifndef H5 */
      const query = uni.createSelectorQuery().in(this);
      query
        .select("#" + id)
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res[0]) {
            const canvas = res[0].node;
            const ctx = canvas.getContext("2d");
            canvas.width = res[0].width * this.pixelRatio;
            canvas.height = res[0].height * this.pixelRatio;
            uChartsInstance[id] = new uCharts({
              type: "area",
              context: ctx,
              width: this.cWidth * this.pixelRatio,
              height: this.cHeight * this.pixelRatio,
              categories: data.categories,
              series: data.series,
              pixelRatio: this.pixelRatio,
              animation: true,
              background: "#FFFFFF",
              color: [
                "#fa3534",
                "#91CB74",
                "#FAC858",
                "#EE6666",
                "#73C0DE",
                "#3CA272",
                "#FC8452",
                "#9A60B4",
                "#ea7ccc",
              ],
              padding: [15, 10, 0, 15],
              legend: {},
              enableScroll: true,

              xAxis: {
                disableGrid: true,
                itemCount: 3,
                scrollShow: true,
                scrollAlign: "right",
              },
              yAxis: {
                gridType: "dash",
                dashLength: 2,
              },
              extra: {
                // line: {
                //     type: "curve",
                //     width: 2,
                // },
                area: {
                  type: "curve",
                  opacity: 0.6,
                  addLine: true,
                  width: 2,
                  gradient: true,
                },
              },
            });
          } else {
            console.error("[uCharts]: 未获取到 context");
          }
        });
      /*#endif */
      /*  #ifdef H5 */

      let dataY = []
      let dataX = []
      dataX = data.categories
      for (let i = 0; i < data.series.length; i++) {
        dataY.push(...data.series[i].data)
      }
      const maxValue = Math.ceil(Math.max(...dataY));
      const canvas = document.getElementById(id);
      const myChart = echarts.init(canvas);
      const option = {
        background: "#FFFFFF",
        color: [
          "#fa3534",
          "#91CB74",
          "#FAC858",
          "#EE6666",
          "#73C0DE",
          "#3CA272",
          "#FC8452",
          "#9A60B4",
          "#ea7ccc",
        ],

        legend: {},
        grid: {
          left: '3%',  // 调整左边距
          right: '4%', // 调整右边距
          containLabel: true, // 将标签内容完全包含在图表内
        },
        xAxis: {
          max: 3,
          scrollAlign: "right",
          data: dataX,
          axisLabel: {
            interval: 0, // 显示所有标签
          },
        },
        yAxis: {
          show: true, // 显示 y 轴
          min: 0,
          max: maxValue,// 将 maxValue 向上取整为整数
          gridType: "dash",
          dashLength: 2,
          type: 'value',
          axisLine: {
            show: true, // 显示 y 轴刻度线
            lineStyle: {
              color: "#999", // 刻度线颜色
              width: 1, // 刻度线宽度
              type: "solid", // 刻度线类型
            }
          }
        },
        dataZoom: [
          {
            type: "inside",
            show: true,
            height: 1,  // 设置滚动条的高度
            start: 0,  // 初始视图开始位置（0-100）
            end: 100,  // 初始视图结束位置（0-100）
            handleStyle: {
              color: "#1f90e6",
            },
          },
          // {
          //   type: "inside",  // 内置型数据缩放
          //   start: 0,
          //   end: 3,
          // },
        ],
        series: [{
          name: '',
          type: 'line',
          data: dataY
        }],
        extra: {
          // line: {
          //     type: "curve",
          //     width: 2,
          // },
          area: {
            type: "curve",
            opacity: 0.6,
            addLine: true,
            width: 2,
            gradient: true,
          },
        },
      };

      // 使用配置项显示图表
      myChart.setOption(option);
      /*#endif */

    },

    touchstart(e) {
      uChartsInstance[e.target.id].scrollStart(e);
    },
    touchmove(e) {
      uChartsInstance[e.target.id].scroll(e);
    },
    touchend(e) {
      uChartsInstance[e.target.id].scrollEnd(e);
      uChartsInstance[e.target.id].touchLegend(e);
      uChartsInstance[e.target.id].showToolTip(e);
    },
  },
  /* #ifdef H5 */
  mounted() {
    this.cWidth = uni.upx2px(700)
    //这里的 500 对应 css .charts 的 height
    this.cHeight = uni.upx2px(500)
    this.getServerData()
  },
  /* #endif */
  /* #ifndef H5 */
  onReady() {
    this.cWidth = uni.upx2px(700)
    //这里的 500 对应 css .charts 的 height
    this.cHeight = uni.upx2px(500)
    this.getServerData()
  },
  /* #endif */
};
</script>


<style scoped  lang='scss'>
.card-charts {
  background-color: #fff;
}

.total {
  padding: 20rpx;
  font-size: $font-size-middle;
  font-weight: 700;
  color: $textBlack;
}

.charts {
  width: 750rpx;
  height: 500rpx;
}
</style>