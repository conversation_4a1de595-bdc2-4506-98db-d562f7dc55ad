const install = (Vue, vm) => {
  vm.$u.api = {
    //获取小程序基础配置
    siteInfo: (data = {}, loadingText = '加载中~', showLoading = false) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/public/siteInfo', data)
    },
    //登陆
    login: (data = {}, loadingText = '正在登录中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/public/login', data)
    },
    //登出
    logout: (data = {}, loadingText = '正在退出~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/public/logout', data)
    },
    //获取微信支付openid
    getPayOpenid: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/waapi/public/login', data)
    },
    //修改密码
    modifyPwd: (data = {}, loadingText = '正在退出~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/modifyPwd', data)
    },
    //获取销售信息
    getSalesInfo: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/index/getSalesInfo', data)
    },
    //获取设备概况
    getMachineStatus: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/index/getMachineStatus', data)
    },
    //获取充电设备概况
    bluetoothMachine: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/index/BluetoothMachine', data)
    },

    //获取设备列表
    getUserUMs: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/getUserUMs', data)
    },

    // 设置音乐时长
    startMusic: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/startMusic', data)
    },

    // 编辑设备音量
    getUserVideo: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/turnVolume', data)
    },

    //获取设备详情
    getUMDetail: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/getUMDetail', data)
    },

    //编辑设备详情
    editUMDetail: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/editUMDetail', data)
    },

    //获取设备商品
    getUMShops: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/getUMShops', data)
    },
    //获取商品列表
    getMyProducts: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/product/getMyProducts', data)
    },
    //获取上级商品列表
    getMyParentProducts: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/product/getMyParentProducts', data)
    },
    //导入上级商品
    addParentProducts: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/product/addParentProducts', data)
    },
    //新增商品
    addProducts: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/product/addProducts', data)
    },
    //删除设备商品
    delDeviceGoods: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/delDeviceGoods', data)
    },

    //点位列表
    getMyHotels: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getMyHotels', data)
    },
    //设置点位分成
    divideMoney: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/divideMoney', data)
    },
    //新增点位
    addHotel: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/addHotel', data)
    },
    //编辑点位
    editHotel: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/editHotel', data)
    },

    // 获取设备的点位信息
    getUMHotel: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/getUMHotel', data)
    },
    //点位模板 获取点位模板
    getHotelTemplate: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getHotelTemplate', data)
    },
    //点位模板 保存点位模板并应用模板
    saveAndApplyHotelTemplate: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/saveAndApplyHotelTemplate', data)
    },
    //点位模板 保存点位模板并应用模板
    saveHotelTemplate: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/saveHotelTemplate', data)
    },

    //点位 充电规则模板 获取
    getHotelRechargeRule: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getHotelRechargeRule', data)
    },
    //点位 充电规则模板 编辑
    addHotelRechargeRule: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/addHotelRechargeRule', data)
    },
    //点位 充电规则模板 保存并应用到设备
    addHotelRechargeRuleAndCopy: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/addHotelRechargeRuleAndCopy', data)
    },
    //设备解除绑定点位
    unbindHotel: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/unbindHotel', data)
    },
    //设备绑定点位
    bindHotel: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/bindHotel', data)
    },
    //是否有分配的权限
    isAllotMachine: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/isAllotMachine', data)
    },
    //是否有分配的权限
    allotMachine: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/allotMachine', data)
    },
    //获取我的点位
    getMyHotels: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getMyHotels', data)
    },
    //用户列表
    getMyUserList: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/getMyUserList', data)
    },
    //设置用户禁用启用
    enableUser: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/enableUser', data)
    },
    //确认解绑微信
    unBingWX: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/unBingWX', data)
    },
    // 添加用户
    addUser: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/addUser', data)
    },
    // 获取角色列表
    getRoleAllList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/getRoleList', data)
    },
    // 获取用户详情
    getUserDetail: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/getUserDetail', data)
    },
    // 编辑用户
    editUser: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/editUser', data)
    },
    // 获取订单列表
    getOrderList: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/order/getOrderList', data)
    },
    //获取补货设备
    getBindHotelUMs: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/getBindHotelUMs', data)
    },
    //补货列表调整库存
    setUMStock: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/setUMStock', data)
    },
    //补货列表商品 2g主板开门
    openDoor: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/openDoor', data)
    },
    //一键补货
    setUMAllStock: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/setUMAllStock', data)
    },
    //设备补货记录
    bhLogList: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/bhLogList', data)
    },
    // 保存设备商品
    saveUMShops: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/saveUMShops', data)
    },
    // 清除设备的相关状态
    clearUMModelStatus: (
      data = {},
      loadingText = '清除中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/clearUMModelStatus', data)
    },
    // 设备启动
    startUM: (data = {}, loadingText = '启动中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/startUM', data)
    },
    // 控制灯
    turnLight: (data = {}, loadingText = '设置中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/turnLight', data)
    },
    // 提示音
    turnTipMusic: (data = {}, loadingText = '设置中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/turnTipMusic', data)
    },

    // 获取点位设备
    getHotelMachines: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getHotelMachines', data)
    },
    // 获取点位在售商品
    getHotelProducts: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getHotelProducts', data)
    },
    // 点位审核通过 审核下架
    updateHotelStatus: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/updateHotelStatus', data)
    },

    //获取云学院文章
    articleList: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/public/articleList', data)
    },
    //获取云学院文章分类
    articleCat: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/public/articleCat', data)
    },

    //获取虚拟码对应的CODE
    getUMVscode: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/getUMByVscode', data)
    },
    // 获取设备编号对应的CODE
    getUMByDeviceSn: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/getUMByDeviceSn', data)
    },
    // 获取设备的领取状态
    isActive: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/isActive', data)
    },
    // 设备的领取
    activeMachine: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/activeMachine', data)
    },
    //获取财务流水
    getIncomeList: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/finance/getIncomeList', data)
    },
    //申请提现
    withdrawalMoney: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/withdrawalMoney', data)
    },
    //提现记录
    getCashWithdrawList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/finance/getCashWithdrawList', data)
    },
    //获取消息通知列表
    userMessage: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/userMessage', data)
    },
    //获取消息通知列表
    orderReOpen: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/orderReopen', data)
    },
    //发送已读消息
    readMessage: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/readMessage', data)
    },
    //获取我的积分任务
    getMyTask: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/task/getMyTask', data)
    },
    //添加/更改积分任务
    addTask: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/task/addTask', data)
    },
    //删除积分任务
    delTask: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/task/delTask', data)
    },
    //获取积分记录
    getPointList: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/task/getPointList', data)
    },
    //获取上级商品信息
    getMyParentProducts: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/product/getMyParentProducts', data)
    },
    //购物车商品提交
    addPurchaseOrder: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/purchase_order/addPurchaseOrder', data)
    },
    //获取我的采购数据
    getMyPurchaseList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/purchase_order/getMyPurchaseList', data)
    },
    //获取我的下级采购数据
    getMyLowerPurchaseList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/purchase_order/getMyLowerPurchaseList', data)
    },
    //获取采购详情
    getPurchaseDetail: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/purchase_order/getPurchaseDetail', data)
    },
    //修改采购订单状态
    updatePurchaseOrder: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/purchase_order/updatePurchaseOrder', data)
    },

    //获取设备的广告价格
    getAdPrice: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/advert/getAdPrice', data)
    },
    //添加/编辑广告价格
    saveAdPrice: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/advert/saveAdPrice', data)
    },

    //获取我精选商城订单列表：
    selectedMallGetOrderList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/selected_mall/getOrderList', data)
    },
    //精选商城订单 处理订单状态-由待处理变为已处理：
    changeStatusProcessed: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/selected_mall/changeStatusProcessed', data)
    },
    //精选商城订单 处理订单状态-发货：
    sendOutGoods: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/selected_mall/sendOutGoods', data)
    },

    //广告 查看审核列表
    getMemberApplyList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/advert/getMemberApplyList', data)
    },
    //广告 审核通过
    adChangeStatusProcessed: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/advert/changeStatusProcessed', data)
    },
    //广告 确认上线
    adGoOnline: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/advert/AdGoOnline', data)
    },
    //广告 查看详情
    getMemberApplyDetail: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/advert/getMemberApplyDetail', data)
    },

    //常见问题
    questionList: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/public/questionList', data)
    },

    //获取微信绑定手机号
    getPhoneNumber: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/public/getPhoneNumber', data)
    },
    //提交授权手机号
    saveUserMobile: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/saveUserMobile', data)
    },

    //设备续费   获取续费设备列表
    getUserExpireMachine: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/machine_renew/getUserExpireMachine', data)
    },
    //设备续费  创建订单
    createRenewOrder: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/machine_renew/createRenewOrder', data)
    },
    //设备续费  提交支付
    subRenewPrepay: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/machine_renew/Prepay', data)
    },
    //设备续费 获取续费记录列表
    getRenewOrder: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/getRenewOrder', data)
    },

    //广告管理  获取广告列表
    getAdvertList: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/getAdvertList', data)
    },
    //广告管理  添加广告
    addAdvert: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/addAdvert', data)
    },
    //广告管理  获取单个广告
    getAdvertOne: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/getAdvertOne', data)
    },
    //广告管理  编辑广告
    editAdvert: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/EditAdvert', data)
    },
    //广告管理  删除广告
    deleateAdvert: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/deleateAdvert', data)
    },

    //屏幕管理  获取屏幕列表
    getScreenList: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/screen/getScreenList', data)
    },
    //屏幕管理  推送二维码
    updateQrWx: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/screen/updateQrWx', data)
    },
    //屏幕管理  清空广告
    delPlace: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/screen/delPlace', data)
    },
    //屏幕管理 获取屏幕广告详情
    getScreenAdv: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/screen/getScreenAdv', data)
    },
    //屏幕管理 给屏幕推送广告
    sendScreenImg: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/screen/sendScreenImg', data)
    },
    //屏幕管理 屏幕更换设备的绑定
    bindScreenMachine: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/screen/bindScreenMachine', data)
    },

    //设备广告 获取设备广告列表
    bindAdvertList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/BindAdvertList', data)
    },
    //设备广告 获取单个设备广告数据
    bindAdvertOne: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/BindAdvertOne', data)
    },
    //设备广告 删除单个广告位广告
    bindDelete: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/bindDelete', data)
    },
    //设备广告  编辑单个设备广告
    bindAdvertPost: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/bindAdvertPost', data)
    },
    //设备广告 删除单个设备全部广告
    resetBind: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/resetBind', data)
    },
    //设备广告 编辑单个点位 所有广告
    bindAdvertHotelPost: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/bindAdvertHotelPost', data)
    },
    //设备广告 编辑自身 所有广告
    bindAdvertUserPost: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/bindAdvertUserPost', data)
    },
    //设备广告 编辑多台设备广告
    bindAdvertMachinePost: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/bindAdvertMachinePost', data)
    },
    //公众号管理 公众号列表
    getWechatList: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/getWechatList', data)
    },

    //公众号管理 获取单个公众号信息
    getWechatOne: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/getWechatOne', data)
    },

    //公众号管理 新增公众号小程序
    addWechat: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/addWechat', data)
    },
    //公众号管理 编辑公众号小程序
    editWechat: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/editWechat', data)
    },
    //公众号管理 删除公众号小程序
    delWechat: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Advert/delWechat', data)
    },

    //财务审核 获取财务审核列表
    getWithDrawalsDealList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/finance/getWithDrawalsDealList', data)
    },
    //财务审核 获取单个财务详情
    getWithDrawalsDealInfo: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/finance/getWithDrawalsDealInfo', data)
    },
    //财务审核 修改提现信息
    modifyAccount: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/finance/modifyAccount', data)
    },

    //财务审核 审核通过
    checkTrue: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/finance/checkTrue', data)
    },
    //财务审核 审核驳回
    checkFalse: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/finance/checkFalse', data)
    },
    //财务审核 确认打款
    financeConfirmPay: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/finance/pay', data)
    },

    //充电订单 获取充电订单列表
    getChargeOrderList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/order/getChargeOrderList', data)
    },

    //认养管理 获取列表
    getInvestOrderList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/invest/getInvestOrderList', data)
    },
    //认养管理 获取我的被认养设备
    getInvestUMList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/invest/getInvestUMList', data)
    },

    //认养管理 订单审核通过
    investProcess: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/invest/Process', data)
    },
    //认养管理 创建账号
    investCreateNumber: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/invest/createNumber', data)
    },
    //数据分析 获取用户首页
    getUserReport: (data = {}, loadingText = '加载中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/order/getUserReport', data)
    },
    //优惠券管理  获取商家购买优惠券套餐
    getHotelCouponPackage: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getHotelCouponPackage', data)
    },
    //优惠券管理 核销优惠券
    writeOffCouponHotel: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/writeOffCouponHotel', data)
    },
    //优惠券管理 创建优惠券
    createCouponHotel: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/createCouponHotel', data)
    },
    //优惠券管理 获取商家购买得套餐
    getHotelCouponPackage: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getHotelCouponPackage', data)
    },
    //优惠券管理 用户领取记录
    getReceiveCouponsHotelList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getReceiveCouponsHotelList', data)
    },
    //优惠券管理 获取优惠券详情
    getCouponHotelUserDetail: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getCouponHotelUserDetail', data)
    },
    //优惠券管理 购买优惠券套餐
    createCouponHotelPackageAndPrepay: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/order/createCouponHotelPackageAndPrepay', data)
    },
    //优惠券管理 .获取商户优惠券列表
    getCouponHotelList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getCouponHotelList', data)
    },
    //优惠券管理 .获取商户优惠券可以创建的数量
    getCouponHotelNum: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getCouponHotelNum', data)
    },
    //优惠券管理 .获取商户管理信息
    getCouponHotelManageInfo: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/getCouponHotelManageInfo', data)
    },
    //修改免费开关
    setUpdateHotelAllFreeStatus: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/updateHotelAllFreeStatus', data)
    },
    //修改是否自动待机
    setUpdateHotelAutoOnOffStatus: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/updateHotelAutoOnOffStatus', data)
    },
    //批量添加水量
    setupdateHotelAllCbm: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/updateHotelAllCbm', data)
    },
    //设置设备禁用启用
    setupdateMachineStatus: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/updateMachineStatus', data)
    },
    // 获取我的邀请
    getMyInviteUserList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/getMyInviteUserList', data)
    },
    // 提交身份认证
    setauthRealInfo: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/authRealInfo', data)
    },
    // 退款
    refundOrder: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/order/refundOrder', data)
    },
    //获取随机任务列表
    getRandList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Rand/index', data)
    },
    //添加随机任务列表
    setAddRand: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Rand/add', data)
    },
    //修改随机任务列表
    setUpdateRand: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Rand/update', data)
    },
    //修改随机任务列表
    setDeletRand: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/Rand/delete', data)
    },
    //更新状态接口
    getAllStatus: (
      data = {},
      loadingText = '更新中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/getAllStatus', data)
    },
    //获取操作记录
    getUMLogList: (
      data = {},
      loadingText = '更新中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/getUMLogList', data)
    },
    //开机自检
    setPowerOnTestStatus: (
      data = {},
      loadingText = '更新中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/setPowerOnTestStatus', data)
    },
    //实名信息
    getRealInfo: (
      loadingText = '更新中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/getRealInfo')
    },
    //间隔时间
    setStartAndStopTime: (
      data = {},
      loadingText = '更新中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/setStartAndStopTime', data)
    },
    //获取设备看板
    dataBashBoardByUm: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/data_bash_board/dataBashBoardByUm', data)
    },
    //获取场地方看板
    dataBashBoardByHotel: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/data_bash_board/dataBashBoardByHotel', data)
    },
    //获取订单看板
    dataBashBoardByOrder: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/data_bash_board/dataBashBoardByOrder', data)
    },
    //获用户收益
    getUserCashReport: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/data_bash_board/getUserCashReport', data)
    },
    //获取微信签名字符串
    getJsSign: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/public/getAccountJsSing', data)
    },
    //删除场地方
    deleteHotel: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/hotel/deleteHotel', data)
    },
    //获取银行列表
    getBankList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user/getBankList', data)
    },
    //定时开关机
    autoOnOff: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/autoOnOff', data)
    },
    //关机
    shutdown: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/shutdown', data)
    },
    //关闭刹车
    offBrake: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/offBrake', data)
    },
    //获取商户列表
    getPackageList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/marketing/getPackageList', data)
    },
    //创建充值套餐
    addPackage: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/marketing/addPackage', data)
    },
    //编辑充值套餐
    editPackage: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/marketing/editPackage', data)
    },
    //删除充值套餐
    delPackage: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/marketing/delPackage', data)
    },
    //获取产品列表
    getProduct: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/getUMProductTypes', data)
    },
    //添加电子围栏
    addGeofence: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/geofence/addGeofence', data)
    },

    //获取电子围栏
    getHotelGeofence: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/geofence/getHotelGeofence', data)
    },
    //添加p点
    addPGeofence: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/geofence/addPGeofence ', data)
    },
    //获取p点
    getHotelPGeofence: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/geofence/getHotelPGeofence', data)
    },
    //删除电子围栏
    delGeofence: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/geofence/delGeofence ', data)
    },
    //删除p点

    delPGeofence: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/geofence/delPGeofence', data)
    },

    //开启电源

    startCar: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/startCar', data)
    },
    //开启关闭履带车
    onAndOffCar: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/onAndOffCar', data)
    },
    //开启音乐灯光//灯带和灯光

    startCarLightAndMusic: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/startCarLightAndMusic', data)
    },
    // 水枪 开关灯带接口
    Switchlightstrip: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/turnWaterGunLight', data)
    },
    // 水枪 音乐开关接口
    WaterMusicswitch: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/turnWaterGunMusic', data)
    },
    // 水枪 水泵开关接口
    Waterpumpswitch: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/turnWaterGunPump', data)
    },
    //LED灯

    turnLightOne: (
      data = {},
      loadingText = '操作中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/turnLightOne', data)
    },
    //LED灯

    turnLightTwo: (
      data = {},
      loadingText = '操作中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/turnLightTwo', data)
    },
    //新履带车主板开启音乐灯光

    startCarLight: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/startCarLight', data)
    },
    //开启灯光
    onAndOffCarLightAndMusic: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/onAndOffCarLightAndMusic', data)
    },
    //开启出泡泡

    startCarPP: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/startCarPP', data)
    },
    //开启出泡泡
    onAndOffCarPP: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/onAndOffCarPP', data)
    },
    //开启履带车和音乐
    startCarAndMusic: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/startCarAndMusic ', data)
    },
    //获取行动轨迹
    getUmGpsList: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/getUmGpsList', data)
    },
    //修改烟雾大小
    setELiquidSize: (
      data = {},
      loadingText = '加载中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/setELiquidSize', data)
    },
    //更改工作模式
    updateMachineWorkType: (
      data = {},
      loadingText = '更改中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/updateMachineWorkType ', data)
    },
    //更改推荐套餐
    setRecommendShop: (
      data = {},
      loadingText = '更改中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/machine_shop/setRecommendShop', data)
    },
    //更改场地套餐
    setHotelRecommendShop: (
      data = {},
      loadingText = '更改中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/machine_shop/setHotelRecommendShop ', data)
    },
    //获取主板列表
    getMotherboardsList: (
      loadingText = '获取主板列表中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/getUMMachineMaintainList')
    },

    //更换主板
    SetMotherboards: (
      data = {},
      loadingText = '更改中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/maintenanceUMBoard', data)
    },
    //设备测试的雷达范围
    //开启启动时雷达
    // startCarRadarByUsing: (
    //   data = {},
    //   loadingText = '操作中~',
    //   showLoading = true,
    // ) => {
    //   setConfig(loadingText, showLoading)
    //   return vm.$u.post('/api/lora/startCarRadarByUsing', data)
    // },
    // //开启空闲时雷达
    // startCarRadarByFree: (
    //   data = {},
    //   loadingText = '操作中~',
    //   showLoading = true,
    // ) => {
    //   setConfig(loadingText, showLoading)
    //   return vm.$u.post('/api/lora/startCarRadarByFree', data)
    // },
    //开启启动时雷达
    setCarRadarRangeByUsing: (
      data = {},
      loadingText = '操作中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/setCarRadarRangeByUsing', data)
    },
    //开启空闲时雷达
    setCarRadarRangeByFree: (
      data = {},
      loadingText = '操作中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/lora/setCarRadarRangeByFree', data)
    },
    //营销模式更改
    updateMachineShopType: (
      data = {},
      loadingText = '操作中~',
      showLoading = true,
    ) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/updateMachineShopType', data)
    },
    // 更新绑定二维码
    bindPseudoCode: (data = {}, loadingText = '设置中~', showLoading = true) => {
      setConfig(loadingText, showLoading)
      return vm.$u.post('/api/user_machine/bindPseudoCode', data)
    },
  }
  const setConfig = (loadingText, showLoading) => {
    Vue.prototype.$u.http.config.showLoading = showLoading
    Vue.prototype.$u.http.config.loadingText = loadingText
    //这里配置每次请求的域名  需要改为测试域名请改这里

    /* #ifndef H5 */
    Vue.prototype.$u.http.config.baseUrl = vm.vSelectSysObj.extra
    /* #endif */
    // Vue.prototype.$u.http.config.baseUrl = "https://test.51xhkj.com"
  }
}
export default {
  install,
}
