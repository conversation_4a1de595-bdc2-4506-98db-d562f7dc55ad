<template>
  <view class="subsection" @click.stop="handleSubsection">
    <view class="title-top">
      <view class="subsetion_title">
        <view>
          {{ title }}
        </view>

        <view class="title_right">
          <BaseIcon class="infoCircle" name="info-circle" size="35" @onClick="isShowParmes()" />
        </view>
      </view>
      <view class="center" v-if="taberType == 'devesn'">
        统计时间 {{ endDate }}
      </view>
      <view class="titl2" @click="NavgetTo()" v-if="userLogin && false">
        查看更多》
      </view>
    </view>

    <view>
      <block v-if="taberType == 'order'">
        <view class="profit-title">
          选择周期：{{ startDate }} ~ {{ endDate }}
        </view>
        <view @click="handleSubsection">
          <u-subsection mode="subsection" :active-color="mainColor" :list="subsectionList" :current="current"
            @change="changeSubsection" />
        </view>

      </block>
      <view class="profit_item_box">
        <view class="profit_item" v-for="(item, i) in dataList" :key="i">
          <text class="h6">{{ item.title }}</text>
          <text>{{ item.number }}</text>
        </view>
      </view>
      <view class="tabs">
        <BaseTabs height="80" :current="curTabIndex" :list="tabList" @change="tabChange" isShowBar
          v-if="taberType == 'order'" />
      </view>
      <block v-if="taberType != 'devesn'">
        <block v-if="taberType == 'pointName'">
          <view class="profit-title">
            选择周期：{{ startDate }} ~ {{ endDate }}
          </view>
          <u-subsection mode="subsection" :active-color="mainColor" :list="subsectionList" :current="current"
            @change="changeSubsection" />
        </block>

        <view class="profit" v-if="curTabIndex == 0">
          <UCharts v-show="show" :data2="data2" :lineData="lineData" :num="num" :chartsName="title"
            :titleLIst="titleLIst" :isId="taberType" />
        </view>
        <view class="profit" v-if="curTabIndex == 1">
          <ECharts isId="order" :dataList="o_pay_list" title1="支付订单数" :title2="total_num" v-show="show" />
        </view>
        <view class="profit" v-if="curTabIndex == 2">
          <ECharts isId="onorder" :dataList="o_unpay_list" title1="未支付订单数" :title2="title2" v-show="show" />
        </view>
      </block>
      <view class="center" v-if="taberType != 'devesn'">
        {{ startDate }} ~ {{ endDate }} {{ curTabIndex == 0 ? prompt : curTabIndex == 1 ? '支付订单数占比' : '未支付订单数占比' }}
      </view>
    </view>
    <u-calendar v-model="showCalendar" mode="range" :safe-area-inset-bottom="true" btn-type="error"
      :range-color="mainColor" :active-bg-color="mainColor" :change-year="false" @change="changeCalendar"></u-calendar>

  </view>
</template>

<script>
import BaseIconModal from '@/components/base/BaseIconModal.vue'
import UCharts from '../uchart/UCharts.vue'
import ECharts from '../uchart/Echarts.vue'
import BaseIcon from '@/components/base/BaseIcon.vue'
import uCalendar from '@/components/uni-calendar/u-calendar.vue'
import { subtractDaysAndFormat } from '@/wxutil/times'
import BaseTabs from '@/components/base/BaseTabs.vue'
export default {
  components: {
    BaseIconModal,
    UCharts,
    uCalendar,
    BaseIcon,
    BaseTabs,
    ECharts
  },
  name: 'DashboardsCard',
  props: {
    title: {
      type: String,
      default: '',
    },
    prompt: {
      type: String,
      default: '',
    },
    mainColor: {
      type: String,
      default: 'rgb(22, 120, 185)',
    },
    taberType: {
      type: String,
      default: 'devesn',
    },
    titleLIst: {
      type: Array,
      default: function () {
        return []
      },
    },
    userLogin: {
      type: String,
      default: '',
    },
    num: {
      type: [String, Number],
      default: 3,
    },
    show: {
      type: Boolean,
      default: true,
    },
    params: {
      type: Object,
      default: function () {
        return {}
      },
    },
  },
  data() {
    return {
      showCalendar: false,
      current: 0,
      subsectionList: [
        {
          name: '今天',
          status: 1,
        },
        {
          name: '昨天',
          status: 2,
        },

        {
          name: '前天',
          status: 3,
        },
        {
          name: '本月',
          status: 4,
        },

        {
          name: '上月',
          status: 5,
        },
        {
          name: '自定义',
          status: 6,
        },
      ],
      startDate: '',
      endDate: '',
      lineData: [],
      dataList: [],
      data2: [],
      totalNum: '', //关联设备数
      totalum: '', //关联场地数
      tabList: [
        {
          name: '订单收益数据',
          status: 0,
        },
        {
          name: '支付订单数占比',
          status: 1,
        },
        {
          name: '未支付订单数占比',
          status: 2,
        }
      ],
      curTabIndex: 0,
      o_pay_list: [],
      o_unpay_list: [],
      total_num: 0,//支付订单数
      title2: 0,
      orderNumList:{
        one:0,
        two:1,
        three:2,
        four:3,
        five:4,
        six:5,
        seven:9,
        eight:10,
        nine:11,
        ten:12,
        eleven:13,
        twelve:14,
        thirteen:15,
        fourteen:16,
        fifteen:17,
        sixteen:6,
        seventeen:7,
        eighteen:8,
      
      
      

        
      },
      deviceNumList:{
        one:0,
        two:1,
        three:2,
        four:3,
        five:4,
        six:5,
        seven:6,
        eight:7,
        nine:8,
        ten:9,
      },
      potionNumList:{
        one:0,
        two:1,
        three:2,
      }
    }
  },
  watch: {
    userLogin: {
      handler: function (val) {
        // console.log('搜索',val)
        if (val == '') return
        this.getList()
      },
    },
    params: {
      handler: function (val) {
        // console.log('数值变化',val)
        this.setPapmes()
      },
    },
    showCalendar: {
      handler: function (val) {
        this.$emit('changeShow', val)
      },
    },
  },
  methods: {
    tabChange(e) {
      this.curTabIndex = e
    },
    formatDate(dateString) {
      // 使用正则表达式匹配日期字符串的各个部分  
      const parts = dateString.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
      if (!parts) {
        throw new Error('Invalid date format');
      }

      // 提取年份、月份和日期  
      const [_, year, month, day] = parts;

      // 确保月份和日期都是两位数  
      const formattedMonth = month.padStart(2, '0');
      const formattedDay = day.padStart(2, '0');

      // 返回格式化的日期字符串  
      return `${year}-${formattedMonth}-${formattedDay}`;
    },
    /* 计算天数 */
    getDaysBetween(date1, date2) {
      // 将字符串日期转换为 Date 对象
      var startDate = new Date(this.formatDate(date1))
      var endDate = new Date(this.formatDate(date2))

      // 计算时间差（以毫秒为单位）
      var timeDiff = endDate - startDate

      // 将时间差转换为天数
      var days = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
      console.log('计算天数', endDate, startDate, date1, date2, days)
      return days
    },
    /* 修改数据 */
    setPapmes() {
      if (this.params.totalum && this.params.totalum != 0) {
        this.dataList[this.orderNumList.ten].number =
          this.totalum +
          '(' +
          ((this.totalum / this.params.totalum) * 100).toFixed(1) +
          '%)' //订单关联场地数
      } else {
        this.dataList[this.orderNumList.ten].number = this.totalum + '(0.0%)'
      }
      if (this.params.totalNum && this.params.totalNum != 0) {
        this.dataList[this.orderNumList.thirteen].number =
          this.totalNum +
          '(' +
          ((this.totalNum / this.params.totalNum) * 100).toFixed(1) +
          '%)' //订单关联设备数
      } else {
        this.dataList[this.orderNumList.thirteen].number = this.totalNum + '(0.0%)' //订单关联设备数
      }
    },
    /* 点击提示 */
    isShowParmes() {
      this.$emit('isShowParmes', this.taberType, this.dataList)
    },
    /* 更新时间 */
    handleData(date, startDate, endDate) {
      // this.queryDate = date
      this.startDate = startDate
      this.endDate = endDate
      this.getList()
    },
    /* 发送请求 */
    async getList() {
      let data = {
        start_time: this.startDate,
        end_time: this.endDate,
        user_login: this.userLogin,
      }
      if (this.taberType == 'devesn') {
        let data = {
          user_login: this.userLogin,
        }
        let list = await this.$u.api.dataBashBoardByUm(data)
        this.dataList[this.deviceNumList.one].number = list.total //设备总数
        this.dataList[this.deviceNumList.two].number =
          list.total != 0
            ? list.onlineTotal +
            '(' +
            ((list.onlineTotal / list.total) * 100).toFixed(1) +
            '%)'
            : '0(0.0%)' //在线数
        this.dataList[this.deviceNumList.three].number =
          list.total != 0
            ? list.pavedTotal +
            '(' +
            ((list.pavedTotal / list.total) * 100).toFixed(1) +
            '%)'
            : '0(0.0%)' //已铺数
        this.dataList[this.deviceNumList.four].number =
          list.total != 0
            ? list.pp_um_total.pavedTotal +
            '(' +
            ((list.pp_um_total.pavedTotal / list.pavedTotal) * 100).toFixed(1) +
            '%)'
            : '0(0.0%)' //泡泡机已铺数
        this.dataList[this.deviceNumList.five].number =
          list.total != 0
            ? list.ldc_um_total.pavedTotal +
            '(' +
            ((list.ldc_um_total.pavedTotal / list.pavedTotal) * 100).toFixed(1) +
            '%)'
            : '0(0.0%)' //履带车已铺数
        this.dataList[this.deviceNumList.six].number =
          list.total != 0
            ? list.kqdp_um_total.pavedTotal +
            '(' +
            ((list.kqdp_um_total.pavedTotal / list.pavedTotal) * 100).toFixed(1) +
            '%)'
            : '0(0.0%)' //空气大炮已铺数
        this.dataList[this.deviceNumList.seven].number =
          list.pavedTotal != 0
            ? list.pavedOnlineTotal +
            '(' +
            ((list.pavedOnlineTotal / list.pavedTotal) * 100).toFixed(1) +
            '%)'
            : '0(0.0%)' //已铺在线数数
        this.dataList[this.deviceNumList.eight].number =
          list.pavedTotal != 0
            ? list.last3Total +
            '(' +
            ((list.last3Total / list.pavedTotal) * 100).toFixed(1) +
            '%)'
            : '0(0.0%)' //3天无成交数
        this.dataList[this.deviceNumList.nine].number =
          list.pavedTotal != 0
            ? list.last7Total +
            '(' +
            ((list.last7Total / list.pavedTotal) * 100).toFixed(1) +
            '%)'
            : '0(0.0%)' //7天无成交数
        this.dataList[this.deviceNumList.ten].number =
          list.pavedTotal != 0
            ? list.last30Total +
            '(' +
            ((list.last30Total / list.pavedTotal) * 100).toFixed(1) +
            '%)'
            : '0(0.0%)' //30天无成交数
        let totalNum = {
          totalNum: list.pavedTotal,
        }
        // console.log('变数',totalNum)
        this.$emit('busNum', totalNum) //已铺数
      } else if (this.taberType == 'pointName') {
        let list = await this.$u.api.dataBashBoardByHotel(data)
        this.dataList[this.potionNumList.one].number = list.h_data.hotel_total //场地总数
        this.dataList[this.potionNumList.two].number =
          list.h_data.hotel_total != 0
            ? list.h_data.hotel_um_total +
            '(' +
            (
              (list.h_data.hotel_um_total / list.h_data.hotel_total) *
              100
            ).toFixed(1) +
            '%)'
            : '0(0.0%)' //有效场地数
        this.dataList[this.potionNumList.three].number =
          list.h_data.hotel_um_total != 0
            ? list.h_data.hotel_7_no_pay_total +
            '(' +
            (
              (list.h_data.hotel_7_no_pay_total /
                list.h_data.hotel_um_total) *
              100
            ).toFixed(1) +
            '%)'
            : '0(0.0%)' //7天无动销数
        let total = {
          totalum: list.h_data.hotel_um_total,
        }
        this.$emit('busNum', total) //有效场地数
        this.lineData = list.h_o_top_5
      } else if (this.taberType == 'order') {
        this.curTabIndex = 0
        let day = this.getDaysBetween(this.startDate, this.endDate) + 1
        let list = await this.$u.api.dataBashBoardByOrder(data)
        this.o_pay_list = list.o_pay_list
        this.o_unpay_list = list.o_unpay_list
        this.dataList[this.orderNumList.one].number = list.o_data.sum_total_num || 0 //订单总数
        // this.dataList[1].number = list.o_data.total_num||0 //订单完单数(占订单总数百分比)


        this.dataList[this.orderNumList.two].number = list.o_data.sum_total_num
          ? list.o_data.total_num +
          '(' +
          ((list.o_data.total_num / list.o_data.sum_total_num) * 100).toFixed(
            1,
          ) +
          '%)'
          : '0(0.0%)' //订单完单数(占订单完单总数的百分比)
        this.dataList[this.orderNumList.seven].number = list.o_data.total_free_num || 0 //免费订单数
        this.dataList[this.orderNumList.four].number = list.o_data.pp_order_info.total_num
          ? list.o_data.pp_order_info.total_num +
          '(' +
          ((list.o_data.pp_order_info.total_num / list.o_data.total_num) * 100).toFixed(
            1,
          ) +
          '%)'
          : '0(0.0%)' //泡泡车完单总数(占订单完单总数的百分比)
        this.dataList[this.orderNumList.five].number = list.o_data.ldc_order_info.total_num
          ? list.o_data.ldc_order_info.total_num +
          '(' +
          ((list.o_data.ldc_order_info.total_num / list.o_data.total_num) * 100).toFixed(
            1,
          ) +
          '%)'
          : '0(0.0%)' //履带车完单总数(占订单完单总数的百分比)
        this.dataList[this.orderNumList.six].number = list.o_data.kqdp_order_info.total_num
          ? list.o_data.kqdp_order_info.total_num +
          '(' +
          ((list.o_data.kqdp_order_info.total_num / list.o_data.total_num) * 100).toFixed(
            1,
          ) +
          '%)'
          : '0(0.0%)' //空气大炮完单总数(占订单完单总数的百分比)
          this.dataList[this.orderNumList.sixteen].number = list.o_data.pp_order_info.total_amount
          ? list.o_data.pp_order_info.total_amount +
          '(' +
          ((list.o_data.pp_order_info.total_amount / list.o_data.total_amount) * 100).toFixed(
            1,
          ) +
          '%)'
          : '0(0.0%)' //泡泡车完单金额(占完单交易总金额的百分比)
          this.dataList[this.orderNumList.seventeen].number = list.o_data.ldc_order_info.total_amount
          ? list.o_data.ldc_order_info.total_amount +
          '(' +
          ((list.o_data.ldc_order_info.total_amount / list.o_data.total_amount) * 100).toFixed(
            1,
          ) +
          '%)'
          : '0(0.0%)' //履带车车完单金额(占完单交易总金额的百分比)
          this.dataList[this.orderNumList.eighteen].number = list.o_data.kqdp_order_info.total_amount
          ? list.o_data.kqdp_order_info.total_amount +
          '(' +
          ((list.o_data.kqdp_order_info.total_amount / list.o_data.total_amount) * 100).toFixed(
            1,
          ) +
          '%)'
          : '0(0.0%)' //空气大炮完单金额(占完单交易总金额的百分比)
        this.dataList[this.orderNumList.three].number = list.o_data.total_amount
          ? (list.o_data.total_amount * 1).toFixed(2)
          : '0.00' //订单交易总额
        this.dataList[this.orderNumList.eight].number = list.o_data.total_num
          ? (list.o_data.total_amount / list.o_data.total_num).toFixed(2)
          : '0.00' //订单均价
        this.dataList[this.orderNumList.nine].number = list.o_data.total_amount
          ? list.c_amount.toFixed(2) +
          '(' +
          ((list.c_amount / (list.o_data.total_amount * 1)) * 100).toFixed(
            1,
          ) +
          '%)'
          : '0(0.0%)' //本人收益金额(占交易总额的百分比)
        this.dataList[this.orderNumList.ten].number = list.o_data.total_hotel.toFixed(0) || '0' //订单关联场地数

        this.dataList[this.orderNumList.eleven].number =
          list.o_data.total_hotel != 0 && day 
            ? (list.o_data.total_num / day / list.o_data.total_hotel).toFixed(2) ?? '0.00' : '0.00' //单场地订单数
        this.dataList[this.orderNumList.twelve].number =
          list.o_data.total_amount && list.o_data.total_hotel != 0 && day
            ? (
              list.o_data.total_amount /
              day /
              list.o_data.total_hotel
            ).toFixed(2) ?? '0.00'
            : '0.00' //单场地收益数据
        console.log('验证数字', list.o_data.total_amount, day, list.o_data.total_hotel, (list.o_data.total_num / day / list.o_data.total_hotel).toFixed(2))
        this.dataList[this.orderNumList.thirteen].number = list.o_data.total_um.toFixed(0) || '0' //订单关联设备数
        this.dataList[this.orderNumList.fourteen].number =
          list.o_data.total_um != 0 && day != 0
            ? (list.o_data.total_num / day / list.o_data.total_um).toFixed(2) ?? '0.00' : '0.00' //单机订单数
        this.dataList[this.orderNumList.fifteen].number =
          list.o_data.total_amount && list.o_data.total_um != 0 && day != 0
            ? (list.o_data.total_amount / day / list.o_data.total_um).toFixed(2) ?? '0.00' : '0.00' //单机收益数
        this.totalum = this.dataList[this.orderNumList.ten].number
        this.totalNum = this.dataList[this.orderNumList.thirteen].number
        this.setPapmes()
        this.lineData = list.o_list
        this.data2 = list.c_data
        this.total_num = list.o_data.total_num || 0
        this.title2 = list.o_data.sum_total_num && list.o_data.total_num ? list.o_data.sum_total_num - list.o_data.total_num : 0
        // this.startDate = this.lineData[0]?.time_point
        // this.endDate = this.lineData[this.lineData?.length - 1]?.time_point
      }
      //   this.lineData = list.table
      // this.startDate = this.lineData[0]?.time_point
      //     this.endDate = this.lineData[this.lineData?.length - 1]?.time_point
    },
    handleSubsection() {
      if (this.current === 5 && !this.showCalendar) this.showCalendar = true
    },

    changeSubsection(i) {
      this.current = i
      if (this.subsectionList[i].status == 6) {

        this.showCalendar = true
      } else {
        //
        let selectItem = this.subsectionList[i]
        let date = new Date()
        if (selectItem.status < 4) {
          this.startDate = this.endDate = subtractDaysAndFormat(
            selectItem.status - 1,
          )
        } else if (selectItem.status == 4) {
          this.endDate =
            date.getFullYear() +
            '-' +
            (date.getMonth() + 1) +
            '-' +
            date.getDate()
          this.startDate =
            date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + 1
        } else if (selectItem.status == 5) {
          let currentDate = new Date();
          let lastMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);

          this.startDate = lastMonthDate.getFullYear() + '-' + (lastMonthDate.getMonth() + 1) + '-1';
          this.endDate = lastMonthDate.getFullYear() + '-' + (lastMonthDate.getMonth() + 1) + '-' + lastMonthDate.getDate();

        }
        //当前日期的年 2022
        this.handleData('', this.startDate, this.endDate)
      }
    },
    changeCalendar(e) {
      //
      let { startDate, endDate } = e
      this.handleData('', startDate, endDate)
    },
    updata() {
      if (this.taberType == 'devesn') {
        this.dataList = [
          {
            title: '设备总数',
            number: '0',
            text: '统计该用户下所有设备数',
          },
          {
            title: '在线数',
            number: '0(0.0%)',
            text: '统计该用户下设备在线的设备数(占所有设备的百分比)',
          },
          {
            title: '已铺数',
            number: '0(0.0%)',
            text: '统计该用户下设备已经铺设的设备数(占所有设备的百分比)',
          },
          {
            title: '泡泡车已铺数',
            number: '0(0.0%)',
            text: '统计该用户下已经铺设的泡泡机设备数(占所有已铺设备数的百分比)',
          },
          {
            title: '履带车已铺数',
            number: '0(0.0%)',
            text: '统计该用户下已经铺设的履带车设备数(占所有已铺设备数的百分比)',
          },
          {
            title: '空气大炮已铺数',
            number: '0(0.0%)',
            text: '统计该用户下已经铺设的空气大炮设备数(占所有已铺设备数的百分比)',
          },
          {
            title: '已铺在线数',
            number: '0(0.0%)',
            text: '统计该用户下设备已经铺设的设备的在线数(占已铺设备的百分比)',
          },
          {
            title: '三天无成交',
            number: '0',
            text: '统计该账户3天内无成交的设备数(占已铺数的百分比)',
          },
          {
            title: '七天无成交',
            number: '0',
            text: '统计该账户7天内无成交的设备数(占已铺数的百分比)',
          },
          {
            title: '大于30天无成交',
            number: '0',
            text: '统计该用户30天内无成交的设备数(占已铺数的百分比)',
          },
        ]
      } else if (this.taberType == 'pointName') {
        this.dataList = [
          {
            title: `${this.vPointName}总数`,
            number: '0',
            text: `统计该用户下所有的${this.vPointName}数`,
          },
          {
            title: `有效${this.vPointName}数`,
            number: '0',
            text: `统计该用户下有设备的${this.vPointName}数(占所有${this.vPointName}的百分比)`,
          },
          {
            title: '7天无动销数',
            number: '0(0.0%)',
            text: `统计该用户下7天内无交易的${this.vPointName}数(占有效${this.vPointName}的百分比)`,
          },
        ]
      } else if (this.taberType == 'order') {
        this.dataList = [
          {
            title: '订单总数',
            number: '0',
            text: '统计该用户在该时间内所产生的订单数',
          },
          {
            title: '完单总数',
            number: '0(0.0%)',
            text: '统计该用户在该时间内支付完成的订单数(占订单总数的额百分比)',
          },
          {
            title: '完单交易总金额',
            number: '0.00',
            text: '统计该用户在该时间内所产生的交易总金额',
          },
          {
            title: '泡泡车完单总数',
            number: '0(0.0%)',
            text: '统计该用户在该时间内支付完成的泡泡车订单数(占订单总数的额百分比)',
          },
          {
            title: '履带车完单总数',
            number: '0(0.0%)',
            text: '统计该用户在该时间内支付完成的履带车订单数(占订单总数的额百分比)',
          },
          {
            title: '空气大炮完单总数',
            number: '0(0.0%)',
            text: '统计该用户在该时间内支付完成的空气大炮订单数(占订单总数的额百分比)',
          },
          {
            title: '泡泡车交易金额',
            number: '0(0.0%)',
            text: '统计该用户在该时间内支付完成的泡泡车交易金额(占交易总金额百分比)',
          },
          {
            title: '履带车交易金额',
            number: '0(0.0%)',
            text: '统计该用户在该时间内支付完成的履带车交易金额(占交易总金额百分比)',
          },
          {
            title: '空气大炮交易金额',
            number: '0(0.0%)',
            text: '统计该用户在该时间内支付完成的空气大炮交易金额(占交易总金额百分比)',
          },
      
          {
            title: '免费订单数',
            number: '0',
            text: '统计该用户在该时间内所产生的免费订单数',
          },
         
          {
            title: '订单均价',
            number: '0.00',
            text: '统计该用户在该时间内平均每单的价格',
          },
          {
            title: '本人收益金额',
            number: '0.00',
            text: '统计该用户在该时间内本人所产生的收益金额(占交易总额的百分比)',
          },

          {
            title: `订单关联${this.vPointName}数`,
            number: '0',
            text: `统计该用户下订单所关联的${this.vPointName}数(占已有效${this.vPointName}数的百分比)`,
          },

          {
            title: `单${this.vPointName}日均订单数`,
            number: '0.00',
            text: `统计该用户下在1天内1个${this.vPointName}所产生的支付完成订单数`,
          },

          {
            title: `单${this.vPointName}日均营收`,
            number: '0.00',
            text: `统计该用户在1天内1个${this.vPointName}所产生的收益`,
          },
          {
            title: '订单关联设备数',
            number: '0(0.0%)',
            text: `统计该用户下订单所关联的设备数(占已铺设备的百分比)`,
          },
          {
            title: '单机日均订单数',
            number: '0.00',
            text: `统计该用户在1天内1台机器所产生的支付完成的订单数`,
          },
          {
            title: '单机日均营收',
            number: '0.00',
            text: `统计该用户在1天内1台机器所产生的收益`,
          },
        ]
      }
      this.endDate = this.startDate = subtractDaysAndFormat(0)
      this.handleData('', this.startDate, this.endDate)
    },
    NavgetTo() {
      this.$emit('NavgetTo')
    },
  },
  mounted() {
    this.updata()
    uni.$on('updata', this.updata)
  },
  beforeDestroy() {
    uni.$off('updata')
  }
}
</script>

<style lang="scss" scoped>
.subsection {
  // border: 1px solid #000;
  background-color: #fff;
  padding: 20rpx;
  border-radius: 15rpx;
  margin: 20rpx;
}

.profit-title {
  margin: 10rpx 0;
}

.title-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profit {
  background-color: #fff;

  &-title {
    padding: 0rpx;
    font-size: $font-size-middle;
    color: $textDarkGray;
  }
}

.profit_item_box {
  margin: 15rpx 0;

  // border: 1px solid #000;
  display: flex;
  flex-wrap: wrap;

  .h6 {
    font-size: 25rpx;
    margin-bottom: 30rpx;
  }

  .profit_item {
    border: 2rpx solid rgb(207, 205, 205);
    // margin-right: -2rpx;
    margin-left: -2rpx;
    margin-bottom: -2rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 50rpx 10rpx;
    width: 33.33%;
  }

  .profit_text {
    font-size: 25rpx;
    color: #666;
    margin: 20rpx 0;
    width: 100%;
    text-align: center;
    // border: 1px solid #000;
  }
}

.title_right {
  width: 30rpx;
  height: 30rpx;
  margin-left: 5rpx;
  // border: 1px solid #000;
}

.subsetion_title {
  margin: 15rpx 0;
  display: flex;
  // border: 1px solid #000;
  align-items: center;
  // background-color: #333;
  // height: 120rpx;
}

.center {
  text-align: center;
  margin: 15rpx 0;
}

.info-card {
  &-title {
    padding: 20rpx;
    padding-bottom: 0;
    font-size: $font-size-middle;
    color: $textBlack;
    font-weight: 700;
  }

  &-box {
    padding: 20rpx;
    background-color: #fff;
    background-color: #1a76b3;
  }
}

.tabs {
  // border: 2rpx solid red;
  padding-right: 20rpx;

}

.titl2 {
  color: #1a76b3;
}
</style>
