<template>
  <view class="card" @click="onCard">
    <view class="card_top">
      <view class="device-code-state flexRowBetween">
        <view class="device-code">
          <view class="device-code-item">设备编号：</view>
          <text user-select>{{ info.device_sn }}</text>
        </view>
        <view class="device-bind-state flexRowAllCenter">
          <view class="bind w_100">
            <block v-if="info.dianweiid > 0">
              <image class="bind_icon" src="@/pagesB/static/img/icon/isBind_icon.png" />
              <view class="theme-color">已绑定</view>
            </block>
            <block v-else>
              <image class="bind_icon" src="@/pagesB/static/img/icon/noBind_icon.png" />
              <view class="text-warn">未绑定</view>
            </block>
          </view>

          <!--已绑定点位-->
          <block v-if="info.dianweiid > 0">
            <!--蓝牙设备-->
            <block v-if="info.isBlue">
              <view class="online devece-state">
                <block v-if="info.isRechargeDevice">
                  <image class="online_icon" src="@/pagesB/static/img/icon/device_online_icon.png" />
                  <view class="theme-color">
                    {{ info.isUse ? '正在充电' : '等待使用' }}
                  </view>
                </block>
              </view>
            </block>
            <block v-else>
              <view class="online devece-state w_100">
                <block v-if="info.status == 1">
                  <image class="online_icon" src="@/pagesB/static/img/icon/device_online_icon.png" />
                  <view class="theme-color">
                    在线/{{
    info.isGameDevice && info.isUse ? '使用中' : '未使用'
  }}
                  </view>
                </block>
                <block v-else-if="info.status == 3">
                  <image class="online_icon" src="@/pagesB/static/img/icon/device_unline_icon.png" />
                  <view class="text-warn">离线</view>
                </block>
                <block v-else>
                  <image class="online_icon" src="@/pagesB/static/img/icon/device_unline_icon.png" />
                  <view class="text-warn">未知</view>
                </block>
              </view>
            </block>
          </block>
          <block v-else>
            <!--蓝牙设备-->
            <block v-if="!info.isBlue">
              <view class="online devece-state w_100">
                <block v-if="info.status == 1">
                  <image class="online_icon" src="@/pagesB/static/img/icon/device_online_icon.png" />
                  <view class="theme-color">
                    在线
                  </view>
                </block>
                <block v-else-if="info.status == 3">
                  <image class="online_icon" src="@/pagesB/static/img/icon/device_unline_icon.png" />
                  <view class="text-warn">离线</view>
                </block>
                <block v-else>
                  <image class="online_icon" src="@/pagesB/static/img/icon/device_unline_icon.png" />
                  <view class="text-warn">未知</view>
                </block>
              </view>
            </block>
          </block>
        </view>
      </view>
      <view class="point_name">
        <view class="device-code point_item">
          <view class="device-code-item point_item">系统码：</view>
          <text user-select @click.stop="navigator()">
            {{ info.vCode != 0 && info.vCode ? info.vCode : '未绑定' }}
          </text>
          <veiw v-if="info.vCode_img" class="code-image">
            <image class="image" @click.stop="codeImage" :src="info.vCode_img" />
          </veiw>
        </view>
      </view>

      <view class="point_name">
        <view class="device-code point_item">
          <view class="device-code-item point_item">{{ vPointName }}：</view>
          <view class="textMaxTwoLine device-code-item point_item">
            <text @click.stop="navigator(info)">
              {{ info.hotelName ? info.hotelName : ''
              }}{{ info.room_num ? '(' + info.room_num + ')' : '' }}
            </text>
          </view>
        </view>
      </view>
      <view class="point_name">
        <view class="device-code point_item">
          <view class="device-code-item point_item">拥有者：</view>
          <view class="device-code-item textMaxTwoLine point_item">
            <text user-select @click.stop="navigator()">
              {{ info.user.user_login
              }}{{
    info.user.user_nickname
      ? ' ( ' + info.user.user_nickname + ' )'
      : ''
  }}
            </text>
          </view>
        </view>
      </view>
      <view class="point_name" v-if="info.deviceTypeName">
        <view class="device-code point_item">
          <view class="device-code-item point_item">设备类型：</view>
          <view class="device-code-item textMaxTwoLine point_item">{{ info.deviceTypeName }}
            <span class="card_click_gps" v-if="info.is_show_gps" @click.stop="goLocation">{{ '一键找回' }}</span>
          </view>
        </view>
      </view>
      <view class="point_name">
        <view class="device-code point_item">
          <view class="device-code-item point_item">设备状态：</view>
          <view class="device-code-item point_item" :style="{ color: info.use_status == 1 ? '#0EADE2' : 'red' }">
            {{ info.use_status == 1 ? '启用' : '禁用'
            }}{{
    info.use_status == 1
      ? ''
      : `（原因：${info.reason ? info.reason : '无'}）`
  }}
          </view>
        </view>
      </view>
      <view class="point_name">
        <view class="device-code point_item">
          <view class="device-code-item point_item">最近成交：</view>
          <view class="device-code-item point_item">
            {{
      info.lastest_order_time_str
        ? info.lastest_order_time_str
        : '大于30天'
    }}
          </view>
        </view>
      </view>
      <view class="point_name" style="justify-content: flex-start;">
        <view class="device-code point_item">
          <view class="device-code-item point_item">
            <text>
              {{
      info.isGameDevice
        ? '游戏规则：'
        : info.isRechargeDevice
          ? '充电规则：'
          : '库存总数：'
    }}
            </text>
          </view>
          <view class="device-code-item point_item">
            <text>{{ shopStatus() }}</text>
          </view>
        </view>
      </view>
      <block v-if="info.isShowLEStauts">
        <view class="point_name">
          <view class="isShowLEStauts point_item" v-html="isShowLEStauts_str()" style="white-space: pre-wrap;">
          </view>
        </view>
        <view class="updata">
          <BaseUpdata title="状态" :time="info.report_time" @onClick="updataClick" :refresh="manyUpdata" iosSize="35">
          </BaseUpdata>
        </view>
      </block>
    </view>

    <view class="card_btn flexRowBetween">
      <block v-if="goShow">
        <view class="card_btn_go" @click.stop="goStart">
          {{ info.isGameDevice && info.isUse ? '使用中' : '启动' }}
        </view>
      </block>
      <block v-else-if="vUserInfo.role_id != 6">
        <view class="card_btn_box" @click.stop="onUnbind" v-if="vButtonPermissions && vButtonPermisAides">
          {{ info.dianweiid > 0 ? '解除绑定' : '绑定' + vPointName }}
        </view>
        <view v-if="!info.isRechargeDevice && vButtonPermissions && vButtonPermisAides
    " class="card_btn_box" @click.stop="deviceGoods">
          {{ info.isGameDevice ? '设置' + vCargoLanes : '绑定商品' }}
        </view>
        <view v-if="!info.isRechargeDevice && vButtonPermissions && vButtonPermisAides
    " class="card_btn_box" @click.stop="deviceAd">
          营销设置
        </view>
        <view class="card_btn_box" v-if="info.isGameDevice" @click.stop="remote">
          遥控设备
        </view>
        <view class="card_btn_box" @click.stop="onOff">
          {{ info.use_status == 1 ? '禁用设备' : '启用设备' }}
        </view>
        <view v-if="!info.isGameDevice && vButtonPermissions" class="card_btn_box" @click.stop="repl">
          操作设备
        </view>
        <view v-if="vIsCharge && vButtonPermissions" class="card_btn_box" @click.stop="onCard">
          充电规则
        </view>
        <view v-if="false" class="card_btn_box" @click.stop="autoOnOff">
          {{ info.is_auto_on_off ? "关闭自动待机" : "开启自动待机" }}
        </view>
        <view v-if="info.isGameDevice && vButtonPermissions" class="card_btn_box" @click.stop="other">
          其他操作
        </view>
      </block>
      <block v-else>
        <view class="card_btn" v-if="nfo.isGameDevice">
          <view class="card_btn_length" @click.stop="onStartDevice(2, 1)">
            开启泡泡+音乐60秒
          </view>
          <view class="card_btn_box" @click.stop="onStartMusic(1)">
            开启音乐60秒
          </view>

          <view class="card_btn_box" @click.stop="onStartDevice(0, 0)">
            停止
          </view>
        </view>
      </block>
      <view style="width: 30%;" v-for="item in 2" :key="item"></view>
    </view>
  </view>
</template>

<script>
import BaseButton from '@/components/base/BaseButton.vue'
import BasePopup from '@/components/base/BasePopup.vue'
import TypeSelectPopup from '@/components/common/TypeSelectPopup.vue'
import BaseUpdata from '@/components/base/BaseUpdata.vue'

export default {
  components: { BaseButton, BasePopup, TypeSelectPopup, BaseUpdata },
  name: 'DeviceListCard',
  props: {
    info: {
      type: Object,
    },
    goShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      manyUpdata: false,
      // isShowLEStauts_str:"灯光:<span style=\"color: #0EADE2;\">开启</span>&nbsp;&nbsp;&nbsp;&nbsp;广告语:未知 &nbsp;&nbsp;&nbsp;&nbsp;水:<span style=\"color: red;\">无</span>&nbsp;&nbsp;&nbsp;&nbsp;电量:12998&nbsp;&nbsp;&nbsp;&nbsp;信号:20&nbsp;&nbsp;&nbsp;&nbsp;"
    }
  },
  methods: {
    //图片展示  
    codeImage() {
      console.log('图片展示')

      this.$emit('codeImage')

    },
    goLocation() {
      uni.navigateTo({
        url:
          `/pagesC/mapMode/GoMapLoction?from=device&item=${encodeURIComponent(JSON.stringify(this.info))}`
      });
    },
    //跳转
    navigator(item) {
      console.log('跳转信息', item)
      if (item && item.dianweiid && item.hotel_id)
        uni.navigateTo({
          url: `/pagesB/device/DeviceList?from=place&dianwei=${item.dianweiid}&hotel_id=${item.hotel_id}`,
        })
    },
    //计算属性
    shopStatus() {
      // 状态
      if (this.info.shopstatus) {
        return this.info.shopstatus.replace(/\\n/g, '\n')
      } else {
        return '未设置'
      }
    },
    isShowLEStauts_str() {
      // 状态
      if (this.info.isShowLEStauts_str) {
        let str = this.info.isShowLEStauts_str.replace(/\\n/g, '\n')
        return str
      } else {
        return ""
      }
    },

    //
    other() {
      // this.showTaskType = true
      this.$emit('others')
    },
    onUnbind() {
      this.$emit('onUnbind')
    },
    deviceGoods() {
      this.$emit('deviceGoods')
    },
    autoOnOff() {
      this.$emit('autoOnOff')
    },
    onOff() {
      this.$emit('onOff')
    },
    repl() {
      this.$emit('repl')
    },
    remote() {
      this.$emit('remote')
    },
    goStart() {
      if (!this.info.isUse) {
        this.$emit('goStart')
      } else {
        this.isShowErr('正在使用')
      }
    },
    onCard() {
      if (this.vUserInfo.role_id == 6) {
        return
      }
      this.$emit('onCard')
    },
    deviceAd() {
      this.isShowErr('未开放')
      // uni.navigateTo({
      //   url: "/pagesB/device/DeviceAd?device_sn=" + this.info.device_sn,
      // });
    },
    updataClick() {
      this.$emit('updataTime')
    },
    async onStartDevice(channel, time) {
      try {
        let data = {
          device_sn: this.info.device_sn, // 设备编号
          channel: channel, // 命令
          length_time: time, // 分钟
        }
        await this.$u.api.startUM(data)
        this.isShowSuccess('操作成功')

      } catch (error) {
        console.log('错误信息', error)
      }

    },
    async onStartMusic(time) {
      try {
        let data = {
          device_sn: this.info.device_sn,
          length_time: time,
        }
        await this.$u.api.startMusic(data)
        this.isShowSuccess('操作成功')
      } catch (error) {
        console.log('错误信息', error)
      }

    },
  },
}
</script>

<style lang="scss" scoped>
.w_100 {
  width: 100rpx;
}

.code-image {
  margin-left: 20rpx;
  width: 40rpx;
  height: 40rpx;

  .image {
    width: 100%;
    height: 100%;
  }
}

.card {
  &_top {
    padding-bottom: 20rpx;
  }

  .device-code-state,
  .point_name {
    margin-bottom: 10rpx;

    .devece-state {
      margin-left: 20rpx;
    }
  }

  .device-code {
    .device-code-item:nth-child(1) {
      width: 160rpx;
      text-align: justify;
      height: 40rpx;
      /* 固定高度 */
      line-height: 40rpx;
      /* 与高度相同 */
      overflow: hidden;
      /* 防止溢出 */

      /* 优化后的伪元素方案 */
      &::after {
        content: '';
        display: inline-block;
        width: 100%;
        height: 0;
        /* 消除高度影响 */
        vertical-align: top;
        /* 对齐顶部 */
      }

      // border: 1px solid #000;
    }

    .device-code-item:nth-child(2) {
      width: 480rpx;

    }

    .card_click_gps {
      text-decoration: underline;
      color: #0EADE2;
      margin-left: 10rpx;
    }
  }

  .isShowLEStauts {
    width: 100%;
    font-size: 22rpx;
  }

  .device_item {
    // border: 1px solid #000;
    display: flex;
    margin-right: 30rpx;
    font-size: 25rpx;
    // width: 12rpx;
    // border: 1px solid #000;
  }

  .device-code,
  .point_name {
    display: flex;
    flex-direction: row;
    font-size: $font-size-base;

    .point_item {
      &:first-child {
        color: $textDarkGray;
        // width: 120rpx;
      }

      &:last-child {

        color: $textBlack;
      }
    }
  }
}

.card_btn {
  padding: 0rpx;
  flex-wrap: wrap;
  justify-content: space-between;

  align-items: center;

  .card_btn_go {
    width: 200rpx;
    padding: 20rpx 0;
    border: 2rpx solid rgb(212, 212, 212);
    margin: 10rpx;
    display: flex;
    border-radius: 50rpx;
    justify-content: center;
  }

  .card_btn_box {
    box-sizing: border-box;
    width: 190rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    // width: 30%;
    border-radius: 50rpx;
    margin: 10rpx;
    padding: 20rpx 0rpx;
    border: 2rpx solid rgb(212, 212, 212);
    // &:nth-child(n + 4) {
    //   margin-top: 20rpx;
    // }
  }

  .card_btn_length {
    border-radius: 50rpx;
    border: 2rpx solid rgb(212, 212, 212);
    padding: 20rpx 0rpx;
    margin: 10rpx 0;
    margin-left: 10rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
