/**
 * BLE通信工具库 - RF协议到BLE协议转换核心
 *
 * 功能：实现XN297L RF协议与BLE协议之间的双向转换
 * 主要用途：让微信小程序能够与XN297L RF设备进行通信
 *
 * 核心转换流程：
 * 1. 用户数据 → RF格式封装 → XN297L处理 → BLE格式 → 无线发送
 * 2. 无线接收 → BLE解析 → XN297L解码 → RF数据 → 用户显示
 *
 * 作者：RF通信团队
 * 更新：2024年
 */

// 导入依赖模块
import { whitening_init, whitenging_encode } from './whitening'  // 数据白化处理
import { invert_8, invert_16, check_crc16 } from './crc16'       // CRC校验和位反转
import { str2Bytes, byteToString, strToHexCharCode } from './util' // 数据格式转换工具

// 默认BLE信道索引（注释掉的备用配置）
// let BLE_CHANNEL_INDEX = 37

/**
 * RF到BLE数据包转换的核心函数
 * 这是整个通信系统的心脏，负责将RF数据转换为BLE可发送的格式
 *
 * @param {Array} address - RF设备地址数组，如 [0xAA, 0xBB, 0xCC, 0xDD, 0xEE]
 * @param {number} address_length - 地址长度（字节数），通常为3-5字节
 * @param {Array} rf_payload - RF载荷数据数组，如 [0x01, 0x02, 0x03, 0x04]
 * @param {number} rf_payload_width - 载荷长度（字节数）
 * @param {Array} output_ble_payload - 输出缓冲区（未使用）
 * @returns {Array} 处理完成的BLE数据包，可直接用于BLE广播
 */
const get_1rf_1payload = (address, address_length, rf_payload, rf_payload_width, output_ble_payload) => {
	// === 初始化参数 ===
	const base_size = 15  // BLE数据包基础大小（固定15字节前缀）
	const channel = wx.getStorageSync('channel') || '37'  // 获取BLE信道，默认37

	// 初始化两套白化寄存器（7位线性反馈移位寄存器）
	const whitening_reg_ble = new Array(7)   // BLE协议白化寄存器
	whitening_reg_ble[0] = 0
	const whitening_reg_297 = new Array(7)   // XN297L协议白化寄存器
	whitening_reg_297[0] = 0

	// 初始化白化寄存器的种子值
	whitening_init(channel, whitening_reg_ble);  // BLE白化：使用信道作为种子
	whitening_init(0x3F, whitening_reg_297);     // XN297L白化：使用固定种子0x3F

	// 创建BLE数据包缓冲区
	// 总长度 = 基础大小 + 前导码(3) + 地址长度 + 载荷长度 + CRC(2)
	let ble_payload = new Array(base_size + 3 + address_length + rf_payload_width + 2);

	// === Step1: 组装基础数据包 ===
	// 添加XN297L协议的固定前导码
	ble_payload[base_size + 0] = 0x71;  // 前导码字节1
	ble_payload[base_size + 1] = 0x0F;  // 前导码字节2
	ble_payload[base_size + 2] = 0x55;  // 前导码字节3

	// 复制RF设备地址（注意：需要反序排列）
	// 例如：地址 [AA, BB, CC, DD, EE] → [EE, DD, CC, BB, AA]
	for (let i = 0; i < address_length; i++) {
		ble_payload[base_size + 3 + i] = address[address_length - i - 1];
	}

	// 复制RF载荷数据（保持原序）
	for (let i = 0; i < rf_payload_width; i++) {
		ble_payload[base_size + 3 + address_length + i] = rf_payload[i];
	}

	// === Step2: XN297L位反转处理 ===
	// 对前导码和地址部分进行8位反转（XN297L协议要求）
	for (let i = 0; i < 3 + address_length; i++) {
		ble_payload[base_size + i] = invert_8(ble_payload[base_size + i]);
	}

	// === Step3: 添加CRC16校验码 ===
	// 计算地址和载荷的CRC16校验值
	let crc = check_crc16(address, address_length, rf_payload, rf_payload_width);

	// 将16位CRC分解为两个8位字节并添加到数据包末尾
	ble_payload[base_size + 3 + address_length + rf_payload_width + 0] = crc & 0xFF;        // CRC低字节
	ble_payload[base_size + 3 + address_length + rf_payload_width + 1] = (crc >> 8) & 0xFF; // CRC高字节

	// === Step4: XN297L协议白化 ===
	// 对地址、载荷和CRC部分进行XN297L白化处理
	const wData = whitenging_encode(ble_payload.slice(base_size + 3),
		address_length + rf_payload_width + 2, whitening_reg_297);
	// 将白化后的数据写回原位置
	for (let i = 0; i < wData.length; i++) {
		ble_payload[base_size + 3 + i] = wData[i]
	}

	// === Step5: BLE协议白化 ===
	// 对整个数据包进行BLE协议白化处理
	whitenging_encode(ble_payload, base_size + 3 + address_length + rf_payload_width + 2, whitening_reg_ble);

	// === 提取最终数据包 ===
	// 从完整缓冲区中提取实际需要的数据部分
	const act_payload = new Array(3 + address_length + rf_payload_width + 2)
	for (let i = 0; i < 3 + address_length + rf_payload_width + 2; ++i) {
		act_payload[i] = ble_payload[i + base_size];
	}

	return act_payload  // 返回可用于BLE广播的最终数据包
}

/**
 * 生成iOS平台的Service UUIDs
 * iOS平台通过Service UUIDs来传输数据，而不是Manufacturer Data
 *
 * @param {Array} actPayload - 处理后的BLE数据包
 * @param {boolean} isIos - 是否为iOS系统（参数未使用，保留兼容性）
 * @returns {Array} Service UUIDs数组，用于iOS平台的BLE广播
 */
const getServiceUUIDs = (actPayload, isIos) => {
	// 将字节数组转换为空格分隔的十六进制字符串
	let payload = byteToString(actPayload)
	// 备用代码：根据iOS版本选择不同策略
	// const uuids = isiOS13?[]:['00c7']

	// 调用具体的UUID生成函数
	return getServiceUUIDsBySpace(payload)
}

/**
 * 通过空格分隔的载荷数据生成Service UUIDs
 * 将数据编码到多个短UUID中，用于iOS平台的数据传输
 *
 * @param {string} payload - 空格分隔的十六进制字符串，如 "01 02 03 04"
 * @returns {Array} 包含多个UUID的数组，每个UUID包含部分数据
 */
const getServiceUUIDsBySpace = (payload) => {
	// 初始化UUID数组，包含固定的标识UUID
	const uuids = ['00c7']  // 固定标识符，用于设备识别
	// const uuids = []  // 备用：空数组开始
	const uuidHexArr = []   // 十六进制数组（未使用，保留）

	console.log('payload', payload)

	if (payload) {
		// 将载荷按空格分割并过滤空项
		let payloadArray = payload.split(' ').filter(item => item && item.length > 0);

		// 确保数组长度为偶数（UUID需要成对的十六进制字符）
		if (payloadArray.length % 2 != 0) {
			payloadArray.push("00")  // 补充一个00字节
		}

		// 将相邻的两个十六进制字符组合成一个UUID
		for (let i = 0; i < payloadArray.length; i++) {
			if (!payloadArray[i]) {
				continue  // 跳过空项
			}
			// 每两个字符组合一次（奇数索引时执行）
			if (i % 2 != 0) {
				// 注意：这里是反序组合 payloadArray[i] + payloadArray[i-1]
				uuids.push(payloadArray[i] + payloadArray[i - 1])
			}
		}
	}

	// 填充UUID数组到17个（iOS BLE广播的UUID数量限制）
	for (let i = uuids.length; i < 17; i++) {
		// 生成填充UUID：格式为 "0102", "0304", "0506" 等
		const pre = i < 10 ? ("0" + i) : ("" + i)           // 前缀：00-16
		const suf = (i + 1) < 10 ? ("0" + (i + 1)) : ("" + (i + 1))  // 后缀：01-17
		uuids.push(pre + suf)
	}

	// console.log('uuids', uuids)
	return uuids  // 返回完整的UUID数组
}

/**
 * 生成新版本的Service UUIDs（标准UUID格式）
 * 将载荷数据编码为标准的128位UUID格式，用于特定的iOS版本
 *
 * @param {Array} actPayload - 处理后的BLE数据包
 * @returns {Array} 包含标准UUID格式的数组
 */
const getNewServiceUUIDs = (actPayload) => {
	// 将字节数组转换为字符串
	let payload = byteToString(actPayload)
	let uuid = "";

	if (payload) {
		// 将空格替换为逗号，便于分割
		payload = payload.replace(/\s+/g, ',')
		let payloadArray = payload.split(',').filter(item => item && item.length > 0);

		// 确保数组长度为偶数
		if (payloadArray.length % 2 != 0) {
			payloadArray.push("00")
		}

		// 调整数组长度为16字节（128位UUID需要）
		if (payloadArray.length < 16) {
			// 不足16字节时，用递增数字填充
			const countLeak = 16 - payloadArray.length
			for (let i = 0; i < countLeak; i++) {
				const leakItem = i < 10 ? ("0" + i) : ("" + i)
				payloadArray.push(leakItem)
			}
		} else if (payloadArray.length > 16) {
			// 超过16字节时，截取前16字节
			payloadArray = payloadArray.slice(0, 16)
		}

		// 反序拼接字符串（从最后一个元素开始）
		for (let i = payloadArray.length - 1; i >= 0; i--) {
			uuid += payloadArray[i]
		}
	}

	// 格式化为标准UUID格式：XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
	const actUUID = uuid.substr(0, 8) + "-" + uuid.substr(8, 4) + "-" + uuid.substr(12, 4) + "-" + uuid.substr(16, 4) + "-" + uuid.substr(20, 12);

	const uuids = [actUUID]

	// 备用代码：可以添加更多UUID
	// for(let i=0;i<3;i++){
	// 	const pre = i < 10 ?( "0" + i) : (""+ i)
	// 	const suf = (i + 1) < 10?("0"+( i + 1)) : ("" + (i + 1))
	// 	uuids.push(pre+suf)
	// }

	return uuids
}

/**
 * 为Android平台准备数据
 * 将十六进制字符串转换为字节数组，用于Android的Manufacturer Data
 *
 * @param {string} payload - 十六进制字符串，如 "01020304"
 * @returns {Array} 字节数组，如 [1, 2, 3, 4]
 */
const getAndroidData = (payload) => {
	const data = new Array()

	if (payload) {
		// 计算字节数（每2个字符代表1个字节）
		const length = payload.length / 2

		// 逐字节转换
		for (let i = 0; i < length; i++) {
			// 提取2个字符并转换为字节值
			const byte = str2Bytes(payload.substring(i * 2, (i + 1) * 2));
			data.push(byte)
		}
	}

	return data  // 返回字节数组
}
/**
 * 使用指定地址生成BLE数据包
 * 这是数据生成的核心函数，处理地址和载荷的转换
 *
 * @param {string} rawAddress - 原始地址字符串，如 "AABBCCDDEE"
 * @param {string} inputPayload - 用户输入的载荷，如 "01 02 03 04"
 * @param {boolean} isIos - 是否为iOS系统（参数传递给核心函数）
 * @returns {Array} 处理完成的BLE数据包
 */
const generateDataWithAddr = (rawAddress, inputPayload, isIos) => {
	// === 地址处理 ===
	// 将十六进制地址字符串转换为字节数组
	const address = Array(rawAddress.length / 2)
	for (let i = 0; i < address.length; ++i) {
		// 每2个字符转换为1个字节
		const add = str2Bytes(rawAddress.substring(i * 2, (i + 1) * 2));
		address[i] = add
	}

	// === 载荷处理 ===
	let rawPayload = inputPayload;
	rawPayload = rawPayload.replace(/\s+/g, '')  // 去除所有空格
	rawPayload = rawPayload.toLowerCase();       // 转换为小写

	// 验证载荷长度（至少1字节）
	if (rawPayload.length < 2) {
		wx.showToast({
			icon: 'none',
			title: 'The payload is at least 1 byte',
		})
		return;
	}

	// 验证载荷长度（必须为偶数，因为每2个字符代表1个字节）
	if (rawPayload.length % 2 != 0) {
		wx.showToast({
			icon: 'none',
			title: 'payload长度必须是偶数',
		})
		return;
	}

	// 将十六进制载荷字符串转换为字节数组
	const payload = new Array(rawPayload.length / 2)
	for (let i = 0; i < payload.length; ++i) {
		payload[i] = str2Bytes(rawPayload.substring(i * 2, (i + 1) * 2));
	}

	// === 调用核心转换函数 ===
	// 创建输出缓冲区（实际未使用）
	let calculatedPayload = new Array(address.length + payload.length + 5);

	// 调用核心转换函数，执行5步转换流程
	const actPayload = get_1rf_1payload(address, address.length, payload, payload.length, calculatedPayload, isIos);

	return actPayload  // 返回最终的BLE数据包
}

/**
 * 主要的数据生成函数（对外接口）
 * 从本地存储获取RF设备地址，然后生成BLE数据包
 *
 * @param {string} inputPayload - 用户输入的载荷数据
 * @param {boolean} isIos - 是否为iOS系统
 * @returns {Array} 处理完成的BLE数据包，可直接用于广播
 */
const generateData = (inputPayload, isIos) => {
	// === 获取RF设备地址 ===
	let rawAddress = wx.getStorageSync('address')  // 从本地存储读取
	rawAddress = rawAddress.replace(/\s+/g, '')    // 清理空格
	rawAddress = rawAddress.toLowerCase();         // 转换为小写

	console.log('rawAddress', rawAddress)

	// === 验证地址格式 ===
	// 地址长度必须在6-10个字符之间（3-5字节）
	if (rawAddress.length < 6 || rawAddress.length > 10) {
		wx.showToast({
			icon: 'none',
			title: '地址不可为空',
		})
		return;
	}

	// === 调用具体实现函数 ===
	return generateDataWithAddr(rawAddress, inputPayload, isIos);
}


/**
 * 模块导出
 * 提供RF到BLE转换的完整功能集
 */
module.exports = {
	// === 核心转换函数 ===
	get_1rf_1payload: get_1rf_1payload,    // RF到BLE的5步转换核心

	// === 主要接口函数 ===
	generateData,                          // 主要数据生成接口（从存储读取地址）
	generateDataWithAddr,                  // 使用指定地址生成数据

	// === iOS平台支持 ===
	getServiceUUIDs,                       // 生成iOS Service UUIDs（多UUID方式）
	getNewServiceUUIDs,                    // 生成iOS Service UUIDs（标准UUID方式）
	getServiceUUIDsBySpace,                // 通过空格分隔生成UUIDs

	// === Android平台支持 ===
	getAndroidData,                        // 为Android准备Manufacturer Data
}

/**
 * 文件总结：
 *
 * 这个文件是RF通信系统的核心转换引擎，主要功能包括：
 *
 * 🔄 **核心转换流程**：
 * 1. Step1: 组装基础数据（前导码 + 地址 + 载荷）
 * 2. Step2: XN297L位反转处理
 * 3. Step3: 添加CRC16校验码
 * 4. Step4: XN297L协议白化
 * 5. Step5: BLE协议白化
 *
 * 📱 **平台适配**：
 * - iOS: 使用Service UUIDs传输数据
 * - Android: 使用Manufacturer Data传输数据
 *
 * 🎯 **主要用途**：
 * - 让微信小程序能够与XN297L RF设备通信
 * - 实现RF协议到BLE协议的无缝转换
 * - 支持双向数据传输（发送和接收）
 *
 * 📊 **数据流向**：
 * 用户输入 → 格式验证 → 地址处理 → 载荷转换 → 5步协议转换 → 平台适配 → BLE广播
 *
 * 🔧 **技术特点**：
 * - 双重白化处理（XN297L + BLE）
 * - CRC16数据完整性校验
 * - 位反转兼容XN297L协议
 * - 支持iOS和Android不同的BLE实现方式
 */
