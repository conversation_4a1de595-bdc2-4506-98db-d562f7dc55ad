<template>
  <view>
    <BaseNavbar :title="title" />
    <view class="content">
      <view v-if="isBindDevice">
        <view class="title">
          <view>设备编号</view>
        </view>
        <BaseInput v-model="device_sn" :disabled="true" />
      </view>
      <view>
        <view class="title">
          <view>{{ vPointName }}名称</view>
          <view class="necessary">*</view>
        </view>
        <view class="map-box">
          <BaseInput :placeholder="'请选择' + vPointName" v-model="choosedAddressInfoTwo.name" />
          <view class="map-box-title" @click.stop="getSelectLocationTwo">
            <BaseIcon name="map" color="#206cc5" />
            <view class="map-box-title-txt">地图搜索</view>
          </view>
        </view>
      </view>
      <view>
        <view class="title">
          <view>省市区</view>
          <view class="necessary">*</view>
        </view>
        <AreaSelect :defaultRegion="defaultRegion" v-model="selectArea" placeholder="请选择省市区" />
      </view>
      <view>
        <view class="title">
          <view>详细位置</view>
          <view class="necessary">*</view>
        </view>
        <view class="map-box">
          <BaseInput placeholder="请输入详细位置" v-model="choosedAddressInfo.address" />
          <view class="map-box-title" @click.stop="getSelectLocation">
            <BaseIcon name="map" color="#206cc5" />
            <view class="map-box-title-txt">地图搜索</view>
          </view>
        </view>
      </view>
      <view v-if="false">
        <view class="title"> 竞对情况 </view>
        <BaseInput @onClick="showCompetition = true" :disabled="true" placeholder="选择竞对情况(多选)" v-model="compete" />
      </view>
      <view>
        <view class="title">
          <view>{{ vPointName }}备注</view>
          <!-- <view class="necessary">*</view> -->
        </view>
        <BaseInput placeholder="请输入备注内容,最多30个字" v-model="remarks" maxlength="30" />
      </view>

      <view>
        <view class="title">
          <view>{{ vPointName }}类型</view>
          <view class="necessary">*</view>
        </view>
        <BaseInput @onClick="showShopType = true" :disabled="true" placeholder="请选择商铺类型" v-model="shopType" />
      </view>
      <!-- <view>
        <view class="title">
          <view>默认{{ vCargoLanes }}</view>
          <view class="necessary">*</view>
        </view>
        <BaseInput :placeholder="`请输入${vCargoLanes}数量`" v-model="channelNumber" />
      </view> -->


      <view>
        <view class="title">
          <view>联系人姓名</view>
          <!-- <view class="necessary">*</view> -->
        </view>
        <BaseInput placeholder="请输入联系人姓名" v-model="linkman" />
      </view>
      <view>

        <view class="title">
          <view>联系人电话</view>
          <view class="necessary">*</view>
        </view>
        <view class="vpointName">
          <BaseInput placeholder="请输入联系人电话" type="number" v-model="linkmanPhone" />
        </view>

      </view>
      <view>
        <view class="title">
          <view v-if="isFromEdit">绑定的{{ vPointName }}账号</view>
          <view v-else>{{ vPointName }}账号</view>
          <view class="necessary" v-if="isFromEdit">*</view>
        </view>
        <view class="ridio">
          <BaseRadio :radioIndex.sync="radioIndex" :size="22" :list="radioIndexList" :width="100" />
        </view>
        <view class="vpointName" v-if="radioIndex == 1">
          <view class="vpointName_zhe" @click="selectHotel">
          </view>
          <BaseInput placeholder="请选择绑定账号" :disabled="true" type="number" v-model="linkmanDianwei" />
          <view class="click">
            <u-icon name="arrow-right" color="#2979ff" size="45"></u-icon>
          </view>
        </view>
      </view>
      <view>
        <view>
          <view class="title">合作模式</view>
          <BaseInput :selectValue="roleValue.label" :index="roleValue.value" placeholder="请选择租赁模式" type="select"
            :list="roleList" @confirm="roleConfirm" />
        </view>
      </view>
      <view v-if="divide_type == 2">
        <view class="title">设置分成比例：</view>
        <BaseInput type="number" placeholder="请输入分成比例" @onInput="validateInput" v-model="divide_per" rightText="%" />
        <p v-if="errorMessage" style="color: red;">{{ errorMessage }}</p>
      </view>
      <view class="title">
        <view class="row-flex-item-title">租赁价格：</view>
        <BaseInput placeholder="请输入租赁价格" v-model="rent" /><text class="fontSize">元/</text>
        <view class="select ">

          <view class="vpointName fontSize">


            <view class="fontSize">{{ options[tabValue].label }}</view>
            <view @click="tabShow = true" class="click">
              <u-icon :name="!tabShow ? 'arrow-down' : 'arrow-up'" color="#333f" size="30"></u-icon>
            </view>
          </view>

          <view class="selectShow" v-if="tabShow">
            <view v-for="(item, i) in options" :key="i" @click="changeBtn(i)">{{ item.label }}</view>
          </view>
        </view>
        <view class="zhe" v-if="tabShow" @click="tabShow = false">

        </view>
        <!-- <view class="necessary">*</view> -->
      </view>

      <view class="title">

        <view class="row-flex-item-title">租赁周期：</view>

        <view class="time">

          <TimeSelect v-model="starManytTime" times placeholder="请选择开始时间" :defaultTime="starManytTime" />
          <TimeSelect class="end-time" times v-model="endManyTime" placeholder="请选择结束时间" :defaultTime="endManyTime" />
        </view>

        <!-- <view class="necessary">*</view> -->
      </view>
      <view>
        <view class="title">
          <view>营业时间 </view>
          <view class="title_right">
            <BaseIconModal size="30" name="question-circle" v-if="prompt">
              {{ prompt }}
            </BaseIconModal>
          </view>
          <view class="necessary"> * </view>
          <view>

            系统灯控默认时间:
            {{ startTime2 }}-{{ endTime2 }}
          </view>
        </view>
        <view class="time">
          <TimeSelectHour v-model="startTime" placeholder="请选择开始时间" :defaultTime="startTime" />
          <TimeSelectHour class="end-time" v-model="endTime" placeholder="请选择结束时间" :defaultTime="endTime" />
        </view>
      </view>
      <view v-if="false">
        <view class="title">

          系统灯控默认时间:
          {{ startTime2 }}-{{ endTime2 }}
        </view>
        <view class="title">

          是否修改默认时间：
          <view class="">
            <u-radio-group v-model="value" @change="radioGroupChange">
              <u-radio @change="radioChange" v-for="(item, index) in list" :key="index" :name="item.name"
                :disabled="item.disabled">
                {{ item.name }}
              </u-radio>
            </u-radio-group>
          </view>
          <!-- <view class="necessary">*</view> -->
        </view>
        <view class="time" v-if="times">
          <TimeSelectHour v-model="startTime2" placeholder="请选择开始时间" :defaultTime="startTime2" />
          <TimeSelectHour class="end-time" v-model="endTime2" placeholder="请选择结束时间" :defaultTime="endTime2" />
        </view>
      </view>
      <view v-if="false">
        <view class="title">{{ vPointName }}面积</view>
        <view class="volume">
          <BaseInput :placeholder="'商铺' + vPointName + '面积'" type="number" v-model="shopArea" />
          <view class="volume-mark">㎡</view>
        </view>
      </view>
      <view v-if="false">
        <view class="title">{{ vPointName }}图片</view>
        <view>
          <BaseUpload width="670" height="300" :auto="true" maxCount="1" :fileList="fileList" uploadText="点击拍摄/上传图片"
            @onUpload="onUpload" />
        </view>
      </view>
      <view v-if="false">
        <view class="title">
          <view>营业执照</view>
          <view class="necessary">*</view>

        </view>
        <view>
          <BaseUpload width="670" height="300" :auto="true" maxCount="1" :fileList="defaultFileListLicense"
            uploadText="点击拍摄/上传图片" @onUpload="onUploadLicense" />
        </view>
      </view>
      <view>

        <view class="title">
          <view>售后服务热线</view>
          <view class="title_right">
            <BaseIconModal size="30" name="question-circle" v-if="PhonePrompt">
              {{ PhonePrompt }}
            </BaseIconModal>
          </view>
        </view>
        <view class="vpointName">
          <BaseInput placeholder="请输入售后服务热线" type="number" v-model="service_phone" />
        </view>

      </view>
      <view class="btn">
        <view>
          <BaseButton type="primary" @onClick="confirm">{{
      btnTitle
    }}</BaseButton>

        </view>

        <view class="examine" v-if="false">
          <BaseButton type="default" @onClick="examineHandler(license_status === 0 ? 1 : 0)">{{
      license_status === 0 ? "审 核 通 过" : "审 核 下 架"
    }}</BaseButton>
        </view>
      </view>
      <view class="btn_del" v-if="false">
        <BaseButton type="default" @onClick="confirmDelet">删 除</BaseButton>
      </view>
      <!--popup 竞对情况-->
      <BasePopup :show.sync="showCompetition" :safeArea="true">
        <TypeSelectPopup :isShowOther="true" title="选择竟对" @comfirm="comfirmCompete" :isOpt="true" :list="competeList" />
      </BasePopup>
      <!--popup 商铺类型-->
      <BasePopup :show.sync="showShopType" :safeArea="true">
        <TypeSelectPopup title="选择商铺类型" :list="shopTypeList" @comfirm="comfirmShopType" />
      </BasePopup>
    </view>
  </view>
</template>

<script>
import BaseButton from "@/components/base/BaseButton.vue";
import BaseInput from "@/components/base/BaseInput.vue";
import BaseNavbar from "@/components/base/BaseNavbar.vue";
import BaseUpload from "@/components/base/BaseUpload.vue";
import AreaSelect from "@/components/common/AreaSelect.vue";
import TimeSelectHour from "@/components/common/TimeSelectHour.vue";
import TimeSelect from "@/components/common/TimeSelect.vue";
import BasePopup from "@/components/base/BasePopup.vue";
import BaseIconModal from "@/components/base/BaseIconModal.vue";
import TypeSelectPopup from "@/components/common/TypeSelectPopup.vue";
import { locationMixin } from "@/mixins/locationMixin";
import BaseIcon from "../../components/base/BaseIcon.vue";
import { timestampToTime } from "@/common/tools/utils.js";
import BaseDropdown from "@/components/base/BaseDropdown.vue";
import BaseRadio from "@/components/base/BaseRadio"
export default {
  components: {
    BaseNavbar,
    BaseInput,
    BaseUpload,
    BaseButton,
    AreaSelect,
    TimeSelectHour,
    BasePopup,
    TypeSelectPopup,
    BaseIcon,
    TimeSelect,
    BaseIconModal,
    BaseDropdown,
    BaseRadio
  },
  mixins: [locationMixin],

  data() {
    return {
      fromData: "",
      isFromEdit: false,
      model: true,
      linkmanPhone: "", //点位联系电话
      service_phone:'',//售后热线电话
      linkmanDianwei: '',//账号
      linkman: "", //点位联系人
      dianwei: "",//场地方id
      selectArea: "", //选择得区域
      remarks: "", //商铺备注
      uploadList: [], //上传图片得list
      startTime: "09:00", //营业开始时间
      endTime: "22:00", //营业结束时间
      startTime2: "09:00", //营业开始时间
      endTime2: "22:00", //营业结束时间
      starManytTime: "",//租赁周期开启时间
      endManyTime: "",//组件结束周期
      showCompetition: false, //竞对情况popup
      compete: "", //选择的竞对内容
      showShopType: false, //商铺类型 popup
      shopType: "", //选择的商铺类型
      shopArea: "", //商铺面积
      shopTypeList: [], //过滤后 传入的商铺类型名称
      selectShopTypeId: "", //选择的商铺类型 id
      fileList: [], //默认显示图片
      fileListLicense: [], //营业执照
      defaultFileListLicense: [], //默认营业执照图片
      defaultRegion: [], //默认区域
      competeList: [], //竞对数组
      channelNumber: "5", //套餐数量
      wifi_name: "", //wifi账号
      wifi_psd: "", //wifi密码
      btnTitle: "保 存",
      device_sn: "",
      mid: "",
      isBindDevice: false, //是否显示设备编号
      id: "", //编辑需要的场地方id
      license_status: 0, //场地方是否审核通过 审核状态 ： 1 通过 0：未通过
      hotelName: null,
      data: false,
      times: false,
      rent: '',
      prompt: "",
      PhonePrompt: '填写服务热线，会在用户扫码页面显示服务热线',
      title: `新增${this.vPointName}`,
      errorMessage: '',

      list: [
        {
          name: '否',
          disabled: false
        },
        {
          name: '是',
          disabled: false
        }
      ],
      years: {
        year: true,
        month: true,
        day: true,
        hour: true,
        minute: true,
        second: false,
      },
      // u-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
      value: '否',
      roleValue: {
        label: "随账号分成", value: 0, status: 0
      },
      roleList: [],
      tabShow: false,
      tabValue: 0,
      options: [
        { label: "月", value: 0, status: 0 },

        {
          label: "年",
          value: 1,
          status: 1,
        },
      ],
      divide_type: 0,
      divide_per: 0,
      radioIndex: 0,
      radioIndexList: []

    };

  },
  watch: {
    divide_per: {
      handler(newValue, oldValue) {
        this.validateInput(newValue);
      },
      immediate: true // 这样会在组件初始化时立即进行一次验证
    }
  },
  methods: {
    confirmDelet() { },
    validateInput() {
      // 首先清空错误信息
      this.errorMessage = '';

      // 判断输入是否为整数
      const value = parseInt(this.divide_per);
      if (isNaN(value) || !Number.isInteger(value)) {
        this.errorMessage = '请输入有效的整数。';
      } else {
        // 判断输入是否在1-100之间
        if (value < 0 || value > 100) {
          this.errorMessage = '请输入1-100之间的整数。';
        }
      }
    },
    roleConfirm(e) {
      this.roleValue = this.roleList[e[0].value];
      this.divide_type = e[0].value
    },
    handleClickOutside() {
    },
    changeBtn(i) {
      // this.optionsList = item;
      // this.tiemVale = item[0].options[item[0].value].label;
      this.tabValue = i
      this.tabShow = false
    },
    // 选中某个单选框时，由radio时触发
    radioChange(e) {
      if (e == '是') {
        this.startTime2 = "09:00" //营业开始时间
        this.endTime2 = "22:00"
        this.times = true
      } else {
        this.startTime2 = "09:00" //营业开始时间
        this.endTime2 = "22:00"
        this.times = false
      }
    },
    radioManyChange(e) {
    },
    // 选中任一radio时，由radio-group触发
    radioGroupChange(e) {
      // console.log(e);
    },
    onUpload(item) {
      this.uploadList = item;
    },
    onUploadLicense(item) {
      this.defaultFileListLicense = [];
      this.fileListLicense = item;
    },
    selectHotel() {
      uni.navigateTo({
        url: `/pagesB/place/SelectPlace?from=home_placeAdd`,
      });
    },
    confirm() {
      if (!this.choosedAddressInfoTwo.name)
        return this.isShowErr(`请选择${this.vPointName}位置~`);
      if (
        !this.choosedAddressInfo.address
      )
        return this.isShowErr(`请选择详细位置~`);
      let areaList = this.selectArea.split("-");
      if (
        areaList?.length !== 3 ||
        !areaList?.[0] ||
        !areaList?.[1] ||
        !areaList?.[2]
      )
        return this.isShowErr("请选择省市区~");
      // if (!this.remarks) return this.isShowErr("请填写备注~");
      if (!this.selectShopTypeId) return this.isShowErr(`请选择${this.vPointName}类型~`);
      if (!this.startTime || !this.endTime)
        return this.isShowErr("请选择营业时间~");
      // if (!this.linkman) return this.isShowErr("请填写联系人~");
      if (!this.$u.test.mobile(this.linkmanPhone))
        return this.isShowErr("请填写正确电话号码~");
      const regex = /^[0-9]\d?$|^100$/;
      if (!regex.test(this.divide_per)) {
        this.errorMessage = '请输入0-100之间的正整数。';
        return this.isShowErr("分成比例需在0-100之间的正整数~");
      }

      // 账号验证
      if (this.isFromEdit) {
        if (!this.linkmanDianwei) {
          return this.isShowErr("账号不能为空");
        }
      }
      // if (this.startTime2 < this.startTime) return this.isShowErr("灯光开启时间不能超出营业开启时间~");
      // if (this.endTime2 > this.endTime) return this.isShowErr("灯光结束时间不能超出营业结束时间~");
      // if (this.endTime2 < this.startTime2) return this.isShowErr("灯光结束时间不能小于开始时间~");

      // if (!this.fileListLicense?.[0]) return this.isShowErr("请上传营业执照~");
      let province = areaList[0]
      let city = areaList[1]
      // let pattern = /市$/;

      if (city == '市辖区' || city == '县') {
        // 字符串以"市"字结尾，匹配成功
        province = province.split("市")[0]
        city = province + '市'
      }

      let data = {

        address: this.choosedAddressInfo.address, //具体地址
        hotelName: this.choosedAddressInfoTwo.name, //点位名称
        linkman: this.linkman, //点位联系人
        linkmanPhone: this.linkmanPhone, //点位联系电话
        service_phone: this.service_phone, //点位联系电话
        dianwei: this.dianwei,
        receiver: "",
        receiverAddres: "",
        receiverPhone: "",
        province: province,
        city: city,
        district: areaList[2],
        compete: this.compete, // 竞对情况
        remark: this.remarks, //备注
        cateid: this.selectShopTypeId, //选择的商铺类型 id
        opening_hours: this.startTime + "_" + this.endTime,
        // light_open_time: this.startTime2 + "_" + this.endTime2,
        area: this.shopArea, //商铺面级
        img: this.uploadList[0] || "", //商铺门头照
        license_img: this.fileListLicense[0] || "", //商铺营业执照/
        channel_number: this.channelNumber, //套餐数量
        lon: this.choosedAddressInfo.longitude,
        lat: this.choosedAddressInfo.latitude,
        // wifi_name: this.wifi_name,
        // wifi_psd: this.wifi_psd,
        rent_start_time: this.starManytTime != 0 ? new Date(this.starManytTime).valueOf() / 1000 : 0,
        rent_end_time: this.endManyTime != 0 ? new Date(this.endManyTime).valueOf() / 1000 : 0,
        rent: this.rent,
        rent_unit: this.tabValue,

        divide_type: this.divide_type,

      };
      let rtnData = null;
      if (this.divide_type == 2) {
        data["divide_per"] = this.divide_per;
      } else {

      }
      if (this.isFromEdit) {
        //编辑点位
        data["id"] = this.id;
        rtnData = this.$u.api.editHotel(data);
      } else {
        //新增点位
        if (this.isBindDevice) data.mid = this.mid; //新增点位并绑定设备
        rtnData = this.$u.api.addHotel(data);
      }
      rtnData.then((res) => {
        if (this.add) {
          let parames = {
            name: this.linkman,
            phone: this.linkmanPhone
          }
          this.$u.vuex("vPointUser", parames);
        }
        this.isShowSuccess(this.title + "成功", 1, () => { }, true);
      });
    },
    comfirmCompete(item) {
      this.showCompetition = false;
      this.compete = item;
    },
    comfirmShopType(item, indexlList) {
      this.showShopType = false;
      this.shopType = item;
      this.selectShopTypeId =
        this.vSiteConfig.point_config.point_category[indexlList[0]]?.id;
    },
    //审核
    examineHandler(license_status) {
      let data = {
        id: this.id,
        license_status,
      };
      this.$u.api.updateHotelStatus(data).then((res) => {

        this.isShowSuccess("操作成功", 1, () => { }, true);
      });
    },
  },
  onLoad(opt) {
    this.roleList = [{ label: "随账号分成", value: 0, status: 0 },

    {
      label: "租赁",
      value: 1,
      status: 1,
    }, { label: '随' + this.vPointName + '分成', value: 2, status: 2 }]
    if (opt?.from) {
      this.fromData = opt.from;
      this.prompt = this.vPointUser.prompt
      // this.prompt = '你好'
      if (this.fromData == "edit") {
        this.title = "编辑" + this.vPointName;
        this.isFromEdit = true;

        //编辑点位 默认赋值
        let data = JSON.parse(decodeURIComponent(opt.data));
        this.choosedAddressInfo = {
          address: data?.address,
          latitude: data?.lat,
          longitude: data?.lon,
        };
        this.choosedAddressInfoTwo = {
          name: data?.hotelName,
        };

        this.wifi_name = data?.wifi_name;
        this.wifi_psd = data.wifi_psd;

        if (data?.provice && data?.city && data?.district) {

          let provice = data?.provice
          let city = data?.city
          let district = data?.district

          if (provice == city.split('市')[0]) {
            provice = provice + '市'
            let pattern = /区$/;
            if (pattern.test(district)) {

              city = '市辖区'
            } else {
              city = '县'
            }
          }
          this.selectArea =
            provice + "-" + city + "-" + data?.district;
          this.defaultRegion = this.selectArea?.split("-");
        }
        this.compete = data?.compete || "";
        this.remarks = data?.remark || "";
        let openinHoursList = data?.opening_hours?.split("_");
        if (
          openinHoursList?.length === 2 &&
          openinHoursList?.[0] &&
          openinHoursList?.[1]
        ) {
          this.startTime = openinHoursList[0];
          this.endTime = openinHoursList[1];
        }


        // let openinHoursList2 = data?.light_open_time?.split("_");
        // if (
        //   openinHoursList2?.length === 2 &&
        //   openinHoursList2?.[0] &&
        //   openinHoursList2?.[1]
        // ) {
        //   this.startTime2 = openinHoursList2[0];
        //   this.endTime2 = openinHoursList2[1];
        // }
        // if (this.startTime2 == '09:00' && this.endTime2 == '22:00') {
        //   this.times = false
        //   this.value = '否'
        // } else {
        //   this.times = true
        //   this.value = '是'
        // }
        this.linkman = data?.linkman || "";
        this.linkmanPhone = data?.linkmanPhone || "";
        this.service_phone = data?.service_phone || "";
        this.linkmanDianwei = data?.dw?.user_login || "";
        this.dianwei = data?.dianwei || "";
        this.shopArea = data?.area || "";
        this.rent = data?.rent;
        this.tabValue = data?.rent_unit
        this.divide_type = data?.divide_type
        this.roleValue = this.roleList[this.divide_type];
        if (this.divide_type == 2) {
          this.divide_per = (data?.divide_per * 1).toFixed(0)
        }
        if (data?.rent_start_time != 0) {

          this.starManytTime = timestampToTime(data?.rent_start_time * 1000, true)
        } else {
          this.starManytTime = ''
        }
        if (data?.rent_end_time != 0) {

          this.endManyTime = timestampToTime(data?.rent_end_time * 1000, true)
        } else {
          this.endManyTime = ""
        }
        // this.starManytTime = timestampToTime('1690588800000' * 1, true)
        // this.endManyTime = timestampToTime('1690588800000' * 1, true)
        // console.log('转换时间',this.starManytTime, timestampToTime('1690588800000' * 1, true))
        if (data.img) this.fileList = [{ url: data.img }];

        if (data.license_img) {
          this.defaultFileListLicense = [{ url: data.license_img }];
          this.fileListLicense = [data.license_img];
        }
        this.uploadList = [data.img];
        this.selectShopTypeId = data?.cateid;
        this.channelNumber = data?.channel_number;
        this.id = data.id;
        this.license_status = data?.license_status;
      } else if (this.fromData == "add") {
        this.title = "新增" + this.vPointName;
        this.linkmanPhone = this.vPointUser.phone
        this.service_phone = this.vPointUser.service_phone
        this.linkman = this.vPointUser.name

        this.add = true


      } else if (this.fromData == "add_and_bind") {
        this.title = "添加绑定" + this.vPointName;
        this.btnTitle = `保存并绑定${this.vPointName}`;
        this.device_sn = opt.device_sn;
        this.mid = opt.mid;
        this.isBindDevice = true;
      }
    }
    this.shopTypeList = this.vSiteConfig?.point_config?.point_category?.map(
      (item) => {
        if (this.fromData == "edit" && item.id == this.selectShopTypeId) {
          this.shopType = item.categoryName;
        }
        return item.categoryName;
      }
    );
    this.competeList =
      this.vSiteConfig?.point_config?.compete?.search("|") > -1
        ? this.vSiteConfig?.point_config?.compete?.split("|")
        : this.vSiteConfig?.point_config?.compete
          ? [this.vSiteConfig?.point_config?.compete]
          : [];

    // console.log("🚀 ~  this.competeList ", this.competeList);
    let list = [{
      title: `用联系人电话作为${this.vPointName}账号,默认密码：123456`,
      name: "0",
      disabled: false,
      selectIndex: 0,

    }, {
      title: `选择已有的${this.vPointName}账号`,
      name: "1",
      disabled: false,
      selectIndex: 0,
    }]
    this.radioIndexList = list
  },
  onShow(e) {
    /*  #ifndef H5 */
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.item) {
      // 有值
      // 修改listData中值
      this.checkHotel = currPage.data.item;
      // this.linkman = this.checkHotel.linkman;
      this.linkmanDianwei = this.checkHotel.user_login;
      this.dianwei = this.checkHotel.id

    }
    /*#endif */
    /*  #ifdef H5 */
    this.checkHotel = this.vCurrPage.item;
    // this.linkman = this.checkHotel.linkman;
    this.linkmanDianwei = this.checkHotel.user_login;
    this.dianwei = this.checkHotel.id
    /*#endif */
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 30rpx;

  >view {
    margin-bottom: 50rpx;
  }

  .zhe {
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    position: fixed;
    // border: 1px solid #000;
    // background-color: red;
    z-index: 999;
  }

  .vpointName {
    // border: 1px solid #000;
    position: relative;

    .vpointName_zhe {
      width: 100%;
      height: 100%;
      // border: 1px solid #000;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 999;
    }
  }

  .click {
    font-size: 25rpx;
    color: $font-size-middle;
    width: 52rpx;
    text-align: center;
    display: flex;
    align-items: center;
    height: 80rpx;
    // border: 1px solid #000;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 666;
  }

  .title_right {
    width: 30rpx;
    height: 30rpx;
    margin-left: 5rpx;
    // border: 1px solid #000;
  }

  .select {
    // border: 2rpx solid #000;
    width: 125rpx;
    // padding: 0 15rpx;
    position: relative;
    font-size: 40rpx;
    margin-left: 5rpx;
    // text-align: center;
    // display: flex;

    border: 1px solid #999;

    >view:nth-child(1) {
      // border: 1px solid #000;
      margin-left: 25rpx;
      margin-right: 25rpx;
    }

    .click {
      // border: 1px solid #000;
      font-size: 25rpx;
      color: $font-size-middle;
      width: 40rpx;
      text-align: center;
      // display: flex;
      // align-items: center;
      height: 60rpx;
      // border: 1px solid #000;
      position: absolute;
      right: -10rpx;
      // margin-left: 10rpx;
      // top: 0;
      // z-index: 666;

    }

    // display: flex;
    // align-items: /center;
  }

  .fontSize {
    /* #ifdef H5 */
    width: 80rpx;
    /* #endif */
    // border:2rpx solid red;
    font-size: 40rpx !important;
  }

  .selectShow {
    width: 130rpx;
    border: 2rpx solid #000;
    position: absolute;
    top: 70rpx;
    z-index: 9999;
    background-color: white;
    left: 0;
    text-align: center;

    >view:nth-child(1) {
      border-bottom: 2rpx solid #333;

      // border: 2rpx solid #000;
    }

  }

  .row-flex {
    display: flex;

    &-item {
      display: flex;
      align-items: center;

      &-title {
        flex-shrink: 0;
        font-size: $font-size-middle;
      }

      &:last-child {
        margin-left: 10rpx;
      }
    }
  }

  .title {
    // border: 1px solid #000;
    position: relative;
    display: flex;
    align-items: center;
    color: $textBlack;
    font-size: $font-size-middle;
    font-weight: bold;
    margin-bottom: 20rpx;

    .title_text {
      margin-top: 30rpx;
    }

    .necessary {
      color: red;
      margin-left: 4rpx;
    }
  }

  .vpointName {
    // border: 1px solid #000;
    position: relative;
  }

  .btn {
    .examine {
      margin-top: 40rpx;
    }
  }

  .click {
    // border: 1px solid #000;
    font-size: 25rpx;
    color: $font-size-middle;
    width: 50rpx;
    text-align: center;
    display: flex;
    align-items: center;
    height: 80rpx;
    // border: 1px solid #000;
    position: absolute;
    // right: 0;
    // margin-left: 10rpx;
    // top: 0;
    z-index: 666;

  }

  .volume {
    position: relative;

    &-mark {
      position: absolute;
      top: 0;
      right: 0;
      width: 60rpx;
      height: 80rpx;
      text-align: center;
      line-height: 80rpx;
    }
  }

  .time {
    display: flex;

    .end-time {
      margin-left: 20rpx;
    }
  }
}

.map-box ::v-deep .u-input__input {
  font-size: 22rpx !important;
}

.map-box {
  position: relative;
  height: 80rpx;
  color: $themeColor;


  &-title {
    height: 80rpx;
    display: flex;
    align-items: center;
    position: absolute;
    right: 30rpx;
    z-index: 99;
    top: 0;

    &-txt {
      margin-left: 10rpx;
      margin-top: -4rpx;
    }
  }
}

.ridio {
  font-size: 20rpx;
}

::v-deep .u-radio__icon-wrap {
  margin-right: 15rpx;
}

::v-deep .u-radio {
  margin: 10rpx 0
}
</style>
