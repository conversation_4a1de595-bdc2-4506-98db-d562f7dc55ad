<template>
  <view class="RemoteEquipment">
    <BaseNavbar :title="title"> </BaseNavbar>
    <view class="remote">
      <!-- 顶部区域 -->
      <view class="content_top" v-show="slider">
        正在刷新{{ value }}%
        <BaseSlider :value="value"></BaseSlider>
      </view>
      <!-- 内容区域 -->
      <view class="remote_content" v-if="vUserInfo.role_id != 6">
        <!-- 内容区域顶部 -->
        <!-- <view class="content_top"> </view> -->

        <view class="remote_top">
          <view
            >设备编号：{{ device_sn ? device_sn : "" }}(<span class="small">{{
              deviceInfo.deviceTypeName
            }}</span
            >)</view
          >
          <view
            >系统码：{{
              deviceInfo.pseudocode ? deviceInfo.pseudocode : ""
            }}</view
          >
          <view class="ellipsis"
            >{{ vPointName }}名称：{{
              hotelLocation ? hotelLocation : ""
            }}</view
          >
          <view class="off_title_box">
            <view class="center_off">
              <view class="center_off_text"> 设备状态: </view>
              <view class="center_off_adio">
                <view :class="status == 3 ? '' : 'light_off'"> 在线 </view>
                <view :class="status == 3 ? 'light_on' : ''"> 离线 </view>
              </view>
            </view>
          </view>
          <view class="off_title_box" v-if="false">
            <view class="center_off">
              <view> 灯光开启时间: </view>
              <view> {{ startTime }}-{{ endTime }} </view>
            </view>
          </view>
        </view>
        <!-- 内容区域中间部分 -->
        <view class="content_center">
          <view>
            <view class="bilud" @click="start(0)">
              <img :src="startSrc" alt="" />
            </view>
            <view class="center_2">启动设备</view>
          </view>
          <view>
            <view class="bilud" @click="stop(-1)">
              <img :src="endSrc" alt="" />
            </view>
            <view class="center_2">停止设备</view>
          </view>
        </view>
        <!-- 开关按钮 -->
        <view class="content_bottom" v-if="false">
          <view class="off_box">
            <view class="center_off">
              <view class="center_off_text"> 灯光 </view>
              <view class="center_off_adio">
                <view :class="light ? '' : 'light_off'"> ON </view>
                <view :class="light ? 'light_on' : ''"> OFF </view>
              </view>
            </view>
            <view class="center_off">
              <view class="center_off_text"> 提示音 </view>
              <view class="center_off_adio">
                <view :class="music ? '' : 'light_off'"> ON </view>
                <view :class="music ? 'light_on' : ''"> OFF </view>
              </view>
            </view>
            <view
              class="off_updata"
              @click="updata"
              :style="{ pointerEvents: disable ? 'none' : '' }"
            >
              <text>刷新</text
              ><u-icon name="reload" color="#fff" size="28"></u-icon>
            </view>
          </view>
        </view>
        <!-- 内容区域底部部分 -->
        <view class="content_bottom">
          <block v-if="deviceInfo.is_pp_device">
            <view class="bottom_1">
              <!-- 音量减 -->
              <view @click="confirmVolume(-10)">
                <img src="./../../static/img/icon/remoteJian.png" alt="" />
              </view>

              <view @click="openVolumeAdjuster"
                >音量:{{ constraintLength }}</view
              >
              <!-- 音量加 -->
              <view @click="confirmVolume(10)">
                <img src="./../../static/img/icon/remoteJ.png" alt="" />
              </view>
            </view>
            <view class="bottom_2">
              <view @click="start(1)">出泡泡</view>
              <view @click="openMusic()">开启音乐</view>
              <view @click="turnTipMusicEnd()">开启提示音</view>
              <view @click="turnTipMusicStop()">关闭提示音</view>
              <view @click="openLightEnd()">启动灯光</view>
              <view @click="openLightStop()">关闭灯光</view>
              <!-- <view @click="openturnLock(1)">开启刹车</view>
            <view @click="openturnLock(0)">关闭刹车</view>
            <view @click="deviceOff()">关机</view>
            <view @click="openOff()">自动开待机</view> -->
              <view @click="openturnLock(0)" v-if="deviceInfo.isShowOffBrake"
                >关闭刹车</view
              >
              <view @click="deviceOff()" v-if="deviceInfo.isShowShutdown"
                >关机</view
              >
              <!-- <view @click="openOff()" v-if="deviceInfo.isShowAutoOnOff">自动开关机</view> -->
              <!-- <view @click="openStart()">选择挡位</view> -->
            </view>
          </block>
          <!-- huv电动车   && deviceInfo.is_new_show_button    -->
          <bolck v-else-if="deviceInfo.is_car_device">
            <view class="bottom_1">
              <!-- 音量减 -->
              <view @click="confirmVolume(-10)">
                <img src="./../../static/img/icon/remoteJian.png" alt="" />
              </view>

              <view @click="openVolumeAdjuster"
                >当前音量:{{ constraintLength }}</view
              >
              <!-- 音量加 -->
              <view @click="confirmVolume(10)">
                <img src="./../../static/img/icon/remoteJ.png" alt="" />
              </view>
            </view>
            <view class="bottom_5" @click="startCar(4)">
              <view style="margin-bottom: 10rpx">启动履带车和音乐</view>
            </view>
            <view class="bottom_5" @click="start(1)">
              <view style="margin-bottom: 10rpx">出泡泡</view>
            </view>
            <view class="bottom_5" @click="openMusic()">
              <view style="margin-bottom: 10rpx">启动音乐</view>
            </view>
            <view class="bottom_2">
              <view @click="startCar(5)"> 启动灯带 </view>
              <view @click="startCar(6)"> 关闭灯带 </view>
            </view>
            <!-- <view class="bottom_2">
              <view @click="changeLight(1, 1)">
                启动LED灯1
              </view>
              <view @click="changeLight(1, 0)">
                关闭LED灯1
              </view>

            </view>
            <view class="bottom_2">
              <view @click="changeLight(2, 1)">
                启动LED灯2
              </view>
              <view @click="changeLight(2, 0)">
                关闭LED灯2
              </view>

            </view> -->
            <!-- <view class='bottom_5 bottom_flex'>
              <view class="center_off">
                <view class="center_off_text flex">
                  <view>
                    {{ car_light ? '禁用' : '启用' }}灯带
                  </view>
                  <view class="title_right title_text">
                    <BaseIconModal name="question-circle" size="30" width="300" color="#fff" v-if="disableLight">
                      <view>

                        {{ disableLight }}
                      </view>
                    </BaseIconModal>
                  </view>
                </view>

                <view class="center_off_adio" @click="changeNo(1)">
                  <view class="center_btn" :class="car_light ? 'switch_off' : ''">
                    ON
                  </view>
                  <view class="center_btn" :class="car_light ? '' : 'light_on'">
                    OFF
                  </view>
                </view>
              </view>
            </view> -->

            <!-- <view class="bottom_5" @click="startCar(7)">
              <view style="margin-bottom: 10rpx;">设置使用时前置雷达和后置雷达预警距离</view>
            </view>
            <view class="bottom_5" @click="startCar(8)">
              <view style="margin-bottom: 10rpx;">设置空闲时前置雷达和后置雷达预警距离</view>
            </view> -->
            <view class="bottom_5 bottom_flex" @click="changeNo(2)">
              <view class="center_off">
                <view class="center_off_text">
                  {{ car_mck ? "关闭" : "开启" }}广告语
                </view>
                <view class="center_off_adio">
                  <view class="center_btn" :class="car_mck ? 'switch_off' : ''">
                    ON
                  </view>
                  <view class="center_btn" :class="car_mck ? '' : 'light_on'">
                    OFF
                  </view>
                </view>
              </view>
            </view>
            <!-- 控制器遥控器按钮 -->
            <view class="bottom_5" @click="goToRemoteController">
              <view>控制器遥控器</view>
            </view>
          </bolck>
          <!-- 履带车 -->
          <bolck
            v-else-if="deviceInfo.is_car_device && deviceInfo.is_show_button"
          >
            <view class="bottom_5">
              <view @click="startCar(1)" style="margin-bottom: 10rpx"
                >单独启动履带车和射灯</view
              >
            </view>
            <view class="bottom_5" v-if="vUserInfo.role_id != 8">
              <view
                @click="startCar(2)"
                style="margin-bottom: 10rpx; margin-top: 30rpx"
                >单独启动音乐电源和灯光</view
              >
            </view>
            <view class="bottom_btn" v-if="vUserInfo.role_id != 8">
              <view class="bottom_btn_left" @click="launchCar(2, 1)"
                >开启并启用音乐电源和灯光</view
              >
              <view class="bottom_btn_right" @click="launchCar(2, 0)"
                >关闭并禁用音乐电源和灯光</view
              >
            </view>
            <view class="bottom_5" v-if="vUserInfo.role_id != 8">
              <view
                @click="startCar(3)"
                style="margin-bottom: 10rpx; margin-top: 30rpx"
                >单独启动出泡泡</view
              >
            </view>
            <view class="bottom_btn" v-if="vUserInfo.role_id != 8">
              <view class="bottom_btn_left" @click="launchCar(3, 1)"
                >开启并启用出泡泡</view
              >
              <view class="bottom_btn_right" @click="launchCar(3, 0)"
                >关闭并禁用出泡泡</view
              >
            </view>
          </bolck>
          <!-- 空气大炮 -->
          <view
            v-else-if="deviceInfo.is_cannon_device && deviceInfo.is_show_button"
          >
            <view class="bottom_1">
              <!-- 音量减 -->
              <view @click="confirmVolume(-10)">
                <img src="./../../static/img/icon/remoteJian.png" alt="" />
              </view>

              <view @click="openVolumeAdjuster"
                >当前音量:{{ constraintLength }}</view
              >
              <!-- 音量加 -->
              <view @click="confirmVolume(10)">
                <img src="./../../static/img/icon/remoteJ.png" alt="" />
              </view>
            </view>
            <view class="bottom_2">
              <view @click="start(1)">自动开炮</view>
              <view @click="openMusic()">开启音乐</view>
              <!-- <view @click="turnTipMusicEnd()">开启提示音</view>
              <view @click="turnTipMusicStop()">关闭提示音</view> -->
              <!-- <view @click="showELiquidSize()">调节烟雾大小</view> -->
            </view>
            <view class="bottom_5 bottom_flex" @click="changeNo(2)">
              <view class="center_off">
                <view class="center_off_text">
                  {{ car_mck ? "关闭" : "开启" }}广告语
                </view>
                <view class="center_off_adio">
                  <view class="center_btn" :class="car_mck ? 'switch_off' : ''">
                    ON
                  </view>
                  <view class="center_btn" :class="car_mck ? '' : 'light_on'">
                    OFF
                  </view>
                </view>
              </view>
            </view>
          </view>
          <!-- 共享水枪 -->
          <view
            v-else-if="deviceInfo.is_water_gun && deviceInfo.is_show_button"
          >
            <view class="bottom_1">
              <!-- 音量减 -->
              <view @click="confirmVolume(-10)">
                <img src="./../../static/img/icon/remoteJian.png" alt="" />
              </view>

              <view @click="openVolumeAdjuster"
                >当前音量:{{ constraintLength }}</view
              >
              <!-- 音量加 -->
              <view @click="confirmVolume(10)">
                <img src="./../../static/img/icon/remoteJ.png" alt="" />
              </view>
            </view>

            <view class="bottom_2">
              <view @click="WaterMusicswitch('open')"> 启动音乐 </view>
              <view @click="WaterMusicswitch('close')"> 关闭音乐 </view>
            </view>
            <view class="bottom_2">
              <view @click="Waterpumpswitch('open')"> 启动水泵 </view>
              <view @click="Waterpumpswitch('close')"> 关闭水泵 </view>
            </view>
            <view class="bottom_2">
              <view @click="Switchlightstrip(1)"> 启动灯带 </view>
              <view @click="Switchlightstrip(2)"> 关闭灯带 </view>
            </view>
            <view class="bottom_5 bottom_flex" @click="changeNo(2)">
              <view class="center_off">
                <view class="center_off_text">
                  {{ car_mck ? "关闭" : "开启" }}广告语
                </view>
                <view class="center_off_adio">
                  <view class="center_btn" :class="car_mck ? 'switch_off' : ''">
                    ON
                  </view>
                  <view class="center_btn" :class="car_mck ? '' : 'light_on'">
                    OFF
                  </view>
                </view>
              </view>
            </view>
          </view>
          <!-- 电玩车 -->
          <view
            v-else-if="deviceInfo.isGameDevice && deviceInfo.is_show_button"
          >
            <!-- 控制器遥控器按钮 -->
            <view class="bottom_5" @click="goToRemoteController">
              <view style="margin-bottom: 10rpx">控制器遥控器</view>
            </view>
          </view>
          <view v-else style="height: 250rpx"> </view>

          <!-- <view class="bottom_4" @click="clear(5, 0)"  id="specialEffect"> 清除订单 </view> -->
          <view class="bottom_4" @click="clear(5, 0)" v-if="vButtonPermisFour">
            <view class="clear_btn">
              <view> 清除订单 </view>
              <view class="title_right title_text">
                <BaseIconModal
                  name="question-circle"
                  size="30"
                  width="300"
                  color="#fff"
                  v-if="prompt"
                >
                  <view>
                    {{ prompt }}
                  </view>
                </BaseIconModal>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="remote_content" v-else>
        <view class="content_top"> </view>
        <view class="content_bottom">
          <view class="bottom_2">
            <view @click="startGameDevice(2, 1)" id="specialEffect"
              >开启泡泡+音乐60秒</view
            >
            <view @click="startMusic(1)" id="specialEffect">开启音乐60秒</view>
            <view @click="stop(-1)" style="margin-top: 8px" id="specialEffect"
              >停止</view
            >
          </view>
        </view>
      </view>
    </view>
    <!-- 履带车启动时长 -->
    <BaseModal :show.sync="isShowStartcar" @confirm="confirmStartCar">
      <block v-if="car_type == 7 || car_type == 8" class="flex">
        <view class="range_title">1.如果设置为0将关闭雷达</view>
        <view slot="default" class="range_input">
          <view class="input_title">前置雷达距离(范围0-120)</view>
          <BaseInput
            type="number"
            :enableRange="true"
            v-model="front_range"
            :min="0"
            :max="120"
            rightText="厘米"
            placeholder="请输入前置雷达距离(cm)"
          />
        </view>
        <view slot="default" class="range_input">
          <view class="input_title">后置雷达距离(范围0-120)</view>
          <BaseInput
            type="number"
            :enableRange="true"
            v-model="back_range"
            :min="0"
            :max="120"
            rightText="厘米"
            placeholder="请输入后置置雷达距离(cm)"
          />
        </view>
      </block>
      <block v-else>
        <view slot="default">
          <BaseInput
            type="number"
            v-model="car_time"
            rightText="分钟"
            placeholder="请输入启动时长(分钟)"
          />
        </view>
      </block>
    </BaseModal>
    <BaseModal :show.sync="isShowStartModal" @confirm="confirmStart">
      <view slot="default">
        <BaseInput
          type="number"
          v-model="length_time"
          :disabled="vInputDisable"
          placeholder="请输入启动时长(分钟)"
        />
      </view>
    </BaseModal>
    <!-- 控制音乐时长 -->
    <BaseModal :show.sync="isShowMusicModal" @confirm="confirmMusicLength">
      <view slot="default">
        <BaseInput
          type="number"
          v-model="adjust_time"
          placeholder="请输入播放时长(分钟)"
        />
      </view>
    </BaseModal>
    <!-- 控制灯光 -->
    <BaseModal :show.sync="isShowLightModal" @confirm="confirmLight">
      <view slot="default" class="default">
        如果你用的是充电宝设备将会导致设备离线
      </view>
      <view slot="default" class="default"> 您确认要关闭灯光吗吗？ </view>
    </BaseModal>
    <!-- 控制关机 -->
    <BaseModal :show.sync="isMomuShutdown" @confirm="confirmShutdown">
      <view slot="default" class="default"> 是否要关机该设备 </view>
    </BaseModal>
    <!-- 出泡泡提示 -->
    <BaseModal :show.sync="isShowStartMain" @confirm="comfirmStartMain">
      <TextLength :num="length_time"></TextLength>
    </BaseModal>
    <!-- 开启灯光 -->
    <!-- <BaseModal :show.sync="isShowMusicEndeModal" @confirm="turnTipMusicEnd">
      <view class="time_tiltle">
        选择灯光开启时间
      </view>
      <view class="time">
        <TimeSelectHour v-model="startTime" placeholder="请选择开始时间" :defaultTime="startTime" />
        <TimeSelectHour class="end-time" v-model="endTime" placeholder="请选择结束时间" :defaultTime="endTime" />
      </view>
    </BaseModal> -->
    <!-- 关闭提示音 -->

    <BaseModal
      :show.sync="isShowMusicStopModal"
      @confirm="confirmMusicStopLength"
    >
      <view slot="default" class="default">
        如果关闭提示音会减少互动效果将影响收入
      </view>
      <view slot="default" class="default"> 您确认要关闭提示音吗？ </view>
    </BaseModal>
    <!-- 待机 -->
    <BaseModal
      :show.sync="isShowOffModal"
      title="自动开关机时间"
      @confirm="confirmOff"
    >
      <view class="default">
        当前自动开关机时间: {{ startTime }}~{{ endTime }}
      </view>
      <view class="flex">
        <BaseInput
          v-model="startTime"
          rightText="arrow"
          :disabled="true"
          placeholder="请选择开始时间"
          @onClick="clickPicker(0)"
        />
        <BaseInput
          class="end-time"
          v-model="endTime"
          rightText="arrow"
          :disabled="true"
          placeholder="请选择结束时间"
          @onClick="clickPicker(1)"
        />
        <!-- <TimeSelectHour v-model="startTime" placeholder="请选择开始时间" :defaultTime="startTime" /> -->
        <!-- <TimeSelectHour class="end-time" v-model="endTime" placeholder="请选择结束时间" :defaultTime="endTime" /> -->
      </view>
    </BaseModal>
    <!-- 挡位 -->
    <BaseModal
      :show.sync="isShowStatusModal"
      title="选择挡位"
      @confirm="confirmStatus"
    >
      <view slot="default">
        <BaseRadio
          :radioIndex.sync="radioIndex"
          :width="50"
          :list="reasonList"
        />
      </view>
    </BaseModal>
    <!-- 烟雾大小 -->
    <BaseModal
      :show.sync="isShowSize"
      title="选择烟雾大小"
      @confirm="confirmSize"
    >
      <view slot="default">
        <BaseRadio :radioIndex.sync="sizeIndex" :width="50" :list="sizeList" />
      </view>
    </BaseModal>
    <BaseModal :show.sync="isShowClearModal" @confirm="confirmClear">
      <view slot="default" class="default">
        您确认要清除正在进行的订单吗？
      </view>
    </BaseModal>

    <!-- 音量调节器 - 独立放置，不嵌套在其他弹窗中 -->
    <VolumeAdjuster
      :show="isShowVolumeModal"
      :defaultVolume="constraintLength"
      :deviceSn="device_sn"
      @confirm="handleVolumeConfirm"
      @cancel="handleVolumeCancel"
      @update:show="isShowVolumeModal = $event"
    />
    <u-picker
      mode="time"
      v-model="isPicker"
      :params="params"
      :default-time="defaultTime"
      :safe-area-inset-bottom="true"
      @confirm="confirmTim"
      class="fidex"
      :z-index="9999999"
    />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue"
import BasePopup from "@/components/base/BasePopup.vue"
import BaseModal from "@/components/base/BaseModal.vue"
import BaseInput from "@/components/base/BaseInput.vue"
import BaseRadio from "@/components/base/BaseRadio.vue"
import BaseButton from "@/components/base/BaseButton.vue"
import BaseSlider from "@/components/base/BaseSlider"
import TimeSelectHour from "@/components/common/TimeSelectHour.vue"
import BaseIconModal from "@/components/base/BaseIconModal.vue"
import TextLength from "@/components/text/TextLength.vue"
import img from "../imgUtil/image"
import VolumeAdjuster from "@/components/common/VolumeAdjuster"

export default {
  components: {
    BaseNavbar,
    BaseModal,
    BaseInput,
    BaseRadio,
    BaseButton,
    BaseSlider,
    BasePopup,
    TimeSelectHour,
    BaseIconModal,
    TextLength,
    VolumeAdjuster,
  },
  data() {
    return {
      prompt: "清除正在进行的订单",
      disableLight: "用户扫码后是否启动",
      title: "遥控设备",
      fromData: "", //来源
      isFromIndexScan: false, //首页扫码来的
      hotelLocation: "", //商户名称
      mid: "",
      device_sn: "",
      selectIndex: 0, //选中数据项
      length_time: 1, //音乐时长
      selectLightRadio: 1,
      constraintLength: 100,
      isFromReplenish: 0,
      adjust_time: 1, //音乐时长
      isShowStartModal: false, // 是否显示启动时间
      isShowMusicModal: false, //是否显示输入音乐时长
      isShowLightModal: false, // 是否关灯
      isShowStartMain: false, //出泡泡超过三分提示
      isShowMusicStopModal: false, //关闭提示音
      isShowMusicEndeModal: false, //清除提示音
      isShowOffModal: false, //待机
      isShowStatusModal: false, //挡位
      isShowClearModal: false,
      startTime: "09:00",
      endTime: "22:00",
      light: false, //灯光开关
      music: false, //提示音开关
      status: 1, //是否在线
      isShowAutoOnOff: false, // 是否显示 定时开关机按钮钮
      isShowStartcar: false, //启动履带车
      car_type: 1, //启动类型
      car_time: 1, //启动时间

      value: 0, //进度条
      slider: false,
      disable: false, //防抖开关
      isMomuShutdown: false, //关机弹窗
      lastOrderTime: 0,
      isShowVolumeModal: false, // 控制音量调节器显示
      ligthList: [
        {
          title: "关灯",
          name: 1,
          selectIndex: 0,
        },
        {
          title: "开灯",
          name: 2,
          selectIndex: 1,
        },
      ],
      reasonList: [
        {
          title: "1挡",
          name: 1,
          disabled: false,
          selectIndex: 0,
        },
        {
          title: "2挡",
          name: 2,
          disabled: false,
          selectIndex: 1,
        },
      ],
      isPicker: false,
      params: {
        year: false,
        month: false,
        day: false,
        hour: true,
        minute: true,
        second: false,
      },
      defaultTime: "",
      pickderStart: 0,
      radioIndex: 0,
      deviceInfo: {},
      startSrc: "",
      endSrc: "",
      sizeList: [
        {
          title: "特大",
          name: 500,
        },
        {
          title: "大",
          name: 1000,
        },
        {
          title: "中",
          name: 2000,
        },
        {
          title: "小",
          name: 3000,
        },
        {
          title: "特小",
          name: 6000,
        },
      ],
      sizeIndex: 0,
      isShowSize: false,
      car_light: false,
      car_mck: true,
      front_range: 0, //前置雷达距离
      back_range: 0, //后置雷达距离
    }
  },
  methods: {
    // 跳转到控制器遥控器页面
    goToRemoteController() {
      uni.navigateTo({
        url: "/pagesD/remote/index",
      })
    },
    sendRequest(i, newState) {
      console.log(`发送请求: 参数 ${i}, 新状态 ${newState}`)
      // 这里可以加入实际的请求逻辑，例如：
      // axios.post('/api/update', { id: i, state: newState });
    },
    async changeNo(i) {
      if (!this.setTime()) {
        return
      }
      try {
        // 定义状态映射和对应的 API 方法
        const stateMap = {
          1: {
            key: "car_light",
            api: this.$u.api.setCarLight, // car_light 对应的 API 方法
          },
          2: {
            key: "car_mck",
            api: this.$u.api.turnTipMusic, // car_mck 对应的 API 方法
          },
        }

        // 检查传入的 i 是否有效
        if (!stateMap[i]) {
          this.isShowErr("无效的参数")
        }

        const { key, api } = stateMap[i] // 获取状态键和对应的 API 方法
        const newState = !this[key] // 计算新状态

        // 构造请求数据
        const data = {
          status: newState,
          device_sn: this.device_sn,
        }
        if (i == 3 || i == 4) {
          data["status"] = newState ? 1 : 0
        }

        // 调用对应的 API 更新状态
        await api(data)

        // 更新本地状态
        this[key] = newState
        this.isShowTwo("操作成功", 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        // console.error('changeNo 执行失败:', error.message);
        this.isShowErr(error.msg) // 显示错误提示
      }
    },
    // 打开音量调节器
    openVolumeAdjuster() {
      if (!this.setTime()) {
        return
      }
      // 🔥 移除 isFromReplenish 限制，允许所有情况下都能打开音量调节器
      this.isShowVolumeModal = true
    },

    // 处理音量调节确认
    async handleVolumeConfirm(volume) {
      // 🔥 立即关闭弹窗
      this.isShowVolumeModal = false

      if (!this.setTime()) {
        return
      }

      // 🔥 移除 isFromReplenish 限制，允许所有情况下都能调节音量
      this.constraintLength = volume
      let data = {
        device_sn: this.device_sn,
        volume: this.constraintLength,
      }
      try {
        let res = await this.$u.api.getUserVideo(data)
        this.lastOrderTime = new Date().valueOf()
        this.isShowSuccess(res.msg)
      } catch (error) {
        this.isShowErr(error.msg)
      }
    },
    // 处理音量调节取消
    handleVolumeCancel() {
      // 取消时不做任何操作，只关闭弹窗
      this.isShowVolumeModal = false
    },

    async confirmSize() {
      try {
        let data = {
          size: this.sizeList[this.sizeIndex].name,
          device_sn: this.device_sn,
        }
        let res = await this.$u.api.setELiquidSize(data, "调节中")
        this.isShowTwo("调节成功", 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        this.isShowErr(error.msg)
      }
    },
    showELiquidSize() {
      this.isShowSize = true
    },
    //1单独启动履带车和射灯 2单独启动音乐电源和灯光 3单独启动出泡泡 4启动履带车和音乐 5启动灯带 6关闭灯带 7启动雷达最大范围
    async startCar(type) {
      if (!this.setTime()) {
        return
      }
      if (type == 6) {
        let data = {
          device_sn: this.device_sn,
          time: 0,
        }
        await this.$u.api.startCarLight(data, "关闭中")
        this.lastOrderTime = new Date().valueOf()
        return
      } else if (type == 7) {
        // this.car_time = this.deviceInfo.radar_range
        this.front_range = this.deviceInfo.radar_range_front_using ?? 50
        this.back_range = this.deviceInfo.radar_range_back_using ?? 50
      } else if (type == 8) {
        this.front_range = this.deviceInfo.radar_range_front_free || 50
        this.back_range = this.deviceInfo.radar_range_back_free || 50
      } else {
        this.car_time = 1
      }

      this.car_type = type
      this.isShowStartcar = true
      // if (type == 1) {

      // } else {
      //   this.confirmStartCar()
      // }
    },
    // 水枪灯带开关方法
    async Switchlightstrip(i) {
      if (!this.setTime()) return

      try {
        // 专门处理灯带控制
        const newState = i === 1 // 1表示开启，2表示关闭
        const data = {
          device_sn: this.device_sn,
          status: newState ? 1 : 0,
        }

        // 调用灯带控制API
        await this.$u.api.Switchlightstrip(
          data,
          newState ? "开启灯带中" : "关闭灯带中"
        )
        // 更新灯带状态
        this.car_light = newState
        this.isShowTwo(newState ? "灯带已开启" : "灯带已关闭", 1)
        this.lastOrderTime = Date.now()
      } catch (error) {
        this.isShowErr(error.msg || "操作失败")
        console.error("灯带控制失败:", error)
      }
    },
    // 水枪 水泵开关控制方法
    async Waterpumpswitch(action) {
      if (!this.setTime()) return // 防抖检查

      try {
        const data = {
          device_sn: this.device_sn,
          status: action === "open" ? 1 : 0, // 1开启，0关闭
        }

        await this.$u.api.Waterpumpswitch(
          data,
          action === "open" ? "开启水泵中" : "关闭水泵中"
        )
        this.isShowTwo(action === "open" ? "水泵已开启" : "水泵已关闭", 1)
        this.lastOrderTime = Date.now()
      } catch (error) {
        this.isShowErr(error.msg || "操作失败")
        console.error("水泵控制失败:", error)
      }
    },
    // 水枪音乐开关方法
    async WaterMusicswitch(action) {
      if (!this.setTime()) return // 防抖检查

      try {
        const data = {
          device_sn: this.device_sn,
          status: action === "open" ? 1 : 0, // 1开启，0关闭
        }

        await this.$u.api.WaterMusicswitch(
          data,
          action === "open" ? "开启音乐中" : "关闭音乐中"
        )

        this.isShowTwo(action === "open" ? "音乐已开启" : "音乐已关闭", 1)
        this.lastOrderTime = Date.now()
      } catch (error) {
        this.isShowErr(error.msg || "操作失败")
        console.error("音乐控制失败:", error)
      }
    },
    async launchCar(type, status) {
      try {
        let data = {
          device_sn: this.device_sn,
          status: status,
        }

        let res = null
        if (type === 1) {
          data["time"] = this.car_time
          res = await this.$u.api.onAndOffCar(data, "启动中")
        } else if (type === 2) {
          res = await this.$u.api.onAndOffCarLightAndMusic(data, "启动中")
        } else if (type === 3) {
          res = await this.$u.api.onAndOffCarPP(data, "启动中")
        }
        this.isShowTwo("启动成功", 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        this.isShowErr(error.msg)
      }
    },
    async confirmStartCar() {
      try {
        // 定义 car_type 和对应 API 的映射
        const apiMap = {
          1: this.$u.api.startCar,
          2: this.$u.api.startCarLightAndMusic,
          3: this.$u.api.startCarPP,
          4: this.$u.api.startCarAndMusic,
          5: this.$u.api.startCarLight,
          7: this.$u.api.setCarRadarRangeByUsing,
          8: this.$u.api.setCarRadarRangeByFree,
        }

        // 检查 car_type 是否有效
        if (!apiMap[this.car_type]) {
          this.isShowErr("无效的参数")
        }
        let title = "启动"
        // 构造请求数据
        const data = {
          device_sn: this.device_sn,
        }
        if (this.car_type == 7 || this.car_type == 8) {
          data["front_range"] = this.front_range
          data["back_range"] = this.back_range
          title = "操作"
        } else {
          data["time"] = this.car_time // time 是公共参数
        }

        // 动态调用对应的 API
        const apiMethod = apiMap[this.car_type]
        await apiMethod(data, `${title}中`)

        // 显示成功提示
        this.isShowTwo(`${title}成功`)
        this.lastOrderTime = new Date().valueOf()
        //重新请求数据
        // this.getHotel();
      } catch (error) {
        // console.error('confirmStartCar 执行失败:', erro);
        this.isShowTwo(error.msg, 0) // 显示失败提示
      }
    },
    confirmTim(e) {
      let { hour, minute } = e
      if (this.pickderStart == 0) {
        this.startTime = `${hour}:${minute}`
      } else {
        this.endTime = `${hour}:${minute}`
      }
    },
    clickPicker(start) {
      if (start == 0) {
        this.pickderStart = 0
        this.defaultTime = this.startTime
      } else {
        this.pickderStart = 1
        this.defaultTime = this.endTime
      }
      console.log("defaultTime", this.startTime, this.defaultTime)
      this.isPicker = true
    },

    async confirmShutdown() {
      try {
        let data = {
          device_sn: this.device_sn,
        }
        // let that = this;
        await this.$u.api.shutdown(data, "关机中")
        // this.isShowSuccess("操作成功", 1, () => { }, true);
        this.isShowTwo("关机成功", 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        this.isShowErr(error.msg)
      }
    },
    //关机
    deviceOff() {
      if (!this.setTime()) {
        return
      }
      this.isMomuShutdown = true
    },
    // 开关刹车
    async openturnLock(i) {
      if (!this.setTime()) {
        return
      }
      let data = {
        device_sn: this.device_sn,
        status: i,
      }
      let titles
      if (i == 0) {
        titles = "关闭刹车"
      } else {
        titles = "开启刹车"
      }
      // console.log("启动设备结果参数 3 ：", data);
      try {
        await this.$u.api.offBrake(data, `${titles}中~`)
        // console.log("启动设备结果4 ：", res);
        this.light = false
        // this.isShowSuccess("操作成功", 1, () => { }, true);
        this.isShowTwo(`${titles}成功`, 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        this.isShowErr(error.msg)
      }
    },
    /* 防抖 */
    setTime() {
      let timestamp = new Date().valueOf()
      if (timestamp - this.lastOrderTime < 5000) {
        uni.showToast({
          title: `其他指令正在执行请等${
            5 - ((timestamp - this.lastOrderTime) / 1000).toFixed(0) + 1
          }秒后进行其他操作`,
          icon: "none",
          duration: 1500,
        })
        return false
      } else {
        return true
      }
    },

    // 调节音量
    async confirmVolume(i) {
      if (!this.setTime()) {
        return
      }

      // 🔥 移除 isFromReplenish 限制，所有设备都能调节音量
      if (this.constraintLength >= 0) {
        this.constraintLength += i
        if (this.constraintLength <= 0) {
          this.constraintLength = 0
        } else if (this.constraintLength >= 100) {
          this.constraintLength = 100
        }
        let data = {
          device_sn: this.device_sn,
          volume: this.constraintLength,
        }
        try {
          let res = await this.$u.api.getUserVideo(data)
          this.lastOrderTime = new Date().valueOf()
          this.isShowSuccess(res.msg)
        } catch (error) {
          this.isShowErr(error.msg)
        }
      }
    },
    // 停止游戏设备
    stop(i) {
      // 停止游戏设备
      if (this.isFromReplenish) {
        // 补货管理才能有启动
        this.selectIndex = i
        this.length_time = 0
        this.confirmStart()
      }
    },
    // 重置
    clear(model, status) {
      if (!this.setTime()) {
        return
      }
      this.isShowClearModal = true
      // 清除设备在使用的状态
    },
    //清除
    async confirmClear(model = 5, status = 0) {
      try {
        let data = {
          device_sn: this.device_sn,
          model: model, // 5,游戏模式 3，充电模式
          status: status, // 1.正在进行 2.未开始
        }
        // let that = this;
        await this.$u.api.clearUMModelStatus(data, "清除订单中")
        // this.isShowSuccess("操作成功", 1, () => { }, true);
        this.isShowTwo("清除成功", 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        this.isShowErr(error.msg)
      }
    },
    // 开启控制灯
    async openLightEnd() {
      if (!this.setTime()) {
        return
      }
      try {
        let data = {
          device_sn: this.device_sn,
          status: 1,
        }
        // console.log("启动设备结果参数 3 ：", data);
        await this.$u.api.turnLight(data, "开启灯光中")
        this.light = false
        // this.isShowSuccess("操作成功", 1, () => { }, true);
        this.isShowTwo("开启成功", 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        // console.log("启动设备结果参数 3 ：", error);
        this.isShowErr(error.msg)
      }
    },
    // 关闭控制灯
    openLightStop() {
      this.isShowLightModal = true
    },
    //Led灯
    async changeLight(i, status) {
      if (!this.setTime()) {
        return
      }
      try {
        let data = {
          device_sn: this.device_sn,
          status: status,
          // startTime: this.startTime,
          // endTime: this.endTime
        }
        const apiMap = {
          1: this.$u.api.turnLightOne,
          2: this.$u.api.turnLightTwo,
        }
        await apiMap[i](data, `${status ? "开启" : "关闭"}中~`)
        this.isShowTwo(`${status ? "开启" : "关闭"}成功`, 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        this.isShowErr(error.msg)
        // console.log(error)
      }
    },
    async confirmLight() {
      if (!this.setTime()) {
        return
      }
      try {
        // 控制灯
        let data = {
          device_sn: this.device_sn,
          status: 0,
        }
        // console.log("启动设备结果参数 3 ：", data);
        await this.$u.api.turnLight(data, "关闭灯光中")
        this.light = true
        // this.isShowSuccess("操作成功", 1, () => { }, true);
        this.isShowTwo("已关闭", 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        this.isShowErr(error.msg)
        // console.log("启动设备结果参数 3 ：", error);
      }
    },
    turnTipEnd() {
      this.isShowMusicEndeModal = true
    },
    // 开启提示音
    async turnTipMusicEnd() {
      try {
        if (!this.setTime()) {
          return
        }
        let data = {
          device_sn: this.device_sn,
          status: 1,
          // startTime: this.startTime,
          // endTime: this.endTime
        }
        // this.isShowMusicStopModal = true
        // console.log("启动设备结果参数 3 ：", data);
        await this.$u.api.turnTipMusic(data, "开启提示音中")
        this.music = false
        // this.isShowSuccess("操作成功", 1, () => { }, true);
        this.isShowTwo("开启成功", 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        this.isShowErr(error.msg)
      }
    },

    exit() {
      this.isShowMusicStopModal = false
    },
    // 关闭提示音
    turnTipMusicStop() {
      this.isShowMusicStopModal = true
    },
    //自动待机
    openOff() {
      this.isShowOffModal = true
    },
    async confirmOff() {
      // if(this.endTime<this.startTime){
      //   this.isShowOffModal = true
      //   return this.isShowErr('结束时间小于开始时间')
      // }
      this.isShowOffModal = false
      try {
        let data = {
          device_sn: this.device_sn,
          start_time: this.startTime,
          end_time: this.endTime,
        }
        await this.$u.api.autoOnOff(data, "开启自动待机中")
        this.music = true
        this.isShowTwo("操作成功", 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        this.isShowErr(error.msg)
      }
    },
    //选择挡位
    openStart() {
      this.isShowStatusModal = true
    },
    confirmStatus() {},
    async confirmMusicStopLength() {
      if (!this.setTime()) {
        return
      }
      try {
        let data = {
          device_sn: this.device_sn,
          status: 0,
        }
        // console.log("启动设备结果参数 3 ：", data);
        await this.$u.api.turnTipMusic(data, "关闭提示音中")
        this.music = true
        // this.isShowSuccess("操作成功", 1, () => { }, true);
        this.isShowTwo("已关闭", 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        // console.log("启动设备结果参数 3 ：", error);
        this.isShowErr(error.msg)
      }
    },

    //游戏设备，启动游戏设备
    start(i) {
      if (!this.setTime()) {
        return
      }
      this.length_time = 1
      if (this.isFromReplenish) {
        // 补货管理才能有启动
        this.selectIndex = i
        this.isShowStartModal = true
      }
    },
    confirmStart() {
      // 开启游戏设备
      // this.isShowProgress = true
      if (this.length_time >= 3 && !this.deviceInfo.isShowOnlyStartAndStop) {
        this.isShowStartMain = true
      } else {
        this.startGameDevice(this.selectIndex + 1, this.length_time)
      }
    },
    comfirmStartMain() {
      this.startGameDevice(this.selectIndex + 1, this.length_time)
    },

    async startGameDevice(channel, time) {
      try {
        const OPERATION_MAP = {
          1: {
            text: "启动中",
            successMsg: `启动${time}分钟`,
            validate: (t) => Number(t) > 0,
          },
          2: {
            text: "出泡泡中",
            successMsg: `出泡泡${time}分钟`,
            validate: () => true,
          },
          default: {
            text: "停止中",
            successMsg: "已停止",
            validate: () => true,
          },
        }

        const config = OPERATION_MAP[channel] || OPERATION_MAP.default
        if (!config.validate(time)) {
          this.isShowErr("无效的参数")
        }

        const payload = {
          device_sn: this.device_sn,
          channel: Number(channel),
          length_time: Number(time),
        }

        await this.$u.api.startUM(payload, config.text)

        this.isShowTwo(config.successMsg, 1)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        this.isShowErr("操作失败")
      }
    },
    // 播放音乐时长
    openMusic() {
      if (this.isFromReplenish) {
        this.isShowMusicModal = true
      }
    },
    // 播放音乐时长
    confirmMusicLength() {
      if (!this.setTime()) {
        return
      }
      this.startMusic(this.adjust_time)
    },
    // 播放音乐
    async startMusic(time) {
      try {
        let data = {
          device_sn: this.device_sn,
          length_time: time, //获取子组件传来的数据
        }
        let res = await this.$u.api.startMusic(data)
        this.isShowSuccess(res.msg)
        this.lastOrderTime = new Date().valueOf()
      } catch (error) {
        this.isShowErr(error.msg)
      }
    },
    // async getActive() {
    //   try {
    //     let data = {
    //       mid: this.mid,
    //     };
    //     let rtn = await this.$u.api.isActive(data);
    //     this.device_sn = rtn?.device_sn;
    //   } catch (error) {
    //     console.log(error)
    //   }

    // },
    async activeMachine() {
      try {
        //领取设备
        let data = {
          device_sn: this.device_sn,
        }

        this.$u.api.activeMachine(data).then((res) => {
          this.isShowSuccess("领取成功", 0, this.getActive())
          this.lastOrderTime = new Date().valueOf()
        })
      } catch (error) {
        this.isShowSuccess("领取失败", 1)
      }
    },
    // 获取商户
    async getHotel() {
      try {
        let data = {
          mid: this.mid,
        }
        let res = await this.$u.api.getUMHotel(data)
        this.hotelLocation = res.hotelName
        this.status = res.status
        this.device_sn = res.device_sn
        this.deviceInfo = res
        this.car_mck = res.tip_music * 1 > 0
        // this.startTime = res.startTime || '09:00'
        // this.endTime = res.endTime || '22:00'
        // 🔥 移除 isShowVolume 限制，所有设备都应该能获取和保存音量
        if (res.volume !== undefined && res.volume !== null) {
          this.constraintLength = res.volume
        }
        // this.lastOrderTime = new Date().valueOf();
      } catch (error) {
        this.isShowErr(error.msg)
      }
    },
    /* 刷新状态 */
    updata() {
      if (!this.setTime()) {
        return
      }
      this.value = 0
      this.slider = true
      this.disable = true
      /* 发送请求 */
      let time = setInterval(() => {
        if (this.value < 100) {
          this.value = this.value + 20
        }
      }, 1000)
      let time2 = setTimeout(() => {
        clearInterval(time)
        this.slider = false
        this.disable = false
        /* 发送请求 */
      }, 5000)
    },
  },
  onLoad(opt) {
    if (opt?.from) {
      this.fromData = opt.from
      if (this.fromData == "replenish") {
        //补货来的
        this.title = "遥控设备"
        this.isFromReplenish = true
        this.device_sn = opt.device_sn
        this.startSrc = img.startImg
        this.endSrc = img.endImg
      }
    }
    if (opt.mid) {
      this.mid = opt.mid
      // this.getActive();
      this.getHotel()
    }
    if (this.vInputDisable) {
      this.length_time = this.vTime
    }
  },
  onShow(e) {
    // let pages = getCurrentPages();
    // let currPage = pages[pages.length - 1]; // 当前页
    // console.log(currPage);
  },
}
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.flex {
  margin: 20rpx 0;
  display: flex;
  align-items: center;
}

.range_title {
  font-size: 25rpx;
  margin: 20rpx;
  color: orangered;
  text-align: center;
}

.input_title {
  margin: 10rpx 0;
}

.range_input {
  margin: 20rpx 0;
}

.bottom_btn {
  display: flex;
  width: 100%;
  font-size: 20rpx;

  .bottom_btn_left {
    flex: 0.5;
    margin-right: 10rpx;
    border-top: 0;
    border-right: 0;
    background-color: #46a1ff;
    border-radius: 5rpx;
    color: #fff;
    padding: 15rpx 10rpx;
    overflow: hidden;
    white-space: nowrap;
    text-decoration: ellipsis;
    text-align: center;
  }

  .bottom_btn_right {
    flex: 0.5;
    // border: 2rpx solid #10cbe8;
    border-radius: 5rpx;
    background: linear-gradient(
      100deg,
      rgb(233, 39, 39) 0%,
      rgb(255, 91, 91) 100%
    );
    border-top: 0;
    color: #fff;
    padding: 15rpx 10rpx;
    overflow: hidden;
    white-space: nowrap;
    text-decoration: ellipsis;
    text-align: center;
  }
}

.title_right {
  position: relative;
  width: 30rpx;
  height: 30rpx;
  // border: 1px solid #000;
  margin-left: 15rpx;
}

.ellipsis {
  white-space: nowrap;
  /* 阻止文本换行 */
  overflow: hidden;
  /* 隐藏超出容器的部分 */
  text-overflow: ellipsis;
  /* 可选：显示省略号 */
}

.title_text {
  font-size: 28rpx;
  color: #333;
}

.clear_btn {
  // border: 1px solid #000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.RemoteEquipment {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: auto;

  #specialEffect {
    transition: all 1.2ms ease-in;
  }

  #specialEffect:hover {
    background: linear-gradient(100deg, #46a1ff 0%, rgb(255, 91, 91) 100%);
  }

  .time {
    display: flex;
    padding: 20rpx;
    justify-content: space-between;

    .end-time {
      margin-left: 20rpx;
    }
  }

  .end-time {
    margin-left: 20rpx;
  }

  .time_tiltle {
    text-align: center;
    margin: 20rpx auto;
  }

  .remote {
    width: 100%;
    height: 100%;
    padding: 50rpx 50rpx 0rpx 50rpx;
    box-sizing: border-box;

    .remote_top {
      width: 95%;
      margin: auto;
      color: #666;
      display: flex;
      margin-top: 35rpx;
      border-radius: 15rpx;
      // margin-bottom: 35rpx;
      flex-direction: column;
      text-align: center;
      font-size: 28rpx;
      padding: 15rpx 0;
      // border: 1px solid #000;
      // background-color: #e7e7e7;

      .small {
        font-size: 24rpx;
      }

      & > view:nth-child(1) {
        padding: 0 15rpx;
        // border-right: 1rpx solid #333;
        margin-right: 10rpx;
      }

      & > view:nth-child(2) {
        overflow: hidden;
        white-space: nowrap;
        text-align: nowrap;
        padding: 0 15rpx;
        // border: 1px solid #000;
      }
    }

    .remote_content {
      width: 100%;
      min-height: 600rpx;
      max-height: auto;
      background: rgb(255, 255, 255);
      z-index: 1;
      border-radius: 20rpx;
      box-shadow: rgba(#333, 20%);
      overflow: hidden;
      margin-top: 15rpx;
      position: relative;

      .content_top {
        // left: 0;
        // top: 0;
        // position: absolute;
        margin: 15rpx 5rpx;
      }

      .content_center {
        // width: 100%;
        height: 340rpx;
        display: flex;
        // flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-bottom: 55rpx;
        text-align: center;

        .bilud {
          margin: 20rpx;
          border-radius: 50%;
        }

        img {
          width: 260rpx;
          height: 260rpx;
        }
      }

      .content_bottom {
        margin-top: 40rpx;
        width: 100%;
        padding: 0px 55rpx 30rpx 55rpx;

        & > view {
          font-weight: bold;
          font-size: 34rpx;
          margin-bottom: 25rpx;
        }

        .off_box {
          justify-content: space-between;
          display: flex;
          margin: 0 30rpx;
          // border: 1px solid #000;
          height: 38rpx;
          font-size: 20rpx;
          align-items: center;
        }

        .off_updata {
          border-radius: 20rpx;
          padding: 2rpx 10rpx;
          display: flex;
          justify-content: space-between;
          color: white;
          background-color: #46a1ff;

          text {
            margin-right: 5rpx;
          }
        }

        .center_off {
          display: flex;

          // border: 1px solid #000;
          > view {
            display: flex;
          }

          .center_off_text {
            margin-right: 5rpx;
          }

          .center_off_adio {
            border-radius: 20rpx;
            background-color: #e6e6e6;
            padding: 2rpx 5rpx;

            > view {
              border-radius: 20rpx;
              padding: 2rpx 12rpx;
            }
          }

          .light_off {
            color: white;

            background-color: #46a1ff;
          }

          .switch_off {
            color: white;

            background-color: #0feb4a;
          }

          .light_on {
            color: white;
            background-color: orangered;
          }
        }

        .bottom_1 {
          width: 100%;
          height: 111rpx;
          background: rgb(247, 247, 247);
          border-radius: 10rpx;
          padding: 18rpx 26rpx;
          box-sizing: border-box;
          overflow: hidden;
          display: flex;
          justify-content: space-around;
          align-items: center;
          font-size: 30rpx;
          font-weight: bold;

          & > view:nth-child(1) {
            width: 126rpx;
            height: 100%;
            cursor: pointer;
            border-radius: 8rpx;
            background: #46a1ff;
            display: flex;
            justify-content: center;
            align-items: center;

            & > img {
              width: 45rpx;
              height: 45rpx;
            }
          }

          & > view:nth-child(3) {
            width: 126rpx;
            height: 100%;
            border-radius: 8rpx;
            background: #46a1ff;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;

            & > img {
              width: 45rpx;
              height: 45rpx;
            }
          }
        }

        .bottom_5 {
          width: 100%;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          color: rgb(255, 255, 255);

          & > view {
            width: 100%;
            height: 85rpx;
            line-height: 85rpx;
            background: #46a1ff;
            border-radius: 8rpx;
            margin: 12rpx 0;
            text-align: center;
            box-sizing: border-box;
            font-weight: 600;
            cursor: pointer;
          }
        }

        .bottom_2 {
          width: 100%;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          color: rgb(255, 255, 255);

          & > view {
            width: 260rpx;
            height: 85rpx;
            line-height: 85rpx;
            background: #46a1ff;
            border-radius: 8rpx;
            margin: 12rpx 0;
            text-align: center;
            box-sizing: border-box;
            font-weight: 600;
            cursor: pointer;
          }
        }

        .bottom_3 {
          width: 100%;
          display: flex;
          height: 95rpx;
          justify-content: space-between;
          color: rgb(255, 255, 255);

          & > view {
            width: 260rpx;
            height: 85rpx;
            line-height: 85rpx;
            background: #46a1ff;
            border-radius: 8rpx;
            text-align: center;
            font-weight: 600;
            cursor: pointer;
          }
        }

        .bottom_4 {
          width: 100%;
          color: rgb(255, 255, 255);
          height: 85rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          // line-height: 85rpx;
          background: linear-gradient(
            100deg,
            rgb(233, 39, 39) 0%,
            rgb(255, 91, 91) 100%
          );
          box-sizing: border-box;
          text-align: center;
          border-radius: 8rpx;
          font-weight: 600;
          cursor: pointer;
          margin-top: 60rpx;
          margin-bottom: 100rpx;
        }

        .bottom_6 {
          width: 100%;
          color: rgb(255, 255, 255);
          height: 85rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          // line-height: 85rpx;
          background: #46a1ff;
          box-sizing: border-box;
          text-align: center;
          border-radius: 8rpx;
          font-weight: 600;
          margin: 12rpx 0;
          cursor: pointer;
        }
      }
    }
  }
}

.off_title_box {
  margin: 10rpx 0;
  // border: 1px solid #000;
  justify-content: center;
  display: flex;

  .center_off {
    display: flex;

    // border: 1px solid #000;
    > view {
      display: flex;
    }

    .center_off_text {
      margin-right: 5rpx;
    }

    .center_off_adio {
      font-size: 20rpx;
      border-radius: 20rpx;
      background-color: #e6e6e6;
      padding: 2rpx 5rpx;

      > view {
        border-radius: 20rpx;
        padding: 2rpx 12rpx;
      }
    }

    .light_off {
      color: white;

      background-color: #46a1ff;
    }

    .light_on {
      color: white;
      background-color: orangered;
    }
  }
}

.default {
  text-align: center;
  font-size: 26rpx;
}

.bottom_flex {
  .center_off {
    align-items: center;
    width: 100%;
    justify-content: space-between;
    padding: 0 30rpx;

    .center_off_adio {
      // margin-left: 5rpx;
      width: 160rpx;
      height: 50rpx;
      line-height: 50rpx;
      align-items: center;
      padding: 0 !important;

      .center_btn {
        width: 50%;
        padding: 0 !important;
      }
    }
  }
}
</style>
