<template>
  <view class="turn-controls">
    <view class="myright">
      <!-- 右转按钮 -->
      <view
        class="ctrl-btn right-turn"
        :class="{ active: isRightTurnActive }"
        @touchstart="rightTurnPress"
        @touchmove="handleRightTurnTouchMove"
        @touchend="rightTurnRelease"
        @touchcancel="rightTurnRelease"
      >
        <view class="button-background">
          <image
            src="../../static/左转.svg"
            :class="isRightTurnActive ? 'imagAc' : 'imag'"
            mode="aspectFit"
            alt="右转"
          />
          <view class="text">右转</view>
        </view>
      </view>

      <!-- 左转按钮 -->
      <view
        class="ctrl-btn left-turn"
        :class="{ active: isLeftTurnActive }"
        @touchstart="leftTurnPress"
        @touchmove="handleLeftTurnTouchMove"
        @touchend="leftTurnRelease"
        @touchcancel="leftTurnRelease"
      >
        <view class="button-background">
          <image
            src="../../static/右转.svg"
            :class="isLeftTurnActive ? 'imagAc' : 'imag'"
            mode="aspectFit"
            alt="左转"
          />
          <view class="text">左转</view>
        </view>
      </view>
    </view>

    <!-- 摇摆控制按钮组 -->
    <view class="swing-control-group">
      <!-- 摇摆使能按钮 - 控制摇摆功能显示/隐藏 -->
      <view
        class="swing-button"
        :class="{ active: isSwingButtonVisible }"
        @click="handleSwingEnable"
      >
        {{ isSwingButtonVisible ? "摇摆显示" : "摇摆不显示" }}
      </view>

      <!-- 获取状态按钮 -->
      <view class="swing-button" @click="handleGetStatus"> 获取状态 </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "TurnControls",
  props: {
    isLeftTurnActive: Boolean, // 左转按钮状态
    isRightTurnActive: Boolean, // 右转按钮状态
    isSwingEnabled: Boolean, // 摇摆是否启用
    isSwingButtonVisible: Boolean, // 摇摆按钮是否显示
  },
  data() {
    return {
      leftTurnButtonRect: null, // 左转按钮位置信息
      rightTurnButtonRect: null, // 右转按钮位置信息
    }
  },
  computed: {
    // 根据摇摆状态更新按钮激活状态
    isSwingOnActive() {
      return this.isSwingEnabled
    },
    isSwingOffActive() {
      return !this.isSwingEnabled
    },
  },
  methods: {
    // 摇摆使能方法 - 发送0x22指令控制摇摆功能显示/隐藏
    handleSwingEnable() {
      this.$emit("swing-enable-toggle")
    },

    // 获取状态方法
    handleGetStatus() {
      this.$emit("get-status")
    },

    // 左转按钮事件
    leftTurnPress() {
      this.getLeftTurnButtonRect()
      this.$emit("left-turn-press")
    },
    leftTurnRelease() {
      this.leftTurnButtonRect = null
      this.$emit("left-turn-release")
    },

    // 左转按钮触摸移动处理
    handleLeftTurnTouchMove(e) {
      if (!this.isLeftTurnActive || !this.leftTurnButtonRect) return

      const touch = e.touches[0]
      const isInside = this.isPointInButton(
        touch.clientX,
        touch.clientY,
        this.leftTurnButtonRect
      )

      if (!isInside) {
        // 触摸点移出按钮区域，自动弹起
        console.log("🔄 左转按钮：触摸移出区域，自动弹起")
        this.leftTurnRelease()
      }
    },

    // 右转按钮事件
    rightTurnPress() {
      this.getRightTurnButtonRect()
      this.$emit("right-turn-press")
    },
    rightTurnRelease() {
      this.rightTurnButtonRect = null
      this.$emit("right-turn-release")
    },

    // 右转按钮触摸移动处理
    handleRightTurnTouchMove(e) {
      if (!this.isRightTurnActive || !this.rightTurnButtonRect) return

      const touch = e.touches[0]
      const isInside = this.isPointInButton(
        touch.clientX,
        touch.clientY,
        this.rightTurnButtonRect
      )

      if (!isInside) {
        // 触摸点移出按钮区域，自动弹起
        console.log("🔄 右转按钮：触摸移出区域，自动弹起")
        this.rightTurnRelease()
      }
    },

    // 获取左转按钮的位置信息
    getLeftTurnButtonRect() {
      const query = uni.createSelectorQuery().in(this)
      query
        .select(".ctrl-btn.left-turn")
        .boundingClientRect((rect) => {
          this.leftTurnButtonRect = rect
        })
        .exec()
    },

    // 获取右转按钮的位置信息
    getRightTurnButtonRect() {
      const query = uni.createSelectorQuery().in(this)
      query
        .select(".ctrl-btn.right-turn")
        .boundingClientRect((rect) => {
          this.rightTurnButtonRect = rect
        })
        .exec()
    },

    // 判断触摸点是否在按钮区域内
    isPointInButton(x, y, buttonRect) {
      if (!buttonRect) return false

      const { left, top, right, bottom } = buttonRect
      return x >= left && x <= right && y >= top && y <= bottom
    },
  },
}
</script>

<style scoped lang="scss">
.turn-controls {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 30rpx;
  margin-right: 180rpx;
  margin-top: 40rpx;
  padding-top: 20rpx;
  position: relative;
  z-index: 50;
  /* 临时调试：添加背景色看位置 */
  // background-color: rgba(255, 0, 0, 0.3);
}

/* 摇摆控制组样式 */
.swing-control-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
  align-items: center;
  margin-top: 80rpx;
}

/* 摇摆按钮样式 - 参考 ControlPanel 中的速度按钮 */
.swing-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 150rpx;
  height: 60rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  background-color: rgba(128, 128, 128, 0.273);
  border-radius: 10rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  pointer-events: auto;
  padding: 0 5rpx;
  /* 去掉模糊效果和过渡动画 */
  outline: none;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  -webkit-user-select: none;

  /* 3D凸起效果 - 与其他按钮保持一致 */
  box-shadow:
    /* 外部发光 */ 0 0 15rpx rgba(0, 255, 255, 0.2),
    /* 上方高光 */ 0 -2rpx 4rpx rgba(255, 255, 255, 0.1),
    /* 下方阴影 */ 0 4rpx 8rpx rgba(0, 0, 0, 0.3),
    /* 内部高光 */ inset 0 1rpx 2rpx rgba(255, 255, 255, 0.1),
    /* 内部阴影 */ inset 0 -1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.swing-button.active {
  background-color: rgba(255, 0, 0, 0.8) !important;
  box-shadow:
    /* 激活状态的发光效果 */ 0 0 20rpx rgba(255, 0, 0, 0.5),
    /* 上方高光 */ 0 -2rpx 4rpx rgba(255, 255, 255, 0.1),
    /* 下方阴影 */ 0 4rpx 8rpx rgba(0, 0, 0, 0.4),
    /* 内部高光 */ inset 0 1rpx 2rpx rgba(255, 255, 255, 0.2),
    /* 内部阴影 */ inset 0 -1rpx 2rpx rgba(0, 0, 0, 0.3);
}
.ctrl-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 180rpx;
  font-size: 40rpx;
  font-weight: bold;
  color: white;
  background-color: rgba(128, 128, 128, 0.273);
  position: relative;
  z-index: 100;
  overflow: hidden;
  border-radius: 12rpx;
  pointer-events: auto;
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;

  /* 3D凸起效果 - 未按下状态 */
  box-shadow:
    /* 外部发光 */ 0 0 20rpx rgba(0, 255, 255, 0.3),
    /* 上方高光 */ 0 -4rpx 8rpx rgba(255, 255, 255, 0.1),
    /* 下方阴影 */ 0 6rpx 12rpx rgba(0, 0, 0, 0.4),
    /* 内部高光 */ inset 0 2rpx 4rpx rgba(255, 255, 255, 0.1),
    /* 内部阴影 */ inset 0 -2rpx 4rpx rgba(0, 0, 0, 0.2);

  border: 1rpx solid rgba(0, 255, 255, 0.6);
  transform: translateY(0rpx);

  /* 去掉过渡效果和点击效果 */
  outline: none;
  -webkit-tap-highlight-color: transparent;

  &.active {
    transform: translateY(4rpx);
    box-shadow: 0 0 15rpx rgba(0, 255, 255, 0.5),
      0 -2rpx 4rpx rgba(255, 255, 255, 0.1), 0 2rpx 6rpx rgba(0, 0, 0, 0.6),
      inset 0 1rpx 2rpx rgba(255, 255, 255, 0.1),
      inset 0 -1rpx 2rpx rgba(0, 0, 0, 0.3);
  }
}

.button-background {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

.text {
  font-size: 30rpx;
  color: white;
  font-weight: bold;
  margin-top: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  position: relative;
  z-index: 2;
}

.imagAc {
  width: 60rpx;
  height: 60rpx;
  /* 激活状态红色高亮效果 */
  filter: brightness(1.5) saturate(2) hue-rotate(-10deg) contrast(1.3);
  transition: all 0.1s ease;
  position: relative;
  z-index: 2;
}
.ctrl-btn.active {
  background-color: rgba(128, 128, 128, 0.8) !important;
  filter: brightness(1.6);
}

.imag {
  width: 60rpx;
  height: 60rpx;
  transition: all 0.1s ease;
  position: relative;
  z-index: 2;
}

.myright {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  margin-top: 20rpx;
  position: relative;
  z-index: 80;
  /* 确保容器不会被裁剪 */
  overflow: visible;
}
</style>
