<!-- src/components/PerformanceMonitor.vue -->
<template>
  <view class="performance-monitor" v-if="duration > 0 && isDevelopment">
    加载耗时：{{ duration.toFixed(2) }}ms
  </view>
</template>

<script>
export default {
  props: {
    duration: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    isDevelopment() {
      // 🔥 只在开发环境显示
      return process.env.NODE_ENV === "development"
    },
  },
  watch: {
    duration(newVal) {
      // 🔥 只在开发环境打印日志
      if (process.env.NODE_ENV === "development") {
        // console.log(`[PerformanceMonitor] 接收到新的duration值: ${newVal}`)
      }
    },
  },
  mounted() {
    // 🔥 只在开发环境打印日志
    if (process.env.NODE_ENV === "development") {
      console
        .log
        // `[PerformanceMonitor] 组件已挂载, 初始duration: ${this.duration}`
        ()
    }
  },
}
</script>

<style scoped>
.performance-monitor {
  position: fixed;
  top: 100px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;
}
</style>
