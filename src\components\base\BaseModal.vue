<template>
  <u-modal
    v-if="content != ''"
    v-model="newShow"
    :title="title"
    :title-style="titleStyle"
    :content="content"
    :show-confirm-button="isShowConfirm"
    :show-cancel-button="isShowCancel"
    :cancel-style="cancelStyle"
    :confirm-style="confirmStyle"
    :mask-close-able="mask"
    :confirm-text="confirmText"
    :cancel-text="cancelText"
    :asyncClose="asyncClose"
    :show-title="showTitle"
    @confirm="confirm"
    @cancel="cancel"

  >
  </u-modal>
  <u-modal
    v-else
    v-model="newShow"
    :title="title"
    :content="content"
    :show-confirm-button="isShowConfirm"
    :show-cancel-button="isShowCancel"
    :cancel-style="cancelStyle"
    :confirm-style="confirmStyle"
    :mask-close-able="mask"
    :title-style="titleStyle"
    :confirm-text="confirmText"
    :cancel-text="cancelText"
    :asyncClose="asyncClose"
    :show-title="showTitle"
    @confirm="confirm"
    @cancel="cancel"
    class="z-index"
  >
    <view class="slot"><slot></slot></view>
  </u-modal>
</template>

<script>
export default {
  name: "BaseModal",
  props: {
    show: { type: Boolean, default: false },
    showTitle: { type: Boolean, default: true },
    content: { type: String, default: "" },
    isShowConfirm: { type: Boolean, default: true },
    isShowCancel: { type: Boolean, default: true },
    asyncClose: { type: Boolean, default: false },
    mask: { type: Boolean, default: false },
    title: { type: String, default: "温馨提示" },
    confirmText: { type: String, default: "确认" },
    cancelText: { type: String, default: "取消" },
    titleStyle:{type:Object,default:function(){
      return {color:'#333'}
    }}
  },
  computed: {
    newShow: {
      get: function (val) {
        return this.show;
      },
      set: function (val) {
        return this.$emit("update:show", val);
      },
    },
  },
  data() {
    return {
      model: true,
      cancelStyle: {
        width: "268rpx",
        height: "88rpx",
        background: "#fff",
        borderRadius: "16rpx",
        color: "#333333",
        fontSize: "32rpx",
        margin: "30rpx 0 30rpx 20rpx",
        lineHeight: "88rpx",
        border: "2rpx solid #C8C8C8",
      },
      confirmStyle: {
        width: "268rpx",
        height: "88rpx",
        background: "linear-gradient(268deg, #206BC5 0%, #0EADE2 99%)",
        borderRadius: "16rpx",
        color: "#fff",
        fontSize: "32rpx",
        margin: "30rpx 20rpx",
        lineHeight: "88rpx",
      },
    };
  },
  methods: {
    confirm(e) {
      this.$emit("confirm");
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="scss" scoped>
.slot {
  margin: 20rpx;
  color: $textBlack;
}
.z-index{
  position:absolute;
  z-index:999999;
}
</style>
