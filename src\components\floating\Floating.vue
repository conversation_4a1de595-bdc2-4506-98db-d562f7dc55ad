<template>
    <movable-area class="movable-area" :style="{ width: '100vw', height: '100vh', zIndex: zindex }">
        <movable-view class="movable-view" :class="{ 'chunk': isChunk }"
            :style="{ width: dragWidth + 'rpx', height: dragHeight + 'rpx' }" :x="x" :y="y" direction="all">
            <view class="right_img">
                <image class="image" :src="kefuUrl" @click.stop="dragClickHandle"></image>
            </view>
        </movable-view>
    </movable-area>

</template>

<script>
export default {
    name: "floating",
    props: {
        zindex: {
            type: String,
            default: () => {
                return '9999'
            }
        },
        x: {
            type: String,
            default: () => {
                return '370'
            }
        },
        y: {
            type: String,
            default: () => {
                return '250'
            }
        },
        dragWidth:{
            type: String,
            default: () => {
                return '80'
            } 
        },
        dragHeight:{
            type: String,
            default: () => {
                return '80'
            } 
        },


    },
    data() {
        return {
            kefuUrl: require('@/static/img/vip.gif'),
            areaWidth: 200,
            areaHeight: 500,
            isChunk:false,
        };
    },
    mounted() {
        this.isChunk=this.chunk
    },
    methods: {
       
        /**
         * 点击触发事件
         * **/
        dragClickHandle(e) {
            e.preventDefault();
            this.isChunk=false
            this.$emit('onClick')
        },
    
       
    },
}
</script>

<style lang="scss" scoped>
.image {
    width: 100%;
    height: 100%;
}

.right_img {
    width: 80rpx;
    height: 80rpx;
}



.movable-area {
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 150rpx;
    left: 0;
    right: 0;
    bottom: 20%;
    pointer-events: none; //鼠标事件可以渗透
    .movable-view {
        border-radius: 50%;
        overflow: hidden;
        width: 80rpx;
        height: 80rpx;
        pointer-events: auto; //恢复鼠标事件
        // background: rgba(255, 255, 255, 0.8);
        box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.09);
        position: relative;
        display: flex;
        justify-content: space-between;
        background-color: rgb(255, 255, 255);

    }

   

}
</style>
