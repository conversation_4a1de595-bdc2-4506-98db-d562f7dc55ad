<template>
  <view class="page">
    <BaseNavbar :title="title" />

    <view class="container">
      <!-- 当前设备信息 -->
      <view class="device-card">
        <view class="card-header">
          <text class="card-title">当前设备信息</text>
        </view>
        <view class="card-content">
          <view class="info-row">
            <text class="label">设备码：</text>
            <text class="value">{{
              deviceInfo.device_sn || deviceSn || "--"
            }}</text>
          </view>
          <view class="system-code-row">
            <text class="label">系统码：</text>
            <view class="input-with-scan">
              <BaseInput
                v-model="deviceInfo.system_code"
                placeholder="请输入系统码"
                :clearable="true"
                class="system-code-input"
              />
              <!-- 点击扫一扫显示出设备码 -->
              <BaseIcon
                name="scan"
                class="scan"
                color="#999"
                @onClick="onClickScanSystemCode"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 新设备码输入 -->
      <view class="form-card">
        <view class="card-header">
          <text class="card-title">新设备码</text>
        </view>
        <view class="card-content">
          <view class="form-item">
            <view class="input-with-button">
              <BaseInput
                v-model="newDeviceCode"
                placeholder="请输入新设备码"
                :clearable="true"
                class="input-field"
              />
              <button class="list-btn" @click="goToMainboardList">
                主板列表
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button
          class="main-btn"
          @click="handleConfirm"
          :disabled="isSubmitting"
        >
          {{ isSubmitting ? "提交中..." : "更换主板" }}
        </button>
      </view>
    </view>

    <!-- 确认弹窗 -->
    <BaseModal
      :show.sync="showConfirmModal"
      title="确认更换设备主板"
      :content="confirmMessage"
      @confirm="submitDeviceCodeChange"
      @cancel="showConfirmModal = false"
    />
  </view>
</template>

<script>
import BaseNavbar from "@/components/base/BaseNavbar.vue"
import BaseInput from "@/components/base/BaseInput.vue"
import BaseModal from "@/components/base/BaseModal.vue"
import BaseSearch from "@/components/base/BaseSearch.vue"
import BaseIcon from "../../components/base/BaseIcon.vue"

export default {
  name: "NewMainboard",
  components: {
    BaseNavbar,
    BaseInput,
    BaseModal,
    BaseSearch,
    BaseIcon,
  },
  data() {
    return {
      title: "更换主板",
      deviceSn: "",
      deviceInfo: {},
      newDeviceCode: "",
      isSubmitting: false,
      showConfirmModal: false,
    }
  },
  computed: {
    confirmMessage() {
      return `确认将设备 ${this.deviceSn} 的设备码更换为 ${this.newDeviceCode}？`
    },
  },
  methods: {
    // 重置页面数据
    resetPageData() {
      this.deviceSn = ""
      this.deviceInfo = {
        system_code: "", // 🔥 确保系统码初始为空
      }
      this.newDeviceCode = ""
      this.isSubmitting = false
      this.showConfirmModal = false

      // console.log("页面数据已重置")
    },

    // 设置设备信息（仅使用路由参数）
    setDeviceInfo() {
      this.deviceInfo = {
        device_sn: this.deviceSn,
        system_code: "", // 🔥 改为空字符串，让输入框一开始为空
      }
      // console.log("设备信息:", this.deviceInfo)
    },

    // 表单验证
    validateForm() {
      if (!this.newDeviceCode.trim()) {
        this.isShowErr("请输入新设备码")
        return false
      }
      return true
    },

    // 处理确认
    handleConfirm() {
      if (!this.validateForm()) {
        return
      }
      this.showConfirmModal = true
    },

    // 跳转到主板列表
    goToMainboardList() {
      uni.navigateTo({
        url: "/pagesD/mainboard/MainboardList",
      })
    },

    // 扫码识别系统码
    onClickScanSystemCode() {
      uni.scanCode({
        success: (res) => {
          // console.log("扫码系统码结果:", res.result)

          // 🔥 从URL中提取vscode参数
          const url = res.result
          const vscode = this.extractVcodeFromUrl(url)

          if (vscode) {
            this.deviceInfo.system_code = vscode
            console.log("提取的系统码:", vscode)
          } else {
            // 如果提取失败，直接使用扫码结果
            this.deviceInfo.system_code = res.result
          }
        },
        fail: (err) => {
          // console.error("扫码失败:", err)
          this.isShowErr("扫码失败，请重试")
        },
      })
    },

    // 从URL中提取vscode参数
    extractVcodeFromUrl(url) {
      try {
        // 使用正则表达式提取vscode参数
        const match = url.match(/[?&]vscode=([^&]*)/i)
        if (match && match[1]) {
          return match[1]
        }

        // 如果正则匹配失败，尝试使用URL对象解析
        const urlObj = new URL(url)
        return urlObj.searchParams.get("vscode")
      } catch (error) {
        // console.error("解析URL失败:", error)
        return null
      }
    },

    // 提交更换主板
    async submitDeviceCodeChange() {
      this.isSubmitting = true
      this.showConfirmModal = false

      try {
        const data = {
          device_sn: this.deviceSn,
          vsCode: this.deviceInfo.system_code,
          new_device_sn: this.newDeviceCode,
        }

        // console.log("提交更换主板数据:", data)

        // 🔥 调用更换主板接口
        const res = await this.$u.api.SetMotherboards(data)
        // console.log("更换主板响应:", res)

        this.isShowSuccess("主板更换成功", 1, () => {
          // 🔥 返回前清理数据
          this.resetPageData()
        })
      } catch (error) {
        // console.error("更换主板失败:", error)
        this.isShowErr(error.msg || "更换主板失败，请重试")
      } finally {
        this.isSubmitting = false
      }
    },
  },

  onLoad(options) {
    // console.log("NewMainboard onLoad options:", options)
    // 🔥 重置页面数据
    this.resetPageData()

    if (options.data) {
      // 🔥 新的参数格式：通过 data 参数传递完整设备信息
      try {
        const deviceData = JSON.parse(decodeURIComponent(options.data))
        // console.log("解析的设备数据:", deviceData)

        this.deviceSn = deviceData.device_sn
        this.deviceInfo = {
          device_sn: deviceData.device_sn,
          system_code: deviceData.system_code || "", // 🔥 使用传递过来的系统码
        }
        // console.log("最终设备信息:", this.deviceInfo)
        // console.log("设备编号:", this.deviceSn)
      } catch (error) {
        // console.error("解析设备数据失败:", error)
        this.isShowErr("设备数据格式错误")
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    } else if (options.device_sn) {
      // 🔥 兼容旧的参数格式
      this.deviceSn = options.device_sn
      this.deviceInfo = {
        device_sn: options.device_sn,
        system_code: "", // 旧格式没有系统码，保持为空
      }
    } else {
      this.isShowErr("缺少设备编号参数")
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },

  onShow() {
    // 🔥 页面显示时重置状态
    this.isSubmitting = false
    this.showConfirmModal = false
  },

  onUnload() {
    // 🔥 页面卸载时清理
    this.resetPageData()
  },
}
</script>

<style lang="scss" scoped>
.page {
  background-color: $pageBgColor;
  min-height: 100vh;
}

.container {
  padding: 30rpx;
}

.device-card,
.form-card {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-content {
  padding: 30rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.form-item {
  margin-bottom: 30rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

/* 限制系统码输入框宽度 */
.system-code-input {
  width: 100% !important;
}

/* 确保扫码图标显示 */
.system-code-input ::v-deep .u-search {
  width: 100% !important;
}

.system-code-input ::v-deep .u-search__content {
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
}

.system-code-input ::v-deep .u-search__input-box {
  flex: 1 !important;
}

.system-code-input ::v-deep .u-search__action {
  display: flex !important;
  margin-left: 10rpx !important;
}

/* 系统码一行显示 */
.system-code-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.system-code-row .label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

/* 输入框容器限制宽度 */
.input-container {
  flex: 1;
  max-width: 400rpx;
  overflow: hidden;
}

.input-with-button {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.input-field {
  flex: 1;
}

.list-btn {
  width: 160rpx;
  height: 70rpx;
  background: #f8f9fa;
  color: #007aff;
  border: 2rpx solid #007aff;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:active {
    background: #007aff;
    color: #fff;
  }
}

/* 输入框和扫码按钮容器 */
.input-with-scan {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
  max-width: 400rpx;
}

/* 系统码输入框 */
.system-code-input {
  flex: 1;
  max-width: 300rpx;
}

/* 扫码占位符 */
.scan-placeholder {
  flex: 1;
  max-width: 300rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  background: #f8f9fa;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  color: #999;
  font-size: 26rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    background: #e8e8e8;
    border-color: #007aff;
    color: #007aff;
  }
}

/* 扫码按钮 */
.scan-btn {
  width: 100rpx;
  height: 60rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &:active {
    background: #0056cc;
  }
}

.action-buttons {
  display: flex;
  margin-top: 60rpx;
}

.main-btn {
  width: 95%;
  height: 88rpx;
  border-radius: 10rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  background: #007aff;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:active {
    background: #0056cc;
  }

  &:disabled {
    background: #ccc;
    color: #999;
    opacity: 0.6;
  }
}
</style>
