<template>
    <view class='card'>
        <view class="content">
            <block v-if="cardType === 'user'">
                <view class="content-box device-code">
                    <view class="title">{{ vPointName }}：</view>
                    <view class="txt">{{ info.hotelName || '暂无' }}</view>
                </view>
                <view class="content-box device-code">
                    <view class="title">设备数量：</view>
                    <view class="txt">{{ info.nums }}台</view>
                </view>
                <view class="content-box device-code">
                    <view class="title">订单总数：</view>
                    <view class="txt">{{ info.order_count || 0 }}单</view>
                </view>
                <view class="content-box device-code" v-if="false">
                    <view class="title">{{ vPointName }}地址：</view>
                    <view class="txt">{{ info.address || '暂无' }}</view>
                </view>
            </block>
            <block v-else-if="cardType === 'hotel'">
                <view class="content-box device-code">
                    <view class="title">设备编号：</view>
                    <view class="txt">{{ info.device_sn || '暂无' }}</view>
                </view>
                <view class="content-box device-code">
                    <view class="title">设备类型：</view>
                    <view class="txt">{{ info.product_name || '暂无' }}</view>
                </view>
                <view class="content-box device-code">
                    <view class="title">备注：</view>
                    <view class="txt">{{ info.room_num || '' }}</view>
                </view>
                <view class="content-box device-code">
                    <view class="title">订单总数：</view>
                    <view class="txt">{{ info.order_count || 0 }}单</view>
                </view>
            </block>
            <block v-else-if="cardType === 'manage'">
                <view class="content-box device-code">
                    <view class="title">账号：</view>
                    <view class="txt">{{ info.user_login }}({{ info.roleName }})</view>
                </view>
                <view class="content-box device-code">
                    <view class="title">设备数量：</view>
                    <view class="txt">{{ info.nums }}台</view>
                </view>
                <view class="content-box device-code" v-if="false">
                    <view class="title">{{ vPointName }}地址：</view>
                    <view class="txt">{{ info.address || '暂无' }}</view>
                </view>
            </block>
            <view class="content-box profit ">
                <view class="profit-box device-code">
                    <view class="title">{{ cardType === 'user' ? vPointName : '设备' }}销售总额：</view>
                    <view class="txt">{{ info.all_amount }} 元</view>
                </view>
                <view class="profit-details" @click="goDetails">明细</view>
                <!-- <view class="profit-details" v-if="cardType !== 'user'" @click="goDetails">明细</view> -->
            </view>
            <!-- <view class="content-box profit "  v-if="cardType === 'user'">
                <view class="profit-box device-code" >
                    <view class="title">本人收益：</view>
                    <view class="txt">{{ info.all_divide||'0.00' }} 元</view>
                </view>
                <view class="profit-details"  @click="goDetails">明细</view>
                
            </view> -->
        </view>
    </view>
</template>
<script>
export default {
    props: {
        info: { type: Object, default: {} },
        cardType: { type: String, default: 'user' },
        index: { type: [Number, String], default: 0 },
        linkmanDianwei: { type: String, default: '' },
        endDate: { type: String, default: '' },
        startDate: { type: String, default: '' },
    },
    data() {
        return {
        };
    },

    methods: {
        goDetails() {
            let param = '';
            if (this.cardType == 'user') {

                param = `?cardType=hotel&hotel_id=${this.info.hotel_id}&index=${this.index}&userName=${this.info.hotelName}&linkmanDianwei=${this.linkmanDianwei}`
            } else if (this.cardType === 'hotel') {
                param = `?cardType=device&device_sn=${this.info.device_sn}&index=${this.index}&userName=${this.info.device_sn}&linkmanDianwei=${this.linkmanDianwei}`
            } else if (this.cardType === 'manage') {
                param = `?cardType=user&uid=${this.info.id}&userName=${this.info.user_login}&linkmanDianwei=${this.linkmanDianwei}`
            }
            if (this.index == 5) {
                param = param + `&startDate=${this.startDate}&endDate=${this.endDate}`
            }
            uni.navigateTo({ url: `/pagesB/dataAnalysis/DataAnalysis${param}` })
        }
    },
}
</script>


<style scoped lang='scss'>
.card {
    padding: 20rpx;
}

.device-code {
  /* 父容器保持 flex 布局 */
  display: flex;
  align-items: center;

  > view:nth-child(1) {
    width: 220rpx;
    text-align: justify;
    height: 40rpx; /* 固定高度 */
    line-height: 40rpx; /* 与高度相同 */
    overflow: hidden; /* 防止溢出 */

    /* 优化后的伪元素方案 */
    &::after {
      content: '';
      display: inline-block;
      width: 100%;
      height: 0; /* 消除高度影响 */
      vertical-align: top; /* 对齐顶部 */
    }

  }

  > view:nth-child(2) {
    width: 360rpx;
  }
}

.content {
    &-box {
        display: flex;
        font-size: $font-size-middle;

        .title {
            color: $textDarkGray;
            flex-shrink: 0;
        }

        .txt {
            color: $textBlack;
        }
    }

    .profit {
        justify-content: space-between;
        display: flex;

        &-box {
            display: flex;
        }

        &-details {
            color: $themeComColor;
            font-weight: 700;
        }
    }
}
</style>